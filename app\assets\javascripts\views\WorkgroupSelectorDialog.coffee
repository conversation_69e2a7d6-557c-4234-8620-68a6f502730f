#=require "collections/WorkgroupCollection"
#=require "views/WorkgroupSelectorItem"
#=require "views/SearchDialog"

App.WorkgroupSelectorDialog = App.SearchDialog.extend
  ###
  A dialog that allows a user to search for and select a user. Used to
  let the user assign someone to a process.
  ###

  #
  # Marionette.CollectionView options
  #

  childView: App.WorkgroupSelectorItem

  #
  # App.SearchDialog options
  #

  title: 'Externe Arbeitsgruppe auswählen'
  searchLabel: 'Name der Arbeitsgruppe'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    App.SearchDialog::initialize.call(this)
    @collection = new App.WorkgroupCollection()
