# Executed when a planner adds another planner to an assignment.
class AddPlannerAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to add a planner to.
  # user       - The user to add as planner.
  def initialize(assignment, user)
    super(assignment)
    @user = user
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    AssignmentMembership.ensure_planner(@user, subject)
    subject.touch
    notify(:info_added_planner, subject, @user)
    notify(:added_planner, subject, @user)
  end
end
