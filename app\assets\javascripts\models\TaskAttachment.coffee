#=require "models/Model"
#=require "models/User"

App.TaskAttachment = App.Model.extend
  ###
  Client-side representation of a TaskAttachment.
  (See app/models/task_attachment.rb)
  ###

  #
  # Backbone.Model options
  #

  defaults:
    filename: ''
    content_type: 'application/octet-stream'
    created_by_id: ''

  #
  # App.Model options
  #

  jsonSingularRoot: 'task_attachment'
  jsonPluralRoot: 'task_attachments'

  #
  # Methods
  #

  urlRoot: ->
    taskId = @get('task').id
    "/api/task_attachments/#{taskId}"

  url: ->
    ###
    See Backbone.Model#url
    ###
    filename = @get('filename')
    "#{@urlRoot()}/#{encodeURIComponent(filename)}"

  upload: (file) ->
    ###
    Upload `file` as content of the atttachment.
    ###
    @_sendUploadRequest(file).success (data) =>
      @set(data.task_attachment)
      @trigger('sync', this)

  _sendUploadRequest: (file) ->
    data = new FormData()
    data.append('file', file)
    $.ajax
      type: 'POST'
      url: @urlRoot()
      data: data
      processData: false
      contentType: false
