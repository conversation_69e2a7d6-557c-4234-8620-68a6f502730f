class Admin::MachinesController < Admin::AdminController
  before_action :require_permission
  before_action :require_index_permission, only: [:index]
  before_action :require_create_permission, only: [:new, :create]
  before_action :require_machine_permission, only: [:edit, :update]

  # GET /admin/machines
  def index
    if current_user.can_edit_machines?
      @machines = Machine.where("responsible_id != ? and (deputy_id != ? OR deputy_id IS NULL)", current_user.id, current_user.id).order(:name)
    end
    @responsible_machines = current_user.responsible_machines.order(:name)
    @deputy_machines = current_user.deputy_machines.order(:name)
  end

  # GET /machines/1
  def show
    @machine = Machine.find(params[:id])
    @tasks = @machine.active_tasks
  end

  # GET /admin/machines/new
  # TODO move out of admin space
  def new
    @machine = Machine.new
    @machine.responsible = current_user
    @machine.build_instructed_users_group
    @users = User.active.order(:username)
    @workgroups = Workgroup.order(:name)
    @locations = Location.all
    render :edit
  end

  # GET /admin/machines/1/edit
  def edit
    @machine = Machine.find(params[:id])
    @users = User.active.order(:username)
    @workgroups = Workgroup.order(:name)
    @locations = Location.all
    render :edit
  end

  # POST /admin/machines
  def create
    @users = User.active.order(:username)
    @workgroups = Workgroup.order(:name)
    @locations = Location.all

    @machine = Machine.new(permitted_params)
    @machine.build_instructed_users_group(name: "#{@machine.name} (Zugelassen)", machine_id: @machine.id, user_ids: permitted_params_instructed_users_group[:user_ids])

    if @machine.save
      flash[:edit_notice] = "Die Maschine wurde erstellt."
      redirect_to action: :index
      # redirect_to action: :show, notice: 'Machine was successfully created.'
    else
      render :edit
    end
  end

  # PATCH/PUT /admin/machines/1
  def update
    @machine = Machine.find(params[:id])

    if @machine.update(permitted_params) && @machine.instructed_users_group.update(permitted_params_instructed_users_group)
      if permitted_params.has_key?(:name)
        @machine.instructed_users_group.update(name: "#{permitted_params[:name]} (Zugelassen)")
        workgroup_groups = OperatorGroup.where(machine: @machine).where.not(workgroup_id: nil)
        workgroup_groups.each do |group|
          if group.workgroup.present?
            group.update!(name: "#{permitted_params[:name]} (#{group.workgroup.name})")
          end
        end
      end
      @machine.update_corresponding_groups
      flash[:edit_notice] = "Die Maschine wurde gespeichert."
      redirect_to action: :index
    else
      flash[:edit_notice] = "Beim Speichern der Maschine ist ein Fehler aufgetreten."
      redirect_to action: :index
    end
  end

  private

    def require_permission
      return if current_user
      redirect_to root_path
    end

    def require_index_permission
      return if current_user && (current_user.can_edit_machines? || current_user.responsible_machines.exists? || current_user.deputy_machines.exists?)
      redirect_to root_path
    end

    def require_create_permission
      return if current_user && current_user.can_edit_machines?
      redirect_to root_path
    end

    def require_machine_permission
      machine = Machine.find(params[:id])
      return if current_user && (current_user.can_edit_machines? || current_user.responsible_machines.exists?(machine.id) || current_user.deputy_machines.exists?(machine.id))
      redirect_to root_path
    end

    # Only allow a trusted parameter "white list" through.
    def permitted_params
      params.require(:machine).permit(:name, :location_id, :state, :tel, :inv_number, :responsible_id, :deputy_id)
    end

    # Only allow a trusted parameter "white list" through.
    def permitted_params_instructed_users_group
      params.require(:instructed_users_group).permit(:user_ids => [])
    end
end
