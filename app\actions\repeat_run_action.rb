# Executed when a planner repeats the latest successful of an assignment.
class RepeatRunAction < Action
  # Initialize the action.
  #
  # run - The run to cancel.
  def initialize(run)
    super(run)
  end

  def allowed?(user)
    subject.repeatable_by_user?(user)
  end

  def do_execute!
    new_run = create_new_draft_run

    copy_attachments_previous_run if Dir.exist?(Pathname.new(Rails.configuration.pms_attachments_root) + "Auftrag #{subject.assignment.id}" + "Durchlauf #{subject.version}")

    mark_changed(new_run)
    mark_changed(subject.assignment.reload) # now a draft again
  end

  private

  def copy_attachments_previous_run
    dest = Pathname.new(Rails.configuration.pms_attachments_root)
    dest += "Auftrag #{subject.assignment.id}"
    dest += "Durchlauf #{subject.version + 1}"
    FileUtils.mkdir_p(dest.dirname)
    src = Pathname.new(Rails.configuration.pms_attachments_root)
    src += "Auftrag #{subject.assignment.id}"
    src += "Durchlauf #{subject.version}"
    FileUtils.cp_r src, dest
  end

  def create_new_draft_run
    new_run = subject.copy
    new_run.update!(completed_at: nil)
    new_run.save!
    new_run.processes.each do |process|
      process.tasks.each do |task|
        task.update!(completion_quality: 0)
        task.attachments.where(sticky: false).destroy_all
      end
    end
    subject.assignment.reopen_after_repeat!
    new_run
  end
end