# Executed when a user assigns a article to a task
class AssignArticleAction < Action
  # Initialize an AssignArticleAction.
  #
  # task     - The task to assign.
  # article  - The article to assign to.
  def initialize(task, article)
    super(task)
    @article = article
  end

  # See Action.
  def allowed?(user)
    subject.reassignable_by_user?(user)
  end

  # TODO get real entity
  # See Action.
  def do_execute!
    subject.articles << @article
    subject.save!
    subject.articles.last
  end
  
end
