# Executed when a planner completely cancels an assignment.
class CancelAssignmentAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to cancel.
  def initialize(assignment)
    super(assignment)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    current_run = subject.runs.last
    current_run.update!(state: "failed")
    current_run.update!(completed_at: Time.now)
    mark_changed(current_run)

    # notify_after_undo_completion_timeout
    notify(:assignment_failed, subject)
    notify(:assignment_failed_observer, subject)    
      
    handle_now_failed_tasks
  end

  private

  def handle_now_failed_tasks
    subject.runs.last.tasks.where(completed: false).each do |failed_task|
      # notify_after_undo_completion_timeout
      case failed_task.operator
      when User
        notify(:assignment_with_task_failed, subject, failed_task)
      when OperatorGroup
        # TODO needs implementation
      end
    end
  end

  # def notify_after_undo_completion_timeout
  #   timeout = Rails.configuration.pms_undo_complete_timeout
  #   NotificationMailer.delay(run_at: timeout.from_now)
  # end
end
