#=require "collections/MachineSearchResultCollection"
#=require "views/MachineSearchResultItem"
#=require "views/SearchDialog"

App.MachineSearchDialog = App.SearchDialog.extend
  ###
  A dialog that allows a user to search for and 
  assign a machine to a task.
  ###

  #
  # Marionette.CollectionView options
  #

  childView: App.MachineSearchResultItem

  #
  # App.SearchDialog options
  #

  title: '<PERSON><PERSON><PERSON>'
  searchLabel: 'Maschinenname'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    App.SearchDialog::initialize.call(this)
    @collection = new App.MachineSearchResultCollection()
