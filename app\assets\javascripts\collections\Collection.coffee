#=require "models/Model"

App.Collection = Backbone.Collection.extend
  ###
  The base class of all collection classes. It is adapted to the JSON
  serialization format of the backend (which stores the list of models
  inside a root element).
  ###

  parse: (response) ->
    ###
    Override of Backbone.Collection#parse. Gets the model data from
    the model-type-specific plural root (`jsonPluralRoot`) and
    extracts side-loaded models with App.Model.extractRelatedModels.
    ###
    jsonRoot = @jsonRoot ? @model::jsonPluralRoot
    data = response[jsonRoot]
    delete response[jsonRoot]
    App.Model.extractRelatedModels(response)
    return data

  create: (attrs, options={}) ->
    ###
    Override of Backbone.Collection#create. Correctly implements the
    `wait` option for Backbone.RelationalModel-derived model types.
    ###
    options.wait ?= true
    if options.wait
      model = new @model(attrs, collection: this)
      @add(model, silent: true) # set up the relation
      model.save().done =>
        @trigger('add', model, this)
        @trigger('sync', this)
    else
      Backbone.Collection::create.call(this, attributes, options)
