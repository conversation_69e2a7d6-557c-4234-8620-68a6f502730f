App.MachineAttachmentView = Marionette.ItemView.extend
  ###
  Displays a link to an attachment.
  ###

  #
  # Backbone.View options
  #

  className: 'list-group-item pms-attachment'
  events:
    'click .pms-attachment-delete-btn': '_deleteAttachment'

  #
  # Marionette.ItemView options
  #

  template: JST['MachineAttachmentView']

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @_deleting = false
    @listenTo(@model, 'sync', @render)

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      uploading: @model.isNew()
      deleting: @_deleting
      editable_by_user: @model.get('machine').get('editable_by_user')
      url: @model.url()

  #
  # Event handlers
  #

  _deleteAttachment: ->
    @model.destroy(wait: true)
    @_deleting = true
    @render()
