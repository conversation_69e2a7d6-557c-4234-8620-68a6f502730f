// Basic styles for the complete app.

body {
  background-color: $pms-body-background;
}

// https://docs.angularjs.org/api/ng/directive/ngCloak
[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak],
.ng-cloak, .x-ng-cloak {
  display: none !important;
}

.prio-1 {
  border-left: 6px solid #dddddd !important;
}

.prio-2 {
  border-left: 6px solid #A6A6A6 !important;
}

.prio-3 {
  border-left: 6px solid #35B200 !important;
}

.prio-4 {
  border-left: 6px solid $brand-warning !important;
}

.prio-5 {
  border-left: 6px solid #d9534f !important;
}


.prio-color-1 {
  background-color: #dddddd;
}

.prio-color-2 {
  background-color:  #A6A6A6;
}

.prio-color-3 {
  background-color:  #35B200;
}

.prio-color-4 {
  background-color:  $brand-warning;
}

.prio-color-5 {
  background-color:  #d9534f;
}
