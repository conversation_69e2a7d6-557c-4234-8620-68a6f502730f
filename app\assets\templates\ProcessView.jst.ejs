<h3 class="pms-process-header" role="button">
  <span class="pms-process-number"><%- index %></span>
  <span class="pms-process-name"><%- name %></span>
  <i class="fas fa-pause pms-pause-icon pms-process-tag-pause"
     title="Hier wurde eine Pause vorgesehen">
  </i>
  <i class="fas fa-flag pms-immediate-work-icon pms-process-tag-immediate"
     title="Sie sind hier einer aktuellen Aufgabe zugewiesen">
  </i>
  <i class="fas fa-flag pms-near-work-icon pms-process-tag-near"
     title="Sie sind hier einer folgenden Aufgabe zugewiesen">
  </i>
  <i class="fas fa-flag pms-work-icon pms-process-tag-assigned"
     title="Sie sind hier einer Aufgabe zugewiesen">
  </i>
  <% if (mightBeOutdated) { %>
    <i class="fas fa-info fa-code-compare pms-process-outdated-icon"
       title="Dieser Prozess könnte veraltet sein"
       data-toggle="popover"
       data-placement="bottom"
       data-html="true"
       data-content="<%= outdatedPopoverContent() %>">
    </i>
  <% } %>
  <% if (overdue) { %>
    <i class="fas fa-regular fa-exclamation-triangle pms-process-overdue-icon"
       title="Dieser Prozess ist überfällig">
    </i>
  <% } %>
  <% if (due && !overdue) { %>
    <i class="fas fa-regular fa-clock pms-process-due-icon"
        title="Es gibt hier ein Fälligkeitsdatum">
    </i>
  <% } %>

  <div class="hidden-xs pms-process-buttons">

    <% if (child_creatable) { %>
      <button class="btn btn-sm btn-default pms-process-options-btn"
              data-toggle="popover"
              data-placement="left"
              data-html="true"
              data-content="<%= processPopoverContent() %>">
        <i class="fas fa-chevron-down"></i>
      </button>

      <button class="btn btn-sm btn-default pms-process-add-child-btn"
              title="<%= (hasChildren)
                         ? 'Zusätzlichen Nachfolger-Prozess anlegen'
                         : 'Nachfolger-Prozess anlegen' %>">
        <i class=" fas fa-plus"></i>
        <% if (hasChildren) { %>
          <i class="fas fa-level-down-alt"></i>
        <% } else { %>
          <i class="fas fa-long-arrow-alt-down"></i>
        <% } %>
      </button>

    <% } %>

    <span class="pms-task-attachments-icon" title="Anhänge vorhanden">
      <i class=" fas fa-paperclip"></i>
    </span>
  </div>

</h3>

<div class="collapse <%= expand ? 'in' : '' %> pms-process-body"
     id="pms-process-body-<%= id %>">

  <dl class="row pms-process-details pms-details-list">
    <dt class="pms-process-due-heading">Fällig am:</dt>
    <dd>
      <span class="pms-process-due">
        <%= prettyDate(due) %>
      </span>
    </dd>
    <% if (instruction) {%>
      <dt class="pms-process-due-heading">Instruktion: </dt>
      <dd>
        <span class="pms-process-instruction pms-multi-line"><%=
          valueOrPlaceholder(instruction)
        %></span>
      </dd>
    <% } %>
  </dl>

  <div class="pms-tasks">
    <% /* TaskView(s) */ %>
  </div>

<% if (editable) { %>
  <form class="hidden-xs form-inline pms-new-task-form">
    <div class="form-group">
      <label class="sr-only" for="pms-new-task-name-input-<%= id %>">
        Aufgabenname
      </label>
      <input type="text"
             class="form-control pms-new-task-name-input"
             id="pms-new-task-name-input-<%= id %>"
             placeholder="Neue Aufgabe">
    </div>
    <button class="btn btn-default pms-new-task-btn" disabled>
      <i class="fas fa-plus"></i>
      Aufgabe hinzufügen
    </button>
  </form>
<% } %>
</div>
