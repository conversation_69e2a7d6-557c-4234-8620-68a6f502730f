#=require "./_backbone"
#=require "views/NotificationBar"
#=require "views/MachinePage"

App.addInitializer ->
  App.notificationsRegion.show(new App.NotificationBar())

App.addInitializer ->
  App.machine = new App.Machine(App.machine, parse: true)
  App.user = new App.User(App.user, parse: true)
  pageEl = $('.pms-machine-page-container').get()
  page = new App.MachinePage(el: pageEl, model: App.machine)
  page.render()
  