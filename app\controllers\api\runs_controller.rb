# Provides an API for creating and manipulating runs.
class Api::RunsController < ApplicationController
  # Activate, pause or cancel a run.
  #
  # id                - The run's ID.
  # state             - The desired state for the run. Passing "active"
  #                     activates the run if it is in the "draft" or
  #                     "paused" state. Passing "paused" pauses an active
  #                     run, and "failed" cancels it.
  # repeat_run        - When the last completed run should be repeated
  #
  # Returns the updated run.
  # Raises "403 Forbidden" if the operation is not allowed
  #   for the requesting user.
  def update
    run = Run.find(params[:id])
    state_change = [run.state, params[:state]]

    if params[:repeat_run] && repeat_run_allowed?(run)
      repeat_run(run)
      return
    end

    unless state_change_allowed?(run, state_change)
      render status: 403, nothing: true
      return
    end

    case state_change
    when %w(draft active), %w(paused active)
      activate_run(run)
    when %w(active paused)
      pause_run(run)
    when %w(active failed), %w(paused failed)
      cancel_run(run)
    else
      render status: :bad_request, nothing: true
    end
  end

  private

  def state_change_allowed?(run, change)
    if change[1] == "active"
      run.activatable_by_user?(current_user)
    else
      run.editable_by_user?(current_user)
    end
  end

  def repeat_run_allowed?(run)
    run.repeatable_by_user?(current_user)
  end

  def activate_run(run)
    action = ActivateRunAction.new(run)
    execute_action_and_respond(action)
  end

  def pause_run(run)
    action = PauseRunAction.new(run)
    execute_action_and_respond(action)
  end

  def cancel_run(run)
    action = CancelRunAction.new(run)
    execute_action_and_respond(action)
  end

  def repeat_run(run)
    action = RepeatRunAction.new(run)
    execute_action_and_respond(action)
  end
end
