# Provides a JSON API for creating, updating and deleting tasks.
class Api::TasksController < ApplicationController
  # Create a new task.
  #
  # task - Task attributes (all mandatory).
  #        :name       - The task's name.
  #        :process_id - The process to add the task to. The task is
  #                      automatically added to the end of the process.
  #
  # Returns the new task as JSON.
  def create
    process = PmsProcess.find(params[:task][:process_id])
    action = CreateTaskAction.new(process, params[:task][:name], current_user)
    execute_create_action_and_respond(action)
  end

  ##
  # Manipulate a task and return it, as well as all other models
  # whose state is affected by the change (PATCH /tasks/:id).

  # Update an existing task.
  #
  # id   - The task's ID.
  # task - Task attributes (all optional).
  #        :completed   - If true, mark the task as completed.
  #        :instruction - The task's new instruction.
  #        :name        - The task's new name.
  #        :note        - The task's new note.
  #        :operator_id - The ID of the task's new operator.
  #        :position    - The position to move the task to within the
  #                       process (one-based index). Other tasks are
  #                       moved as needed.
  #
  # Returns the new task, as well as any other records which changed,
  #   as JSON.
  def update
    task = Task.find(params[:id])
    action = new_update_action(task, permitted_update_params)
    execute_action_and_respond(action)
  end

  # Delete a task.
  #
  # id - The task's ID.
  #
  # Returns all other records affected by the change.
  def destroy
    task = Task.find(params[:id])
    action = DeleteTaskAction.new(task)
    execute_action_and_respond(action)
  end

  private

  def permitted_update_params
    params
      .require(:task)
      .permit(:completed,
              :completion_quality,
              :instruction,
              :name,
              :note,
              :position,
              :machine,
              :remove_machine,
              :article,
              :remove_article,
              operator: [:id, :type])
  end

  # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
  def new_update_action(task, params)
    if params[:completed] == true
      CompleteTaskAction.new(task, current_user)
    elsif params[:completed] == false
      UndoTaskCompletionAction.new(task)
    elsif params[:completion_quality]
      UpdateCompletionQualityAction.new(task, params[:completion_quality])
    elsif params[:instruction]
      UpdateAttributeAction.new(task, :instruction, params[:instruction])
    elsif params[:name]
      UpdateAttributeAction.new(task, :name, params[:name])
    elsif params[:note]
      SetTaskNoteAction.new(task, params[:note])
    elsif params[:operator]
      case params[:operator][:type]
      when "user" then operator_class = User
      when "operator_group" then operator_class = OperatorGroup
      end
      AssignTaskAction.new(task, operator_class.find(params[:operator][:id]), current_user)
    elsif params[:machine]
      AssignMachineAction.new(task, Machine.find(params[:machine]))
    elsif params[:remove_machine]
      UnassignMachineAction.new(task, Machine.find(params[:remove_machine]))
    elsif params[:article]
      AssignArticleAction.new(task, Article.find(params[:article]))
    elsif params[:remove_article]
      UnassignArticleAction.new(task, ArticleTask.find(params[:remove_article]))
    elsif params[:position]
      MoveTaskAction.new(task, params[:position].to_i)
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

  def permitted_create_params
    params
      .require(:task)
      .permit(:name, :process_id, operator: [:id, :type])
  end
end
