#=require "models/TaskTemplate"
#=require "views/behaviors/Editable"
#=require "views/support/TemplateHelpers"
#=require "views/OperatorSearchDialog"
#=require "views/MachineSearchDialog"
#=require "views/MachineTaskView"
#=require "views/MachineView"
#=require "views/MachineTaskTemplateHolder"
#=require "views/ArticleTaskTemplateHolder"
#=require "views/ArticleTaskTemplateView"
#=require "views/ArticleSearchDialog"

App.TaskTemplateView = Marionette.LayoutView.extend
  ###
  Displays a task template in the admin area.
  ###

  #
  # Backbone.Model optins
  #

  className: 'pms-task pms-future'

  events:
    'click .pms-task-delete-btn': '_removeTask'
    'click .pms-task-assign-btn': '_showAssignDialog'
    # 'click .pms-task-add-material-btn': '_addMaterial'
    # 'click .pms-task-add-machine-btn': '_showAddMachineDialog'
    # 'click .pms-task-machine-delete-btn': '_removeMachine'
    'click .pms-task-add-article-btn': '_showAddArticleDialog'
    'click .pms-task-add-machine-btn': '_showAddMachineDialog'

  #
  # Marionette.View options
  #

  ui:
    nameInput: '.pms-task-name-input'
    instructionInput: '.pms-task-instruction-input'
    addUtilityButton: '.pms-task-add-utility-btn'

  behaviors:
    Editable:
      name:
        type: 'string'
        target: '.pms-task-name'
        inputClass: 'form-control pms-task-name-input'
      instruction:
        type: 'text'
        target: '.pms-task-instruction'
        inputClass: 'form-control pms-task-instruction-input'

  templateHelpers:
    valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
    prettyUser: App.TemplateHelpers.prettyUser
    prettyOperatorGroup: App.TemplateHelpers.prettyOperatorGroup
    promptAddMachineOrMaterial: ->
        _.template("""
          <div class='pms-task-add-machine-or-material'>
            <div>
              <button class='btn btn-sm btn-default pms-task-add-article-btn'>
                <i class='fas fa-cube' title='Material'></i> Material
              </button>
              <button class='btn btn-sm btn-default pms-task-add-machine-btn'>
                <i class='fas fa-cog' title='Maschine'></i> Maschine
              </button>
            </div>
          </div>
        """)(this)

  #
  # Marionette.ItemView options
  #

  template: JST['TaskTemplateView']

  modelEvents:
    'change:operator' : 'render'
    'change:id': 'render'
    # 'change:machines' : 'render'

  #
  # Marionette.CollectionView options
  #

  # childView: App.MachineView
  # TODO transfer editable to itemView
  # childViewOptions: (model, index) ->
  #   return {editable: true }
  # childViewContainer: '.pms-machines'

  #
  # Marionette.Layout options
  #

  regions:
    article_task_templates: '.pms-articles'
    machines: '.pms-machines'

  #
  # Methods
  #

  onDomRefresh: ->
    ###
    Override of Marionette.View#onDomRefresh.
    ###
    @article_task_templates.show(new App.ArticleTaskTemplateHolder(model: @model))
    @machines.show(new App.MachineTaskTemplateHolder(model: @model))

    # hide machine field if empty
    console.log "onDomRefresh machine length: #{@model.get('machines').length}"
    if (@model.get('machines').length > 0)
      @$el.find('.pms-task-machines-holder').show()
    else
      @$el.find('.pms-task-machines-holder').hide()
    # hide article field if empty
    console.log "onDomRefresh article length: #{@model.get('article_task_templates').length}"
    if (@model.get('article_task_templates').length > 0)
      @$el.find('.pms-task-articles-holder').show()
    else
      @$el.find('.pms-task-articles-holder').hide()

  initialize: ->
    @listenTo @model.get('article_task_templates'), 'add', @_toggleArticle
    @listenTo @model.get('article_task_templates'), 'remove', @_toggleArticle
    @listenTo @model.get('machines'), 'add', @_toggleMachine
    @listenTo @model.get('machines'), 'remove', @_toggleMachine

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      assignedToUser: App.user?.isAssignedTo(@model)
      operator: @model.get('operator')?.toJSON() || null

  onRender: ->
    ###
    Override of Marionette.ItemView#onRender.
    ###
    @ui.addUtilityButton.popover()
    # Make the associated task's ID available through the DOM. This is
    # used by the task drag-and-drop code in ProcessTemplateView.
    @$el.attr('data-pms-id', @model.id)

  #
  # Event handlers
  #

  _removeTask: ->
    @model.destroy()

  _showAssignDialog: ->
    new App.OperatorSearchDialog().show (operator) =>
        operatorSpec = {id: operator.id, type: operator.get('type')}
        @model.save(
          task_template: {operator: operatorSpec},
          {patch: true, wait: true})

  _addMaterial: ->
    @ui.addUtilityButton.popover('hide')

  _addMachine: ->
    @ui.addUtilityButton.popover('hide')

  _showAddMachineDialog: ->
    @ui.addUtilityButton.popover('hide')
    new App.MachineSearchDialog().show (machine) =>
        @model.save(
          task_template: {machine: machine.id},
          {patch: true, wait: true})

  _showAddArticleDialog: ->
    @ui.addUtilityButton.popover('hide')
    new App.ArticleSearchDialog().show (article) =>
        @model.save(
          task_template: {article: article.id},
          {patch: true, wait: true})

   _toggleArticle: ->
    # hide article field if empty
    if (@model.get('article_task_templates').length > 0)
      @$el.find('.pms-task-articles-holder').show()
    else
      @$el.find('.pms-task-articles-holder').hide()

  _toggleMachine: ->
    # hide machine field if empty
    if (@model.get('machines').length > 0)
      @$el.find('.pms-task-machines-holder').show()
    else
      @$el.find('.pms-task-machines-holder').hide()
