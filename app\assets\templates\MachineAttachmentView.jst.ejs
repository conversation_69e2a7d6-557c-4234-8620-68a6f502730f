<% if (uploading || deleting) { %>
<div class="pms-attachment-link">
<% } else { %>
<a class="pms-attachment-link" target="_blank" href="<%= url %>">
<% } %>

  <aside>
    <% if (uploading) { %>
      <i class="fas fa-spinner fa-pulse"></i>
    <% } else if (deleting) { %>
      <i class="far fa-trash-alt"></i>
    <% } else if (content_type.match(/^image/)) { %>
      <img src="<%= url %>/thumbnail">
    <% } else { %>
      <i class="fas fa-file"></i>
    <% } %>
  </aside>

  <% if (deleting) { %>
    <span class="text-muted pms-attachment-filename">
      Löschen ...
    </span>
  <% } else if (uploading) { %>
    <span class="text-muted pms-attachment-filename">
      <%- filename %>
    </span>
  <% } else { %>
    <span class="pms-attachment-filename" href="<%= url %>" target="_blank">
      <%- filename %>
    </span>
  <% } %>

<% if (uploading || deleting) { %>
</div>
<% } else { %>
</a>
<% } %>

<button class="btn btn-sm btn-default pms-attachment-download-btn" title="Anhang herunterladen" onclick="location.href='<%= url %>/download'">
  <i class="fas fa-download"></i>
</button>

<% if ((editable_by_user) && !uploading && !deleting) { %>
  <button class="btn btn-sm btn-default pms-attachment-delete-btn"
          title="Anhang löschen">
    <i class="far fa-trash-alt"></i>
  </button>
<% } %>
