#=require "collections/ProcessCollection"
#=require "models/Model"
#=require "models/Process"

App.Run = App.Model.extend

  #
  # Backbone.Model options
  #

  urlRoot: '/api/runs'

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasMany
    key: 'processes'
    keySource: 'process_ids'
    relatedModel: App.Process
    collectionType: App.ProcessCollection
    reverseRelation:
      key: 'run',
      keySource: 'run_id',
      includeInJSON: 'id'
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'run'
  jsonPluralRoot: 'runs'

  #
  # Methods
  #

  prev: ->
    ###
    Returns the run immediately preceding this run.  If the
    run is the first one in the assignment, null is returned.
    ###
    runs = @get('assignment').get('runs')
    runs.findWhere(version: @get('version') - 1)
