# Executed when a user adds a new process to a run.
class CreateProcessAction < CreateAction
  # Initialize the action.
  #
  # name_or_template - Either the name of the new process to create
  #                    or an existing process that should be used as
  #                    template for the new one.
  # run              - The run within to create the process in.
  # parent           - The process to make the new process a child of
  #                    (optional). Passing nil creates a root process
  #                    (no parent).
  def initialize(name_or_template, run, parent = nil)
    super(run)
    @name_or_template = name_or_template
    @run = run
    @parent = parent
  end

  def allowed?(user)
    @run.editable_by_user?(user)
  end

  def do_create!
    if @name_or_template.is_a?(String)
      process = new_process
    elsif @name_or_template.is_a?(ProcessTemplate)
      process = instantiate_process
    elsif @name_or_template.is_a?(PmsProcess)
      process = copy_process
    end
    add_parent_to_new_process(process, @parent) if @parent
    process
  end

  private

  def new_process
    PmsProcess.create!(name: @name_or_template, run: @run)
  end

  def instantiate_process
    new_process = @name_or_template.new_instance
    new_process.run = @run
    new_process.save!
    new_process
  end

  def copy_process
    new_process = @name_or_template.copy
    new_process.run = @run
    new_process.save!
    new_process
  end

  def add_parent_to_new_process(process, parent)
    process.parent_dependencies.create!(parent: parent)
    mark_changed(parent)
  end
end
