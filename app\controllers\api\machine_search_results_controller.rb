# Provides a JSON API for searching for task machines.
class Api::MachineSearchResultsController < ApplicationController
  # Search for machine by name.
  #
  # query - The search term.
  #
  # Returns matching machines as JSON (using MachineSearchResultSerializer).
  def search
    if params[:search].present?
      # remove all '%' from search params to prevent output of all machines
      params[:search].gsub! '%', ''
      if params[:search].length >= 2
        by_name = search_by("name", Machine)
        # to add more search possibilitys like location, see Api::OperatorSearchResultsController
        result = (by_name).uniq
      else
        result = []
      end
    else
      # Don't return all machines at once to avoid too much traffic
      result = []
    end
    render json: result, root: :search_results
  end

  private

  def search_by(attribute, scope)
    scope
      .includes(:responsible, :deputy, :instructed_users_group, :attachments, location: :building)
      .where("lower(#{attribute}) like lower(?)", "%#{params[:search]}%")
  end
end
