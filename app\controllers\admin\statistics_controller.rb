class Admin::StatisticsController < Admin::AdminController
  before_action :require_permission

  def index
    @assignments_count = 0
    @num_active = 0
    @num_completed = 0
    @num_failed = 0
    @num_paused = 0
    @num_draft = 0
    @num_template = 0

    @active_assignments = Array.new
    @completed_assignments = Array.new
    @failed_assignments = Array.new
    @paused_assignments = Array.new
    @draft_assignments = Array.new
    @template_assignments = Array.new

    assignments_statistic_data = Task.connection.select_all(sql_get_statistic_data)
    assignments_statistic_data.each do |row|
      case row['state']
        when "active"
          @active_assignments.push(row)
          @num_active = @num_active + row['numCount'].to_i
        when "completed"
          @completed_assignments.push(row)
          @num_completed = @num_completed + row['numCount'].to_i
        when "failed"
          @failed_assignments.push(row)
          @num_failed = @num_failed + row['numCount'].to_i
        when "paused"
          @paused_assignments.push(row)
          @num_paused = @num_paused + row['numCount'].to_i
        when "draft"
          @draft_assignments.push(row)
          @num_draft = @num_draft + row['numCount'].to_i
        when "template"
          @template_assignments.push(row)
          @num_template = @num_template + row['numCount'].to_i
      end
      @assignments_count = @assignments_count + row['numCount'].to_i
    end

    @all_workgroups = Workgroup.all.where(extern: false).order(:name).pluck(:name)
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      @years = Workgroup.connection
              .select_all("SELECT DISTINCT datepart(yyyy, completed_at) AS year 
                    FROM runs 
                    WHERE completed_at IS NOT NULL 
                    ORDER BY year ASC")
              .map { |row| row["year"] }
    else
      @years = Workgroup.connection
              .select_all("SELECT DISTINCT strftime('%Y', completed_at) AS year 
                    FROM runs 
                    WHERE completed_at IS NOT NULL 
                    ORDER BY year ASC")
              .map { |row| row["year"] }
    end
    @num_assignments_by_year_workgroup = @years.each_with_object({}) do |year, hash|
      @all_workgroups.each do |workgroup|
        hash[year.to_i] ||= {}
        hash[year.to_i][workgroup] ||= {}
        %w[active completed failed paused draft template].each do |state|
          result = Workgroup.connection.select_all(sql_num_assignments(year, state, workgroup)).first
          hash[year.to_i][workgroup][state] = result ? result['assign_count'].to_i : 0
        end
      end
    end

    @num_tasks_by_year_workgroup = @years.each_with_object({}) do |year, hash|
      @all_workgroups.each do |workgroup|
        hash[year.to_i] ||= {}
        result = Workgroup.connection.select_all(sql_num_tasks(year, workgroup)).first
        hash[year.to_i][workgroup] = result ? result['task_count'].to_i : 0
      end
    end

    users = User.all
    @users_count = users.count
    group_users_count = users.group(:deactivated).count
    @number_of_active_users = group_users_count[false]
    @number_of_deactivated_users = group_users_count[true]

    @num_tasks = 0
    @num_users = 0
    @workgroup_id = 0
    if params[:workgroup] 
      @workgroup_id = params[:workgroup].to_i
    end
    if params[:numTasks] && params[:year]
      @num_tasks = params[:numTasks].to_i
      @year_to_view = params[:year].to_i
      @long_tasks_2019 = Task.connection.select_all(sql_long_tasks(@num_tasks))
    elsif params[:numUsers] && params[:year]
      @num_users = params[:numUsers].to_i
      @year_to_view = params[:year].to_i
      @long_tasks_users = Task.connection.select_all(sql_long_tasks_users(@num_users))
    end
  end

  private

  def require_permission
    return if current_user && current_user.can_edit_users?
    redirect_to root_path
  end

  def sql_get_statistic_data
    <<-SQL
    SELECT r1.state, users.workgroup_id, workgroups.name, COUNT(*) as numCount
      FROM assignments
      JOIN runs r1 ON (assignments.id = r1.assignment_id)
      LEFT OUTER JOIN runs r2 ON (assignments.id = r2.assignment_id AND (r1.version < r2.version))
      JOIN users ON assignments.created_by_id = users.id
      JOIN workgroups ON users.workgroup_id = workgroups.id
      WHERE r2.id IS NULL
      group by users.workgroup_id,workgroups.name, r1.state
      order by r1.state
    SQL
  end

  def sql_long_tasks(limit)
    <<-SQL
    SELECT TOP #{limit} tasks.name, Count(tasks.name) as amount, AVG(duration) as avgDuration, MAX(duration) as maxDuration, MIN(duration) as minDuration, Sum(duration) as totalTime
      FROM tasks
      INNER JOIN users ON users.id = tasks.completed_by_id
      where completed = #{sql_boolean_true}
      #{sql_workgroup}
      and duration > 0
      and duration < 5256000
      and #{sql_timespace}
      group by tasks.name
      having Sum(duration) > (86400 * 2)
      order by totalTime desc
    SQL
  end

  def sql_long_tasks_users(limit)
    <<-SQL
    SELECT users.username, tasks.completed_by_id, Count(*) as amount, AVG(duration) as avgDuration, MAX(duration) as maxDuration, MIN(duration) as minDuration, Sum(duration) as totalTime
      FROM tasks
      INNER JOIN users ON users.id = tasks.completed_by_id 
      where completed = #{sql_boolean_true}
      #{sql_workgroup}
      and duration > 0
      and duration < 5256000
      and #{sql_timespace}
      group by users.username, tasks.completed_by_id
      order by totalTime desc
    SQL
  end

  def sql_num_tasks(year, workgroup_name)
    <<-SQL
    SELECT COUNT(tasks.id) as task_count
      FROM tasks
      JOIN users ON tasks.completed_by_id = users.id
      JOIN workgroups ON users.workgroup_id = workgroups.id
      WHERE tasks.completed = #{sql_boolean_true}
      AND #{sql_specific_year(year)}
      AND workgroups.name = '#{workgroup_name}'
      AND workgroups.extern = #{sql_boolean_false}
      GROUP BY workgroups.name
      ORDER BY workgroups.name
    SQL
  end

  def sql_num_assignments(year, state, workgroup_name)
    <<-SQL
    SELECT COUNT(assignments.id) as assign_count
      FROM assignments
      JOIN runs r1 ON (assignments.id = r1.assignment_id)
      LEFT OUTER JOIN runs r2 ON (assignments.id = r2.assignment_id AND (r1.version < r2.version))
      JOIN users ON assignments.created_by_id = users.id
      JOIN workgroups ON users.workgroup_id = workgroups.id
      WHERE r2.id IS NULL
      AND r1.state = '#{state}'
      AND #{sql_specific_year(year, 'r1')}
      AND workgroups.name = '#{workgroup_name}'
      AND workgroups.extern = #{sql_boolean_false}
      GROUP BY workgroups.name
      ORDER BY workgroups.name
    SQL
  end

  def sql_specific_year(year, table_alias = nil)
    column = table_alias ? "#{table_alias}.completed_at" : "completed_at"
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "datepart(yyyy, #{column}) = '#{year}'"
    else
      "strftime('%Y', #{column}) = '#{year}'"
    end
  end

  def sql_boolean_true
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "1"
    else
      "\"t\""
    end
  end

  def sql_boolean_false
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "0"
    else
      "\"f\""
    end
  end

  def sql_timespace
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "completed_at >= CONVERT(datetime, '#{@year_to_view}-01-01') and completed_at < CONVERT(datetime, '#{@year_to_view + 1}-01-01')"
    else
      "completed_at >= '#{@year_to_view}-01-01' AND completed_at < '#{@year_to_view + 1}-01-01'"
    end
  end

  # use workgroup_id as constraint if specified
  def sql_workgroup
    if @workgroup_id > 0
      "and users.workgroup_id = #{@workgroup_id}"
    else
      ""
    end
  end
end
