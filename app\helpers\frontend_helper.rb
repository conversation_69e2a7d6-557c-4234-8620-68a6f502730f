module FrontendHelper
  def state_label_class(state)
    if state == "active"
      "label-primary"
    elsif state == "template"
      "label-default"
    elsif state == "draft"
      "label-default"
    elsif state == "failed"
      "label-danger"
    elsif state == "completed"
      "label-success"
    elsif state == "paused"
      "label-warning"
    end
  end

  def state_label_content(state)
    if state == "active"
      "Aktiv"
    elsif state == "template"
      "Vorlage"
    elsif state == "draft"
      "Inaktiv"
    elsif state == "failed"
      "Abgebrochen"
    elsif state == "completed"
      "Abgeschlossen"
    elsif state == "paused"
      "Pausiert"
    end
  end

  def pretty_date(date)
    l(date, format: "%A, %d.%m.%Y")
  end

  # generates a formated link for telefon numbers
  def tel_to(phone_number)
    phone_number = number_to_phone(phone_number, delimiter: " ")
    link_to phone_number, "tel:#{phone_number}"
  end

  def remove_spaces(string)
    string.delete(' ').tr("/().", "-")
  end

  def set_values(assignments, project, current_user)
    @filtered_assignments = assignments.select { |a| a.project.delete(' ').downcase == project.delete(' ').downcase}
    @filtered_assignments_sort_id = @filtered_assignments.sort_by &:id
    @project_priority = @filtered_assignments.map { |a| a.priority }.max

    # return the creator of an assignment in case he is an assigned planner, else return the name of the first planner
    @project_planners = User.find(@filtered_assignments.map do |a|
      a.created_by_id
    end
    .compact.uniq)
  end

  def set_values_group(group)
    @assignments = group.assignments.active.order(priority: :desc).select { |a| a.has_process_immediate_work_for_group?(group) }
    @assignments_sort_id = @assignments.sort_by &:id
    @group_priority = @assignments.map { |a| a.priority }.max
  end
end