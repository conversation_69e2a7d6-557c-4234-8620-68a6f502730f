#=require "views/RunView"

App.AssignmentRunsView = Marionette.CompositeView.extend
  ###
  The runs display area of an AssignmentPage. It contains a
  RunView for each run of an assignment and allows the user
  to switch between them.
  ###

  #
  # Backbone.View options
  #

  className: 'pms-assignment-runs'

  events:
    'shown.bs.tab .pms-run-tab': '_triggerShowOnRunView'

  #
  # Marionette.View options
  #

  ui:
    runTabBar: '.pms-run-tabs'
    runs: '.pms-run-views'

  #
  # Marionette.ItemView options
  #

  template: JST['AssignmentRunsView']

  #
  # Marionette.CollectionView options
  #

  childView: App.RunView
  childViewContainer: '.pms-run-views'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @collection = @model.get('runs')
    @listenTo(@collection, 'add', @_showNewRun)

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData. Make the
    assignment's runs available to the template.
    ###
    throw new Error() unless @model.get('runs')
    _.extend @model.toJSON(),
      runs: @model.get('runs').toJSON()

  onShow: ->
    ###
    Override of Marionette.View#onShow.
    ###
    @_showRun(@model.latestRun())

  _runTab: (run) ->
    @ui.runTabBar.find("a[data-run-id='#{run.id}']")

  _runView: (run) ->
    @ui.runs.find(".pms-run[id='pms-run-#{run.id}']")

  _showRun: (run) ->
    if run
      runTab = @_runTab(run)
      runTab.tab('show')

      # For some reason, Bootstrap's tab('show') does not always show
      # the RunView associated with the run tab, but only
      # shows the tab as active. To be sure everything works as
      # intended, we show the run view manually.
      runView = @_runView(run)
      @$(runView).addClass('active')

  #
  # Event Handlers
  #

  _triggerShowOnRunView: (event) ->
    # Make sure that "show" is triggered on a RunView whenever it
    # becomes visible. This works around the issue that rendering the
    # process graph of a run is only possible while the run
    # is shown on-screen, as the layout algorithm depends on the
    # ProcessViews' positions on the page.
    tab = $(event.target)
    runId = tab.attr('data-run-id')
    run = App.Run.find(runId)
    runView = @children.findByModel(run)
    runView.triggerMethod('show')

  _showNewRun: (run) ->
    # Marionette won't let us re-render the view's tab bar without
    # also re-rendering all contained RunViews. We thus create
    # the new run tab manually, at the cost of some duplication.
    tabLink = $('<a/>')
      .addClass('pms-run-tab')
      .attr('data-toggle', 'tab')
      .attr('data-run-id', run.id)
      .attr('href', '#pms-run-' + run.id)
      .text(run.get('version'))
    tabItem = $('<li/>').append(tabLink)
    tabItem.appendTo(@ui.runTabBar)

    # Give the new run time to be rendered, then show it.
    setTimeout (=> @_showRun(run)), 0
