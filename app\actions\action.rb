# Represents an action requested by the user. This is an abstract base
# class; the actual actions are implemented in subclasses such as
# CompleteTaskAction.
class Action
  # The record targeted by the action.
  attr_reader :subject

  # An array of all records which changed due to the action's
  # execution. In this context, "changed" means that any of the
  # record's serializable attributes has a new value, regardless of
  # whether the attribute is stored (such as Task#completed) or
  # computed (as Task#completable?). Thus, #changed_records indicates
  # which records need to be sent to clients to update their state.
  #
  # Returns the set of changed records as an array. The action's
  #   subject record is excluded.
  attr_reader :changed_records

  # Initializes an action. Remember that Action is an abstract class
  # and should not be initialized directly.
  #
  # subject - The record targeted by the action. It is made accessible
  #           to the subclass through the #subject method.
  #
  # Returns nothing.
  def initialize(subject)
    @subject = subject
    @changed_records = []
  end

  # Determine whether the action may be executed by a user. Checks
  # both whether the action is possible (e.g., completing a completed
  # task is not) and whether the user has the necessary permissions.
  # To be implemented individually by each subclass.
  #
  # user - The user to check with.
  #
  # Returns true if the action is allowed for the user, or false
  #   otherwise.
  def allowed?(_user)
  end

  # Run the action. This includes database modifications as well as
  # sending out notification e-mails if needed. Execution happens in a
  # transaction, so all changes are guaranteed to be applied
  # atomically (all-or-nothing).
  #
  # Subclasses define the behavior of #execute! by overriding
  # #do_execute!.
  #
  # Returns nothing.
  # Raises an exception if the action fails.
  def execute!
    ActiveRecord::Base.transaction { do_execute! }
  end

  protected

  # Specifies what to do if #execute! is called. To be implemented
  # individually by each subclass.
  #
  # Returns nothing.
  def do_execute!
  end

  # Declare that `record` should be returned from #changed_records.
  # To be used in #do_execute!.
  #
  # Returns nothing.
  def mark_changed(record)
    return if !record || record == subject
    @changed_records << record
  end

  # Like ActiveRecord::Base#update!, but also marks the record as
  # changed (see #mark_changed). To be used in #do_execute!.
  #
  # Returns nothing.
  def update!(record, *args)
    record.update!(*args)
    mark_changed(record)
  end

  # Like ActiveRecord::Base#touch, but also marks the record as
  # changed (see #mark_changed). To be used in #do_execute!.
  #
  # Returns nothing.
  def touch(record)
    record.touch
    mark_changed(record)
  end

  # Provide a NotificationMailer instance for delayed mail
  # delivery. Prefer this over directly calling
  # NotificationMailer#delay in #do_execute!.
  #
  # Returns an object with the interface of NotificationMailer.
  def notify(mailer_method, *args)
    # FIXME: Sending should be delayed until the action completed
    # successfully.
    if Rails.configuration.pms_delay_notification_emails # test if emails are delivered without delay
      NotificationMailer.delay.send(mailer_method, *args)
    else
      NotificationMailer.send(mailer_method, *args).deliver_now
    end
  end

  # Execute another action and add its #changed_records to the
  # #changed_records of this action. To be used in #do_execute!.
  #
  # action_class - The class of the action to create and run.
  # params       - The constructor parameters to pass when
  #                creating the action.
  #
  # Returns nothing.
  def call!(action_class, *params)
    action = action_class.new(*params)
    action.execute!
    action.changed_records.each { |r| mark_changed(r) }
  end
end
