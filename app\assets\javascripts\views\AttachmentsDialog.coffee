#=require "views/AttachmentView"

App.AttachmentsDialog = Marionette.CompositeView.extend
  ###
  A dialog displaying all attachments of a task. Allows attachments to
  be opened and uploaded.
  ###

  #
  # Backbone.View options
  #

  className: 'pms-attachments-dialog modal'
  events:
    'submit .pms-attachments-upload-form': '_uploadAttachment'
    'change input[type=file]': '_enableUploadButton'

  #
  # Marionette.ItemView options
  #

  template: JST['AttachmentsDialog']

  #
  # Marionette.ItemView options
  #

  ui:
    fileInput: 'input[type=file]'
    uploadButton: '.pms-attachment-upload-btn'

  #
  # Marionette.CollectionView options
  #

  childView: App.AttachmentView
  childViewContainer: '.pms-attachments-list'

  #
  # Methods
  #

  initialize: (options) ->
    ###
    Override of Backbone.View#initialize.
    ###
    @title = options.title
    @collection = @model.get('attachments')
    @listenTo(@collection, 'add', @_showDownloadAllButton)
    @listenTo(@collection, 'remove', @_hideDownloadAllButton)

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      taskId: @model.id
      title: @title
      uploadAllowed: @model.get('annotatable')

  show: ->
    ###
    Display the attachments dialog.
    ###
    @render()
    @$el.modal('show')

  onRender: ->
    @_hideDownloadAllButton() if @collection.isEmpty()

  #
  # Event handlers
  #

  _showDownloadAllButton: ->
    @$(".pms-attachments-download-all-btn").show()

  _hideDownloadAllButton: ->
    @$(".pms-attachments-download-all-btn").hide()

  _enableUploadButton: ->
    files = _.toArray(@ui.fileInput.get(0).files)
    @ui.uploadButton.prop("disabled", files.length == 0)

  _uploadAttachment: ->
    files = _.toArray(@ui.fileInput.get(0).files)
    if files.some((f) -> f.size > 210000000)
      confirm "Mindestens eine ausgewählte Datei ist zu groß zum Hochladen (>200MB). Wende Dich bitte an das PMS-Team."
      @$el.modal('hide')
    else
      @_uploadFilesOneByOne(files)

  _uploadFilesOneByOne: (files) ->
    return if files.length == 0

    file = files.shift()
    #filename = @_clearFilename(file.name)
    attachment = new App.TaskAttachment(task_id: @model.id, filename: file.name)
    @collection.sort()


    attachment.upload(file)
      .done =>
        @_uploadFilesOneByOne(files)
      .fail =>
        @collection.remove(file)
