# Executed if a user moves a process within a run's process graph.
class MoveProcessAction < Action
  # Initialize the action.
  #
  # process     - The process to move.
  # parent      - The process which should be made the moved process's
  #               new parent.
  # child_index - Where to insert the process within the new parent's
  #               children (optional).
  def initialize(process, parent, child_index = nil)
    super(process)
    @parent = parent
    @child_index = child_index
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    return if @parent == subject
    call!(RemoveProcessAction, subject)
    call!(InsertProcessAction, subject, @parent, @child_index)
  end
end
