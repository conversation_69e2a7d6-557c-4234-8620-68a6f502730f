# Serves the admin pages for managing PMS user accounts.
class Admin::UsersController < Admin::AdminController
  before_action :require_permission

  # Show a page listing all users in the system.
  #
  # Returns a page based on the "admin/users/index" template.
  def index
    @users = User.active.order(:username)
    @deactivated = User.select { |u| u.deactivated }
    @workgroups = Workgroup.order(:name)
  end

  # Show a page for creating a new user.
  #
  # Returns a page based on the "admin/users/new" template.
  def new
    @user = User.new
  end

  # Create a new user and tell the user whether it was successful.
  #
  # Returns either a redirect to the new user's edit page or the
  # new-user page with an error message.
  def create
    @user = User.new(permitted_params.merge(first_login: true, encrypted_password: ''))
    if @user.save
      @user.send_reset_password_instructions
      if params[:redirect] == 'index'
        redirect_to action: :index
      else
        redirect_to action: :new
      end
      flash[:notice] = "Der Benutzer wurde angelegt."
    else
      render :new # with error messages
    end
  end

  # Show a page to edit an existing user.
  #
  # id - The user's ID.
  #
  # Returns a page based on the "admin/users/edit" template.
  def edit
    @user = User.find(params[:id])
  end

  # Create a new user and tell the user whether it was successful.
  #
  # id - The user's ID.
  #
  # Returns either a redirect to the new user's edit page or the
  # New-user page with an error message.
  def update
    @user = User.find(params[:id])
    if params[:user][:can_set_highest_priority] == "1" && params[:user][:can_edit_planners] == "0"
      flash[:notice] = "Das Häkchen bei 'Kann Aufträge auf sehr hohe Priorität setzen' muss ebenfalls entfernt werden."
      redirect_to action: :edit, id: params[:id]
    else
      if @user.update(permitted_params)
        flash[:notice] = "Die Benutzerdaten wurden gespeichert."
        redirect_to action: :edit, id: params[:id]
      else 
        @current_user = current_user
        render :edit # with error messages
      end
    end
  end

  def destroy
    user = User.find(params[:id])
    user.update!(deactivated: true)
    flash[:notice] = "Der Benutzeraccount von #{user.name} wurde deaktiviert."
    redirect_to action: :index
  end

  def activate
    user = User.find(params[:id])
    user.update!(deactivated: false)
    flash[:notice] = "Der Benutzeraccount von #{user.name} wurde reaktiviert."
    redirect_to action: :index
  end

  private

  def require_permission
    return if current_user && current_user.can_edit_users?
    redirect_to root_path
  end

  def permitted_params
    params.require(:user).permit(:username,
                                 :first_name,
                                 :last_name,
                                 :email,
                                 :workgroup_name,
                                 :workgroup_id,
                                 :can_edit_templates,
                                 :can_edit_planners,
                                 :can_edit_users,
                                 :can_edit_machines,
                                 :can_set_highest_priority,
                                 :extern,
                                 operator_group_ids: [])
  end
end
