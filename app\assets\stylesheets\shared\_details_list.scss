// The "pms-details-list" class ist used for definition lists (<dl>)
// which show the detailed attributes of some entity (e.g. a process).
// Use it in combination with Bootstrap's "dl-horizontal" class:
//
//     <dl class="dl-horizontal pms-details-list">
//       <dd>Name</dd>
//       <dt>...</dt>
//       ...
//     </dl>

.pms-details-list {
  dt, dd {
    margin-bottom: 0.25em;
    padding-left: 0em;
  }

  dd {

    .pms-multi-line {
      white-space: pre-line;
      min-height: 1.5em;
      word-wrap: break-word;
    }

    .pms-editable {
      border: 1px solid transparent;
      padding: 0px;
    }

    .pms-editable:hover {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 0px;
    }
  }
}
