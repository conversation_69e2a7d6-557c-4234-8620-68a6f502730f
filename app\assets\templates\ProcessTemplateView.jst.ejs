<h3 class="pms-process-header">
  <span class="pms-process-name">
    <%= valueOrPlaceholder(name) %>
  </span>
</h3>

<div class="pms-process-body">
  <% if (instruction) {%>
    <dl class="pms-process-details pms-details-list">
      <dt>Instruktion</dt>
      <dd><span class="pms-process-instruction"><%=
        valueOrPlaceholder(instruction) %>
      </span></dd>
    </dl>
  <% } %>

  <div class="pms-tasks">
    <% /* TaskView(s) */ %>
  </div>

  <form class="pms-new-task-form form-inline">
    <div class="form-group">
      <label class="sr-only" for="pms-new-task-name-input">
        Aufgabenname
      </label>
      <input type="text"
             class="form-control pms-new-task-name-input"
             id="pms-new-task-name-input"
             placeholder="Neue Aufgabe">
    </div>
    <button class="btn btn-default pms-new-task-btn">
      <i class="fas fa-plus"></i>
      Aufgabe hinzufügen
    </button>
  </form>

  <% if (updated_at) {%>
    <div class="pms-process-date">Letzte Änderung: <%= prettyDateTime(updated_at) %></div>
  <% } %>

</div>
