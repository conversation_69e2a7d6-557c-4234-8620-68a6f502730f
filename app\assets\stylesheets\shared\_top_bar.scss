// Styles for the "shared/_top_bar" partial view.

.dropdown-menu {
  a {
    padding: 3px 18px 3px 15px !important;
  }

  i {
    min-width: 25px;
    text-align: center;
  }
}

.pms-top-bar {
  border-bottom: none;
  box-shadow: 0px 1px 2px #222;
  background-color: $pms-top-bar-background;
  color: $pms-top-bar-text;

  .navbar-brand, .navbar-nav > li > a, .navbar-text {
    color: $pms-top-bar-text;
  }

  .pms-navbar-header {

    margin-right: -15px;

    .pms-form-open-assignment {

      padding: 13px;

      .input-group-btn {
        display: block;
      }

      .input-group .form-control:not(:first-child):not(:last-child) {
        border-radius: 4px 0px 0px 4px;
      }

      .pms-assignment-id {
        border: 1px solid #ccc;
        width: 210px;
        border-radius: 4px 0px 0px 4px;
        font-size: 12px;
        height: 25px;
      }

      .pms-btn-open-assignment {
        border: 1px solid #ccc;
        height: 25px;
        padding: 0px 5px;
        .fa-search {
          font-size: 100%;
        }
      }
    }

    .navbar-brand:hover,
    .navbar-nav > li > a:hover,
    .navbar-brand:focus,
    .navbar-nav > li > a:focus {
      color: darken($pms-top-bar-text, 10%);
    }

    .navbar-brand {
      font-weight: bold;
    }

    .navbar-brand img {
      display: inline-block;
    }

    .navbar-toggle .icon-bar {
      background-color: white;
      border-color: white;
    }

    .navbar-toggle:focus, .navbar-toggle:hover {
      background-color: white;

      .icon-bar {
        background-color: $fraunhofer-green;
      }
    }
  }
}

// On phones, prevent the items in the user submenu from
// being displayed with dark grey text instead of white.
@media (max-width: $screen-xs-max) {
  .dropdown-menu {
    background-color: #e7e7e7 !important;

    a {
      padding: 3px 18px 3px 25px !important;
    }
  }

  .pms-top-bar .dropdown-menu > li > a {
    color: black !important;
  }

  .divider {
    background-color: #555 !important;
  }

}
