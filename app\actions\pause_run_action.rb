# Executed when a planner pauses the latest run of an assignment.
class PauseRunAction < Action
  # Initialize the action.
  #
  # run - The run to pause.
  def intialize(run)
    super(run)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.update!(state: "paused")
    mark_changed(subject.assignment) # now also paused
  end
end
