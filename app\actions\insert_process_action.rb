# Inserts a process at a specific point in a run's proces graph,
# adjusting existing connections between processes to be updated
# accordingly. The process is assumed to have neither parent nor
# children.
#
# This is a building block for other actions which do process graph
# manipulation (such as MoveProcessAction).
class InsertProcessAction < Action
  # Initializes the action.
  #
  # process     - The process to insert into the graph.
  # parent      - The process which should be made the moved process's
  #               new parent.
  # child_index - Where to insert the process within the new parent's
  #               children (optional).
  def initialize(process, parent, child_index = nil)
    super(process)
    @parent = parent
    @child_index = child_index
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def execute!
    return if @parent == subject
    move_subject_to_run_of_parent
    make_subject_child_of_parent
  end

  private

  def move_subject_to_run_of_parent
    return if subject.run_id == @parent.run_id
    subject.update!(run_id: @parent.run_id)
  end

  def make_subject_child_of_parent
    adopt_child_at_target_index_if_exists
    create_dependency_to_parent
  end

  def adopt_child_at_target_index_if_exists
    dep = ProcessDependency.find_by(parent: @parent, child_index: @child_index)
    return unless dep.present?
    dep.update!(parent: subject, child_index: 0)
    touch(dep.child)
  end

  def create_dependency_to_parent
    ProcessDependency.create!(parent: @parent,
                              child: subject,
                              child_index: @child_index)
    touch(@parent)
  end
end
