<h4 class="pms-task-header">

  <button class="btn btn-default btn-sm pms-task-check-btn" disabled>
    <i class="fas fa-check pms-task-check-btn-icon"></i>
  </button>

  <span class="pms-task-name">
    <%= valueOrPlaceholder(name) %>
  </span>

  <span class="hidden-xs pull-right pms-task-buttons" href="#">
    <button class="hidden-xs btn btn-sm btn-default pms-task-add-utility-btn"
        title="Material oder Maschinen hinzufügen"
        data-toggle="popover"
        data-placement="left"
        data-html="true"
        data-content="<%= promptAddMachineOrMaterial() %>" >
        <i class="fas fa-cubes pms-task-add-material-or-machine-btn-icon"></i>
    </button>
    <a class="hidden-xs btn btn-sm btn-default pms-task-move-btn"
       title="Aufgabe verschieben">
      <i class="fas fa-bars"></i>
    </a>
    <button class="btn btn-sm btn-default pms-task-delete-btn" title="Aufgabe löschen">
        <i class="far fa-trash-alt"></i>
    </button>
  </span>

</h4>

<dl class="dl-horizontal pms-details-list">
  <dt>Zugewiesen</dt>
    <dd>
      <span class="pms-task-operator">
        <% if (typeof id != 'undefined') { %>
          <% if (operator && operator.type == "user") { %>
            <%= prettyUser(operator, id, true) %>
          <% } else { %>
            <%= prettyOperatorGroup(operator, assignedToUser, id, true) %>
          <% } %>
        <% } %>
      </span>
      <button class="btn btn-xs btn-default pms-task-assign-btn">
        <% if (assignedToUser) { %>
          Abgeben
        <% } else { %>
          Zuweisen
        <% } %>
      </button>      
      </dd>
    <dt>Instruktion</dt>
  <dd><span class="pms-task-instruction"><%=
    App.TemplateHelpers.formatText(instruction)
  %> </span></dd>

  <div class="pms-task-articles-holder" style="display:none">  
    <dt>Material</dt>
    <dd>
      <div class="pms-task-articles">
        <table class="pms-articles" style="width:100%;">
          <% /* MaterialView(s) */ %>
        </table>
      </div>
    </dd>
  </div>

  <div class="pms-task-machines-holder" style="display:none">
    <dt>Maschinen</dt>
    <dd>
      <div class="pms-task-machines">
        <table class="pms-machines" style="width:100%;">
          <% /* MachineView(s) */ %>
        </table>
      </div>
    </dd>
  </div>
  
</dl>
