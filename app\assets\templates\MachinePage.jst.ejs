<dl class="dl-horizontal pms-details-list">
  <dt><PERSON>ntwortlicher</dt>
  <dd>
    <span class="pms-task-operator">
      <% if (typeof id != 'undefined') { %>
        <% if (responsible) { %>
          <%= prettyUserMachine(responsible, id, false) %>
        <% } %>
      <% } %>
    </span>
      <button class="btn btn-xs btn-default pms-responsible-assign-btn">
        <% if (responsible) { %>
          Abgeben
        <% } else { %>
          Z<PERSON><PERSON>sen
        <% } %>
      </button>
    </dd>
    <dt>Stellvertreter</dt>
    <dd>
      <span class="pms-task-operator">
        <% if (typeof id != 'undefined') { %>
          <% if (deputy) { %>
            <%= prettyUserMachine(deputy, id, false) %>
          <% } %>
        <% } %>
      </span>
      <button class="btn btn-xs btn-default pms-deputy-assign-btn">
        <% if (deputy) { %>
          Ab<PERSON>ben
        <% } else { %>
          <PERSON><PERSON><PERSON><PERSON>
        <% } %>
      </button>
    </dd>
</dl>

<div class="form-group">
  <label>Dokumente:</label><br>
  <span class="pms-task-buttons" href="#">
    <button class="btn btn-sm btn-default pms-task-attachments-btn" title="Anhänge">
      <span class="pms-task-num-attachments nonzero">Maschinendokumente</span>
      <i class=" fas fa-paperclip"></i>
    </button>
  </span>
</div>
