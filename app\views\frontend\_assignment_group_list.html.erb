<div class="list-group pms-assignment-list">
  <div class="list-group pms-assignmentgroup-list">
    <% projects.each do |project| %>
      <% set_values(assignments, project, current_user) %>

      <%
        visible_assignments = @filtered_assignments.select do |assignment|
          user_has_work = @any_work_ids.include?(assignment.id)
          (!@hide_user_work || user_has_work)
        end
        visible_assignments_sorted = visible_assignments.sort_by(&:id)
      %>

      <% next if visible_assignments.empty? %>

      <div class="list-group-heading prio-<%= @project_priority %>" data-toggle="collapse"
            data-target="#pms-assignmentgroup-list-<%= remove_spaces(project) %>-<%= state %>"><i class="fas fa-chevron-down"></i>
        <%= project %>
        <span class="badge">
          <%= visible_assignments.count %>
            <% board_count = visible_assignments.map(&:board_count).compact.select { |count| count > 0}.sum %>
          <% if (board_count > 0) %>
            | <%= board_count %>
          <% end %>
        </span>
        <% visible_assignments_sorted.each do |assignment| %>
          <% cache("#{assignment.cache_key}/#{current_user.id}/project_flags") do %>
            <%= link_to assignment_path(assignment.id) do %>
              <span class="label label-default pms-assignment-id" title="Auftrags-ID">
                <% if assignment.assignment_role == "planner" || assignment.role_count == 2 %>
                  <% if @immediate_work_ids.include?(assignment.id) %>
                    <i class="fas fa-dot-circle pms-smaller-icon pms-immediate-work-bright-icon" title="Sie sind hier Planer:in und einer aktuellen Aufgabe zugewiesen"></i>
                  <% elsif assignment.has_near_work_for_user?(current_user) %>
                    <i class="fas fa-dot-circle pms-smaller-icon pms-near-work-icon" title="Sie sind hier Planer:in und einer folgenden Aufgabe zugewiesen"></i>
                  <% elsif @any_work_ids.include?(assignment.id) %>
                    <i class="fas fa-dot-circle pms-smaller-icon pms-work-icon" title="Sie sind hier Planer:in und einer zukünftigen Aufgabe zugewiesen"></i>
                  <% else %>
                    <i class="fas fa-dot-circle pms-smaller-icon pms-white-icon" title="Sie sind hier Planer:in"></i>
                  <% end %>
                <% else %>
                  <% if @immediate_work_ids.include?(assignment.id) %>
                    <i class="fas fa-circle pms-smaller-icon pms-immediate-work-bright-icon" title="Sie sind hier einer aktuellen Aufgabe zugewiesen"></i>
                  <% elsif assignment.has_near_work_for_user?(current_user) %>
                    <i class="fas fa-circle pms-smaller-icon pms-near-work-icon" title="Sie sind hier einer folgenden Aufgabe zugewiesen"></i>
                  <% elsif @any_work_ids.include?(assignment.id) %>
                    <i class="fas fa-circle pms-smaller-icon pms-work-icon" title="Sie sind hier einer zukünftigen Aufgabe zugewiesen"></i>
                  <% end %>
                <% end %>
                <%= "##{assignment.id}#{".#{assignment.runs.last.version}" if assignment.runs.last.version > 1}" %>
              </span>
            <% end %>
          <% end %>
        <% end %>
        <span class="planer-label" title="Projektplanende">
          <% @project_planners.each do |planner| %>
            <%= planner.name %> <% if planner != @project_planners.last %>|<% end %>
          <% end %>
        </span>
      </div>

      <div id="pms-assignmentgroup-list-<%= remove_spaces(project) %>-<%= state %>" class="collapse">
        <%= render "assignment_list", assignments: visible_assignments, show_state: false, project: project %>
      </div>
    <% end %>
  </div>
</div>
