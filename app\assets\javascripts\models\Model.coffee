App.Model = Backbone.RelationalModel.extend
  ###
  The base class of a model classes. It adapts
  Backbone.RelationalModel to the server's JSON serialization and is
  able to extract related models from responses.
  ###

  # The name of the JSON root element for a single model (e.g. "task"
  # for Task). To be defined by each subclass.
  jsonSingularRoot: undefined

  # The name of the JSON root element for a collection of models
  # (e.g. "tasks" for Task). To be defined by each subclass.
  jsonPluralRoot: undefined

  parse: (response) ->
    ###
    Override of Backbone.Model#parse. Extracts model attributes from
    the model-type-specific root and passes side-loaded models to
    App.Model.extractRelatedModels (see further below).
    ###
    App.Model.extractRelatedModels(response)
    if response[@jsonSingularRoot]
      response[@jsonSingularRoot]
    else
      response

  destroy: (options) ->
    ###
    Overrride of Backbone.Model#destroy. Looks for updated models
    returned by the backend and applies the changes to the local
    store.
    ###
    promise = Backbone.RelationalModel::destroy.call(this, options)
    promise.then (response) =>
      @parse(response)


# Used to keep track of all App.Model subclasses.
App.Model.subclasses = []

App.Model._extend = App.Model.extend
App.Model.extend = ->
  ###
  Override of Backbone.RelationalModel.extend which additionally keeps
  track of all created subclasses. This information is used to
  implement the extraction of side-loaded related models from server
  responses (see #_loadRelatedModels).
  ###
  subclass = @_extend.apply(this, arguments)
  @subclasses.push(subclass)
  return subclass

App.Model.extractRelatedModels = (response) ->
  ###
  Read models of all known JSON plural roots (that is, the roots
  defined in the App.Model subclasses) and add them to the model store.
  ###
  for subclass in App.Model.subclasses
    relatedData = response[subclass::jsonPluralRoot]
    continue unless relatedData
    subclass.findOrCreate(attrs) for attrs in relatedData
