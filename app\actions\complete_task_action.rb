# Executed if a user marks a task as completed.
class CompleteTaskAction < Action
  # Initializes a CompleteTaskAction.
  #
  # task            - The task name.
  # current_user    - The user which completes the task.
  def initialize(task, current_user)
    super(task)
    @current_user = current_user
  end

  def allowed?(_user)
    subject.completable?
  end

  def do_execute!
    subject.update!(completed: true)
    subject.update!(completed_at: Time.now)
    subject.update!(completed_by: @current_user)
    # sets the completion_quality to 1 as default when task is completed
    subject.update!(completion_quality: 1)
    # sets the duration field of the task
    subject.calculate_duration
    # notify_after_undo_completion_timeout
    notify(:task_completed, subject, subject.completed_at)
    notify(:task_completed_observer, subject, subject.completed_at)
    case subject.operator
      when OperatorGroup
        ActionController::Base.new.expire_fragment("open_tasks_table/#{subject.operator.id}/operator_group_cache")
        # notify_after_undo_completion_timeout
        notify(:group_task_completed, subject, subject.completed_at)
    end

    handle_possibly_completed_process
    handle_now_completable_successors
    handle_possibly_completed_run
    notify_planners_about_unassigned_tasks
  end

  private

  def handle_possibly_completed_process
    return unless subject_last_task_in_process?
    mark_changed(subject.process.reload)
    subject.process.update!(process_completed: true)
    # notify_after_undo_completion_timeout
    notify(:process_completed, subject.process, subject.completed_at)
    notify(:process_completed_observer, subject.process, subject.completed_at)

    subject.process.children.select(&:completable?).each do |process|
      # notify_after_undo_completion_timeout
      notify(:process_ready, process)
      notify(:process_ready_observer, process)
    end
  end

  def subject_last_task_in_process?
    subject.position == subject.process.tasks.maximum(:position)
  end

  def handle_now_completable_successors
    subject.successors.select(&:completable?).each do |successor|
      mark_changed(successor)

      # Only notify the next task's operator if s/he isn't the one who
      # completed this task - otherwise, the notifcation is redundant
      # as the user gets this information directly from the UI.
      case successor.operator
        when User
          # notify_after_undo_completion_timeout
          notify(:next_task_ready, subject, successor, subject.completed_at)
        when OperatorGroup
          # notify_after_undo_completion_timeout
          notify(:next_group_task_ready, subject, successor, subject.completed_at)
      end

      # notify_after_undo_completion_timeout
      notify(:next_task_ready_planner, subject, successor, subject.completed_at)

      # If the successor is in another process than the subject, it
      # must be the first task of a process that has now become
      # completable. In that case, the process has changed, too.
      next unless successor.process != subject.process
      mark_changed(successor.process)
    end
  end

  def handle_possibly_completed_run
    run = subject.process.run
    return unless run.done?

    run.update!(state: "completed")
    run.update!(completed_at: Time.now)

    mark_changed(run)
    mark_changed(run.assignment)

    # notify_after_undo_completion_timeout
    notify(:assignment_completed, run.assignment,
                            subject,
                            subject.completed_at)
    notify(:assignment_completed_observer, run.assignment,
                            subject,
                            subject.completed_at)
  end

  def notify_planners_about_unassigned_tasks
    completable_tasks.select { |t| !t.operator }.each do |task|
      notify(:no_operator_assigned, task)
      notify(:no_operator_assigned_observer, task)

    end
  end

  def completable_tasks
    # completable_processes = subject.completable_processes
    non_empty = completable_processes.select { |p| !p.tasks.empty? }
    non_empty.map { |p| p.tasks.find(&:completable?) }
  end

  def completable_processes
    subject.process.run.completable_processes
  end

  # def notify_after_undo_completion_timeout
  #   timeout = Rails.configuration.pms_undo_complete_timeout
  #   NotificationMailer.delay(run_at: timeout.from_now)
  # end
end
