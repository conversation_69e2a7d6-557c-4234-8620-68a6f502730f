# Provides an API to create, manipulate and search for processes.
class Api::PmsProcessesController < ApplicationController
  # Search for processes by name. Only processes in assignments
  # planned by the requesting user are considered.
  #
  # search - The search query.
  #
  # Returns the found processes as JSON.
  def index
    if params[:search].present?
      search = "#{params[:search]}%"
      result = PmsProcess
        .where("lower(pms_processes.name) like lower(?)", search)
        .joins(run: { assignment: :planners })
        .joins(:tasks)
        .where(users: { id: current_user.id })
        .where.not(runs: { state: "failed" })
        .distinct
        .includes(run: :assignment)
    else
      result = PmsProcess.none
    end
    render json: result, root: :processes, assignment_name: true
  end

  # Create a new process within a run.
  #
  # name    - The name of the new process. Only required if the copy_of
  #           parameter is not passed.
  # run_id  - The ID of the run to add the process to.
  # insert  - Where to insert the process within the run (optional).
  #           :parent_id   - The ID of the process which should be the
  #                          parent of the new process.
  #
  # Returns the new process and all other changed records as JSON.
  # Raises "403 Forbidden" if the operation is not allowed
  #   for the requesting user.
  def create
    run = Run.find(params[:run_id])

    if params[:template_id]
      if Rails.configuration.pms_use_process_templates
        name_or_template = ProcessTemplate.find(params[:template_id])
      else
        name_or_template = PmsProcess.find(params[:template_id])
      end
    elsif params[:copy_of]
      name_or_template = PmsProcess.find(params[:copy_of])
    else
      name_or_template = params[:name]
    end

    if params[:insert]
      parent = PmsProcess.find(params[:insert][:parent_id])
    else
      parent = nil
    end

    action = CreateProcessAction.new(name_or_template, run, parent)
    assert_action_allowed(action) || return
    action.execute!

    unless params[:copy_of] || params[:template_id]
      first_task_action = CreateTaskAction.new(action.created_record, "Aufgabe 1", current_user)
      assert_action_allowed(first_task_action) || return
      first_task_action.execute!
      process_data = to_json(action.created_record, also: [first_task_action.created_record, *action.changed_records])
      # hacky but i don't know how to do it better
      process_data = process_data.sub(/\"task_ids\":\[\]/, "\"task_ids\":[#{first_task_action.created_record.id}]")
    else
      process_data = to_json(action.created_record, also: action.changed_records)
    end

    render json: process_data
  end

  # Manipulate a process.
  #
  # id            - The process's ID.
  # name          - The new name for the process (optional).
  # instruction   - The new instruction for the process (optional) [not longer supported]
  # insert        - Where to move the process to (optional).
  #                 :parent      - The ID of the process which should
  #                                become the process's new parent.
  #                 :child_index - Where to insert the new process in
  #                                relation to its siblings (optional).
  #                                If omitted, the process is added at
  #                                the end of the parent's child list.
  # add_parent    - The ID of a parent to add to the process (optional).
  # remove_parent - The ID of a parent to remove from the process (optional).
  #
  # Returns the updated process and any other changed records as JSON.
  # Raises "403 Forbidden" if the operation is not allowed
  #   for the requesting user.
  def update
    process = PmsProcess.find(params[:id])
    action = new_update_action(process, params)
    execute_action_and_respond(action)
  end

  # Delete a process.
  #
  # id - The process's ID.
  #
  # Returns all records changed due to the deletion.
  # Raises "403 Forbidden" if the operation is not allowed
  #   for the requesting user.
  def destroy
    process = PmsProcess.find(params[:id])
    action = DeleteProcessAction.new(process)
    execute_action_and_respond(action)
  end

  private

  # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
  def new_update_action(process, params)
    if params[:add_parent]
      new_parent = PmsProcess.find(params[:add_parent])
      AddProcessParentAction.new(process, new_parent)
    elsif (params[:instruction] && params[:instruction] == "")
      UpdateAttributeAction.new(process, :instruction, nil)
    elsif params[:due]
      UpdateAttributeAction.new(process, :due, params[:due])
    elsif params[:name]
      UpdateAttributeAction.new(process, :name, params[:name])
    elsif params[:insert]
      parent = PmsProcess.find(params[:insert][:parent_id])
      child_index = params[:insert][:child_index]
      MoveProcessAction.new(process, parent, child_index)
    elsif params[:remove_parent]
      parent = PmsProcess.find(params[:remove_parent])
      RemoveProcessParentAction.new(process, parent)
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
end
