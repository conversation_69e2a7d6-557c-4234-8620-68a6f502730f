// Styles for the AttachmentsPictures Backbone view, used on the
// assignment page.

.pms-attachment-pictures-dialog {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;

  .pms-attachment-pictures-overlays {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .pms-attachment-pictures-prev-zone,
    .pms-attachment-pictures-next-zone {
      cursor: pointer;
      z-index: 3;
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .pms-attachment-pictures-center-zone {
      flex: 4;
      display: flex;
      flex-direction: row;
      justify-content: center;
      height:100%;
      pointer-events: none;
    }

    .pms-attachment-picture-url {
      z-index: 3;
      color: white;
      background-color: #00000060;
      align-self: flex-end;
      padding: 0.5em;
    }

    .fas {
      font-size: 5vh;
      color: rgba(255, 255, 255, 0.35);
      text-shadow: rgba(0, 0, 0, 0.35) 0 0 1vh;
      width: 100%;
      text-align: center;
    }

    .pms-attachment-pictures-close {
      z-index: 4;
      position: absolute;
      top: 3vw;
      left: 3vw;
      padding: 4vw;
      width: fit-content;
      text-align: left;
      display: inline-block;
      cursor: pointer;
    }
  }

  .pms-attachment-picture-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #00000066;
    touch-action: none; // allow custom pan/zoom without browser gestures
    -ms-touch-action: none;
    overscroll-behavior: contain;
  }

  .pms-attachment-picture {
    max-width: 100%;
    max-height: 100%;
    user-select: none;
    transition: transform 0.05s;
    will-change: transform;
    cursor: grab;
    object-fit: contain;
    object-position: center;
    touch-action: none; // prevent browser panning/zooming on the image
    -ms-touch-action: none;
  }

  .pms-attachment-picture-zoom-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 6px;
    z-index: 5;
  }
  .pms-attachment-picture-zoom-controls button {
    z-index: 5;
    background: rgba(0,0,0,0.7);
    color: #fff;
    padding: 6px;
    border: none;
    border-radius: 3px;
    width: 32px;
    height: 32px;
    cursor: pointer;
  }
  .pms-attachment-picture-zoom-controls button:hover {
    background: #444;
  }
}