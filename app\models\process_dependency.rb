# A process dependency states that a process (the "parent") must be
# completed before another process (the "child") can be started.
#
# For display purposes, the children of a process are ordered by way
# of a "child index" stored in each process dependency. This ensures
# that the children of a process can always be shown in the same order
# if the process dependency graph is visualized.
class ProcessDependency < ActiveRecord::Base
  belongs_to :parent, class_name: "PmsProcess", inverse_of: :child_dependencies
  belongs_to :child, class_name: "PmsProcess", inverse_of: :parent_dependencies
  belongs_to :run
  validates :child_index, presence: true
  before_validation :set_run_id
  before_validation :choose_child_index

  private

  def set_run_id
    return if run_id.present?
    self.run_id = parent.run.id
  end

  def choose_child_index
    return if child_index.present?
    same_parent = self.class.where(parent_id: parent_id)
    highest_in_use = same_parent.maximum(:child_index) || -1
    self.child_index = highest_in_use + 1
  end
end
