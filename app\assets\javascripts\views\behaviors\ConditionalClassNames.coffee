App.Behaviors.ConditionalClassNames = Marionette.Behavior.extend
  ###
  Adds CSS classes to a view's root element depending on the
  truthiness of an attribute of the model, or on an arbitrary
  condition passed as a function.

    behaviors:
      ConditionalClassName:
        'pms-completed': 'completed'
        'pms-uncompleted': -> !@model.get('completed')

  The classes are automatically readjusted after each change to the
  view's model.
  ###

  _toggleClassNames: ->
    for className, condition of @options
      if _.isFunction(condition)
        enabled = condition.call(@view)
      else
        enabled = !!@view.model.get(condition)
      if enabled
        @view.$el.addClass(className)
      else
        @view.$el.removeClass(className)

  onShow: ->
    @listenTo(@view.model, 'change', @_toggleClassNames)

  onRender: ->
    @_toggleClassNames()
