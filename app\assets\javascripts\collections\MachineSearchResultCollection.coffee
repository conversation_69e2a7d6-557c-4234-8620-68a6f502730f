#=require "collections/Collection"
#=require "models/Machine"

App.MachineSearchResultCollection = App.Collection.extend
  ###
  Collection class for MachineSearchDialog search results.
  ###

  url: "/api/search/machines"
  jsonRoot: "search_results"

  # model: (attrs) ->
  #   switch attrs.type
  #     when "user"
  #       App.User.findOrCreate(attrs, parse: true)
  #     when "operator_group"
  #       App.OperatorGroup.findOrCreate(attrs, parse: true)

  # modelId: (attrs) ->
  #   "#{attrs.type}:#{attrs.id}"
