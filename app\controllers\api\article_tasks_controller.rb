# Provides a JSON API for updating article tasks.
class Api::ArticleTasksController < ApplicationController
  ##
  # Manipulate a article task and return it, as well as all other models
  # whose state is affected by the change (PATCH /tasks/:id).

  # Update an existing task.
  #
  # id   - The article_task's ID.
  # article_task - ArticleTask attributes (all optional).
  #    :amount   - the give amount of an article
  #
  # Returns the new article_task, as well as any other records which changed,
  #   as JSON.
  def update
    article_task = ArticleTask.find(params[:id])
    action = new_update_action(article_task, permitted_update_params)
    execute_action_and_respond(action)
  end

  private

  def permitted_update_params
    params
      .require(:article_task)
      .permit(:amount)
  end

  # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
  def new_update_action(article_task, params)
    if params[:amount]
      UpdateAttributeAction.new(article_task, :amount, params[:amount])
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

end
