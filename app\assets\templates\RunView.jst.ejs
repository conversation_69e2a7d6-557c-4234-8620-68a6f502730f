<% if (state != "template") { %>
  <header class="pms-run-header">
    <span class="text-muted">
      Erstellt am <b><%= prettyDate(created_at) %></b>
      <% if (completed_at) { %>
        &nbsp Abgeschlossen am <b><%= prettyDate(completed_at) %></b>
      <% } %>
    </span>
  </header>
<% } %>

<% if (process_ids.length == 0) { %>
 <div class="alert alert-info pms-run-alert">
   <b>Dieser Durchlauf hat noch keine Prozesse.</b>
   <% if (editable) { %>
     <a class="pms-run-add-first-process" href="">
       Füge den ersten Prozess hinzu
     </a>,
     um mit der Planung zu beginnen.
   <% } %>
 </div>
<% } %>

<% if (state == 'draft' && !editable) { %>
  <div class="alert alert-info pms-run-alert">
   <b>Der Durchlauf ist noch in Planung.</b> Er beginnt, sobald eine
   planende Person ihn aktiviert. Eine Benachrichtigung wird
   verschickt, sobald der Durchlauf aktiviert wurde.
  </div>
<% } %>

<%if (state == 'paused') { %>
  <div class="alert alert-info pms-run-alert">
    <b>Der Durchlauf ist pausiert.</b> Er kann fortgesetzt werden,
    sobald eine planende Person ihn wieder aktiviert.
    <% if (!editable) { %>
    Wenn Sie als bearbeitende Person eingeteilt sind, erhalten Sie eine E-Mail,
    sobald der Durchlauf wieder aktiviert wurde.
    <% } %>
  </div>
<% } %>

<div class="pms-process-graph"></div>

<div id="pms-process-<%= id %>"
     class="panel-group pms-processes">
  <% /* ProcessViews */ %>
</div>
