# Executed when a user unassigns a machine to a task
class UnassignMachineAction < Action
  # Initialize an UnassignMachineAction.
  #
  # task     - The task to assign.
  # machine  - The machine to unassign to.
  def initialize(task, machine)
    super(task)
    @machine = machine
  end

  # See Action.
  def allowed?(user)
    # TODO
    subject.reassignable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.machines.delete(@machine)
  end
  
end
