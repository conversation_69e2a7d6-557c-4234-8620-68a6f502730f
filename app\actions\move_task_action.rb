# Executed when a user moves a task within its containing process.
class MoveTaskAction < Action
  # Initialize a MoveTaskAction.
  #
  # task     - The task to move.
  # position - The (one-based) index of the position to move the
  #            task to.
  def initialize(task, position)
    super(task)
    @position = position
  end

  def allowed?(user)
    subject.movable_by_user?(user) && !neighbor_task_at(@position).completed?
  end

  def do_execute!
    old_position = subject.position
    subject.update!(position: @position)
    mark_tasks_to_move_as_changed(old_position)
  end

  private

  def mark_tasks_to_move_as_changed(old_subject_position)
    min, max = [old_subject_position, @position].minmax
    (min..max).each do |position|
      task_to_move = neighbor_task_at(position)
      mark_changed(task_to_move)
    end
  end

  def neighbor_task_at(position)
    subject.process.tasks.find_by_position(position)
  end
end
