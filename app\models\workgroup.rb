# A workgroup ("Arbeitsgruppe") represents an origanizational unit within
# the IZM. Each user belongs to one workgroup.
#
# Workgroups have a 'name', which is the abbreviation of their 'full name',
# such as 'EST' that stands for 'Embedding and Substrates Technology'.
# The attribute 'ou' is the internal organization unit number within Fraunhofer
# ("OE" for "Organisationseinheit").
#
# Workgroups are useful for filtering data, e.g. only assignments from one's
# own group are made accessible to a user.
class Workgroup < ActiveRecord::Base
  validates :name, presence: true
  validates :full_name, presence: true
  validates :ou, presence: true

  has_many :operator_groups, foreign_key: "workgroup_id", class_name: "OperatorGroup"
end
