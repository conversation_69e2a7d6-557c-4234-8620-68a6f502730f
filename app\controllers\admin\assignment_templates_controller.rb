# Serves the admin interface for creating and editing assignment
# templates.
class Admin::AssignmentTemplatesController < Admin::AdminController
  before_action :require_permission

  # Show a list of all assignment templates in the system.
  #
  # Returns a page based on the "admin/assignment_templates/index"
  #   template.
  def index
    templates = Assignment.joins(:runs).where("runs.state" => "template").order(:name)
    @assignment_templates = templates
  end

  # Create a new assignment template and redirect to its assignment
  # page for editing.
  #
  # Returns a redirect to the new template assignment's page.
  def create
    action = CreateAssignmentAction.new("Neue Auftragsvorlage",
                                        "Vorlage",
                                        current_user,
                                        "",
                                        "",
                                        "",
                                        3,
                                        template: true)
    template = action.execute!
    redirect_to assignment_path(template.id)
  end

  private

  def require_permission
    return if current_user && current_user.can_edit_templates?
    redirect_to root_path
  end
end
