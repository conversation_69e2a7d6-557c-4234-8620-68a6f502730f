#=require "./_backbone"
#=require "views/NotificationBar"
#=require "views/ProcessTemplatePage"

App.addInitializer ->
  App.notificationsRegion.show(new App.NotificationBar())

App.addInitializer ->
  App.template = new App.ProcessTemplate(App.template, parse: true)
  App.user = new App.User(App.user, parse: true)
  pageEl = $('.pms-process-template-page-container').get()
  page = new App.ProcessTemplatePage(el: pageEl, model: App.template)
  page.render()
