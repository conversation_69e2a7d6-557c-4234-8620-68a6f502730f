# Executed when a planner removes an observer from an assignment.
class RemoveObserverAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to remove the observer to.
  # user       - The observer to remove.
  def initialize(assignment, user)
    super(assignment)
    @user = user
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    AssignmentMembership
      .where(assignment: subject, member: @user)
      .delete_all
  end
end
