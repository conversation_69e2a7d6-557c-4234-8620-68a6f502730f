# function to sort users inside user list for faster access
$(document).ready ->
  $('#operator_search').on 'keyup', ->
    console.log "Key Press YAY"
    value = $(this).val().toLowerCase()
    $('#operator_list .list-group-holder').filter ->
    	$(this).collapse('show')
    $('#operator_list .panel-heading').filter ->
    	$(this).toggle false
    $('#operator_list .list-group-item').filter ->
      isVisible = $(this).text().toLowerCase().indexOf(value) > -1
     	$(this).toggle isVisible
     	if isVisible
     		$(this).closest(".list-group-holder").prev().toggle true
      return
    return
  return