<% if (window.navigator.userAgent.indexOf("Trident") !== -1) { var dymoprint = 1; }
else { var dymoprint = 0;
var url = new URL(window.location.href);
if (url.searchParams.get("dymoprint") == 1) { dymoprint = 1; } } %>

<h4 class="<%= stateLabelClasses() %> pms-page-heading prio-<%= priority %>">
  <span class="pms-heading-title">
    [<%= valueOrPlaceholder(project) %>] <%- name %>
  </span>
  <% if (overdue) { %>
    <i class="fas fa-exclamation-triangle pms-header-overdue-icon"
       title="Dieser Auftrag ist überfällig">
    </i>
  <% } %>
  <% if (due && !overdue) { %>
    <i class="fas fa-regular fa-calendar pms-header-overdue-icon"
       title="Es gibt hier ein Fälligkeitsdatum">
    </i>
  <% } %>
  <span class="pms-assignment-id" title="Auftrags-ID">
    #<%= id %>
  </span>
  <% if (board_count > 0) { %>
    <span class="pms-assignment-board-count" title="Anzahl">
      <%= board_count %>x
    </span>
  <% } %>
  <span class="pms-assignment-copy-datapath-top">
    <% if (datapath && datapath != "") { %>
      ...\<%= datapath.split("\\").pop() %>
      <button class="hidden-xs btn btn-xs btn-default pms-assignment-copy-datapath-btn">
        <i class="far fa-2x fa-copy"></i>
      </button>
    <% } %>
  </span>
</h4>
<% if (state != "template") { %>
  <span class="pms-assignment-state">
    <button type="button"
            class="<%= stateLabelClasses() %> pms-assignment-state-label"
            <% if (editable && state != "failed") { %>
              data-toggle="popover"
              data-placement="bottom"
              data-html="true"
              data-content="<%= statePopoverContent() %>"
            <% } %>
            data-original-title title>
      <%= prettyState() %>
    </button>
  </span>
<% } %>

<button class="btn btn-xs btn-default pms-assignment-details-btn"
        data-toggle="collapse"
        data-target=".pms-assignment-details">
  <i class="fas fa-chevron-down"></i>
  <% if (state == "template") { %>
    <b>Vorlagendetails</b>
  <% } else { %>
    <b>Auftragsdetails</b>
  <% } %>
</button>

<% if (!App.user.get('extern')) { %>
  <% if (!(state == "template") || App.user.get("can_edit_templates")) { %>
    <button class="hidden-xs btn btn-xs btn-default pms-assignment-copy-btn">
      <i class="far fa-clone"></i>
      <% if (state == "template") { %>
        <b>Vorlage kopieren</b>
      <% } else { %>
        <b>Auftrag kopieren</b>
      <% } %>
    </button>
  <% } %>

  <% if (state != "template" && App.user.get("can_edit_templates")) { %>
    <button class="hidden-xs btn btn-xs btn-default pms-assignment-create-template-btn"
            title="Aus dem Auftrag eine Vorlage erstellen">
    <i class="fas fa-share-square"></i>
      <b>Vorlage erzeugen</b>
    </button>
  <% } %>

  <% if (dymoprint == 1) { %>
    <% if (canPrintLabels) { %>
      <button class="hidden-xs btn btn-xs btn-default pms-assignment-print-label-btn"
              data-toggle="popover"
              data-placement="bottom"
              data-html="true"
              data-content="<%= printLabelPopoverContent() %>">
        <i class="fas fa-print"></i>
        <b>Etikett drucken</b>
      </button>
    <% } else { %>
      <button class="hidden-xs btn btn-xs btn-default pms-assignment-print-label-btn"
              disabled>
        <i class="fas fa-print"></i>
        <b>Kein Etikettdrucker gefunden</b>
      </button>
    <% } %>
  <% } else {%>
    <a class="hidden-xs btn btn-xs btn-default"
        href="./<%= id %>?dymoprint=1">
        <i class="fas fa-print"></i>
        <b>Etikettdrucker suchen</b>
    </a>
  <% } %>

  <% if (deletable) { %>
    <button class="hidden-xs btn btn-xs btn-danger pms-assignment-delete-btn"
            data-toggle="popover"
            data-placement="bottom"
            data-trigger="focus"
            data-html="true"
            data-content="<%= deleteConfirmationButton('pms-assignment-confirm-delete-btn') %>">
      <i class="far fa-trash-alt"></i>
      <% if (state == "template") { %>
        <b>Vorlage löschen</b>
      <% } else { %>
        <b>Auftrag löschen</b>
      <% } %>
    </button>
  <% } %>

  <% if (state != "template") { %>
    <a class="hidden-xs btn btn-xs btn-default"
       href="mailto:<%= memberEmails %>?subject=<%= mailSubject %>">
      <i class="far fa-envelope"></i>
      <b>E-Mail an Beteiligte</b>
    </a>
  <% } %>

    <button class="hidden-xs btn btn-xs btn-default pms-assignment-to-pdf-btn"
            data-toggle="popover"
            data-placement="bottom"
            data-trigger="default"
            data-delay='{ "show": 0, "hide": 2000 }'
            data-html="true"
            data-content="<button class='hidden-xs btn btn-xs btn-default
                                         pms-assignment-to-pdf-intern-btn'>
                            intern
                          </button>
                          <button class='hidden-xs btn btn-xs btn-default
                                         pms-assignment-to-pdf-extern-btn'>
                            extern
                          </button>">
      <i class="far fa-file-pdf"></i>
      <b>PDF-Export</b>
    </button>

    <button class="hidden-xs btn btn-xs btn-default pms-assignment-to-csv">
      <i class="fas fa-download" aria-hidden="true"></i>
      <b>CSV-Export</b>
    </button>
  <% } %>

<div class="pms-assignment-details collapse<%= detailsExpanded ? ' in' : '' %>">
  <dl class="dl-horizontal pms-details-list">
    <% if (state != "template") { %>
      <dt>Projektname</dt>
      <dd>
        <% if (editable && state != "completed" && state != "failed") { %>
          <span class="pms-assignment-project">
        <% } else { %>
          <span>
        <% } %>
          <%= valueOrPlaceholder(project) %>
        </span>
      </dd>
    <% } %>

    <% if (state == "template") { %>
      <dt>Vorlagenname</dt>
    <% } else { %>
      <dt>Auftragsname</dt>
    <% } %>
    <dd>
      <% if (editable && state != "completed" && state != "failed") { %>
        <span class="pms-assignment-name">
      <% } else { %>
        <span>
      <% } %>
          <%= name %>
        </span>
    </dd>

    <% if (state != "template") { %>
      <dt>
        <a class="hidden-xs" title="E-Mail an Planende"
           href="mailto:<%= plannerEmails %>?subject=<%= mailSubject %>">
           <i class="far fa-envelope"></i></a>
        Planende
      </dt>
      <dd>
        <ul class="pms-assignment-planners">
          <% _.each(planners, function(user) { %>
            <%= plannerListItem(user) %>
          <% }) %>
          <% if (editable && state != "completed" && state != "failed") { %>
           <li class="pms-add-planner">
             <button class="btn btn-xs btn-default pms-add-planner-btn">
               <i class="fas fa-plus" title="Planende hinzufügen"></i>
             </button>
           </li>
          <% } %>
        </ul>
      </dd>
      <dt>
        <a class="hidden-xs" title="E-Mail an Beobachtende"
           href="mailto:<%= observerEmails %>?subject=<%= mailSubject %>">
           <i class="far fa-envelope"></i></a>
        Beobachtende
      </dt>
      <dd>
        <ul class="pms-assignment-observers">
          <% if (observers.length == 0) { %>
            -
          <% } else { %>
            <% _.each(observers, function(user) { %>
              <%= observerListItem(user) %>
            <% }) %>
          <% } %>
          <% if (editable && state != "completed" && state != "failed") { %>
           <button class="btn btn-xs btn-default pms-add-observer-btn">
             <i class="fas fa-plus" title="Beobachtende hinzufügen"></i>
           </button>
          <% } %>
        </ul>
      </dd>
      <dt>Extern verfügbar</dt>
      <dd>
        <ul class="pms-assignment-extern-workgroups">
          <% if (externWorkgroups.length == 0) { %>
            -
          <% } else { %>
            <% _.each(externWorkgroups, function(workgroup) { %>
              <li data-pms-workgroup-id="<%= workgroup.id %>">
                <%= workgroup.name %>
                <% if (editable && state != "completed" && state != "failed") { %>
                  <button class="btn btn-xs btn-default pms-remove-extern-workgroup-btn">
                    <i class="fas fa-minus"></i>
                  </button>
                <% } %>
              </li>
            <% }) %>
          <% } %>
          <% if (editable && state != "completed" && state != "failed") { %>
             <button class="btn btn-xs btn-default pms-add-extern-workgroup-btn">
               <i class="fas fa-plus"></i>
             </button>
          <% } %>
        </ul>
      </dd>
    <% } %>
    <dt>Dateipfad</dt>
    <dd>
      <div class="pms-assignment-datapath-container">
        <% if (editable) { %>
          <div class="pms-assignment-datapath pms-editable">
            <%= valueOrPlaceholder(datapath) %>
          </div>
        <% } else { %>
          <div class="pms-assignment-datapath">
            <%= valueOrPlaceholder(datapath) %>
          </div>
        <% } %>
        <div>
          <% if (datapath && datapath != "") { %>
            <button class="hidden-xs btn btn-xs btn-default pms-assignment-copy-datapath-btn">
              <i class="far fa-copy"></i>
            </button>
          <% } %>
        </div>
      </div>
    </dd>
    <dt>Beschreibung</dt>
    <dd>
      <% if(editable && state != "completed" && state != "failed") { %>
        <div class="pms-assignment-description pms-multi-line"><%= 
          formatText(description) %>
        </div>
      <% } else { %>
        <div class="pms-multi-line"><%= 
          formatText(description) %>
        </div>
      <% } %>
    </dd>
    <% if (state != "template") { %>
      <dt>Anzahl</dt>
      <dd>
        <% if (state != "completed" && state != "failed") { %>
        <button class="btn btn-xs btn-default pms-reduce-count-btn" title="Anzahl verringern"
            <% if (!editable || (board_count <= 0)) { %>
              disabled
            <% } %> >
          <i class="fas fa-minus"></i>
        <% } %>
        </button>
        <% if (editable && state != "completed" && state != "failed") { %>
          <span class="pms-assignment-board-count-field pms-editable">
            <%= board_count %>
          </span>
        <% } else { %>
          <%= board_count %>
        <% } %>
        <% if (state != "completed" && state != "failed") { %>
          <button class="btn btn-xs btn-default pms-raise-count-btn" title="Anzahl erhöhen"
            <% if (!editable) { %>
              disabled
            <% } %> >
            <i class="fas fa-plus"></i>
          </button>
        <% } %>
      </dd>
    <% } %>

    <% if (state != "template") { %>
      <dt>Fällig am</dt>
      <dd>
        <% if (editable && state != "completed" && state != "failed") { %>
          <span class="pms-assignment-due">
        <% } else { %>
          <span>
        <% } %>
           <%= prettyDate(due) %>
          </span>
        <% if (overdue) { %>
          <i class="fas fa-exclamation-triangle pms-overdue-icon"
             title="Dieser Auftrag ist überfällig">
          </i>
        <% } %>
      </dd>

      <dt>Priorität</dt>
      <dd>
        <div class="pms-assignment-priority">
        <% if (state != "completed" && state != "failed" && priority != 1) { %>
          <button class="btn btn-xs btn-default pms-reduce-priority-btn" title="Priorität verringern"
            <% if (!editable || (priority == 5 && !highest_prio_editable)) { %>
              disabled
            <% } %> >
            <i class="fas fa-minus"></i>
          </button>
        <% } %>

        <% if (priority == 1) { %>
            Sehr niedrig
        <% } else if (priority == 2) { %>
            Niedrig
        <% } else if (priority == 3) { %>
            Standard
        <% } else if (priority == 4) { %>
            Hoch
        <% } else if (priority == 5) { %>
            Sehr hoch
        <% } %>

        <% if (state != "completed" && state != "failed" && priority != 5 ) { %>
          <button class="btn btn-xs btn-default pms-raise-priority-btn" title="Priorität erhöhen"
            <% if (!editable || (priority == 4 && !highest_prio_editable)) { %>
              disabled
            <% } %> >
            <i class="fas fa-plus"></i>
          </button>
        <% } %>
        </div>
      </dd>
    <% } %>

    <dt>Erstellt</dt>
    <dd>
      <span class="pms-assignment-created">
        <%= prettyDate(created_at) %>
      </span>
    </dd>

    <dt>Zuletzt geändert</dt>
    <dd>
      <span class="pms-assignment-updated">
        <%= prettyDate(updated_at) %>
      </span>
    </dd>

    <% if (state == "completed" || state == "failed") { %>
      <dt>Abgeschlossen am</dt>
      <dd>
        <span class="pms-assignment-completed">
          <%= prettyDate(completed_at) %>
        </span>
      </dd>
    <% } %>
  </dl>
</div>

<% if (editable) { %>
<div id="pms-assignment-cancel-dialog" class="modal fade" data-backdrop="false">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Grund für Abbruch:</h4>
      </div>
      <div class="modal-body">
        <textarea class="pms-assignment-cancel-reason pms-multi-line"
          id="pms-assignment-cancel-reason"></textarea>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-warning pms-cancel-run-btn"
                title="Aktuellen Durchlauf abbrechen und Kopie erzeugen, um von vorne zu beginnen">
          Durchlauf abbrechen
        </button>
        <button type="button" class="btn btn-danger pms-cancel-assignment-btn"
                title="Gesamten Auftrag beenden">
          Auftrag abbrechen
        </button>
        <button type="button" class="btn btn-default"
                title="Dialog schließen"
                data-dismiss="modal">
          Dialog schließen
        </button>

      </div>
    </div>
  </div>
</div>
<% } %>