# Provides a JSON API for creating, updating and deleting tasks within
# process templates.
class Api::TaskTemplatesController < ApplicationController
  before_action :require_permission, only: [:create, :update, :destroy]

  # Add a task to a process template.
  #
  # task_template - Task template attributes (all mandatory).
  #                 :name                - The task name.
  #                 :process_template_id - The ID of the process template
  #                                        to add to.
  #
  # Returns the created task as JSON.
  def create
    new_task_template = TaskTemplate.create!(permitted_ini_params)
    new_task_template.process_template.touch
    render json: new_task_template
  end

  # Update a task within a process template.
  #
  # id            - The ID of the task template to update.
  # task_template - Task template attributes (all optional).
  #                 :name - The task's new name.
  #                 :instruction - The task's new instruction.
  #                 :position    - The task's new position within
  #                                the process template. Other
  #                                task templates are moved as
  #                                needed.
  #
  # Returns the updated task as JSON. If task_template[:position] was
  # specified and other tasks' positions changed, these are returned,
  # too.
  def update
    template = TaskTemplate.find(params[:id])
    #template.update!(permitted_params)
    updated_template = new_update_action(template, permitted_params)
    template.process_template.touch
    render json: template
  end

  # Delete a task within a process template.
  #
  # id - The ID of the task template to delete.
  #
  # Returns nothing.
  def destroy
    template = TaskTemplate.find(params[:id])
    template.destroy!
    template.process_template.touch
    render json: {}
  end

  private

  def require_permission
    return if current_user.can_edit_templates?
    render(status: :forbidden, nothing: true)
  end

  def permitted_params
    params.require(:task_template)
      .permit(:name, 
              :instruction,
              :position,
              :process_template_id,
              :machine,
              :remove_machine,
              :article,
              :remove_article,
              operator: [:id, :type])
  end

  def permitted_ini_params
    params.require(:task_template)
      .slice(:name, :instruction, :position, :process_template_id, :operator_id)
      .permit!
  end

   # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
  def new_update_action(template, params)
    if params[:instruction]
      updated_template = template.update!(instruction: params[:instruction])
    elsif params[:name]
      updated_template = template.update!(name: params[:name])
    elsif params[:operator]
      case params[:operator][:type]
      when "user" then operator_class = User
      when "operator_group" then operator_class = OperatorGroup
      end
      newOperator = operator_class.find(params[:operator][:id])
      updated_template = template.update!(operator: newOperator)
    elsif params[:machine]
      template.template_machines << Machine.find(params[:machine])
      updated_template = template.save!
    elsif params[:remove_machine]
      template.template_machines.delete(Machine.find(params[:remove_machine]))
      updated_template = template
    elsif params[:article]
      template.template_articles << Article.find(params[:article])
      updated_template = template.save!
    elsif params[:remove_article]
      template.article_task_templates.delete(ArticleTaskTemplate.find(params[:remove_article]))
      updated_template = template
    elsif params[:position]
      updated_template = template.update!(position: params[:position])
    elsif params[:process_template_id]
      updated_template = template.update!(process_template_id: params[:process_template_id])
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
end
