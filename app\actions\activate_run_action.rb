# Executed if a planner (re)activates an assignment's latest run.
class ActivateRunAction < Action
  # Initializes the action.
  #
  # run - The run to activate. It must be a draft or paused.
  def initialize(run)
    super(run)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    old_state = subject.state
    subject.update!(state: "active")
    mark_changed(subject.assignment) # now also active
    case old_state
    when "draft"
      notify(:run_activated, subject)
      notify(:run_activated_observer, subject)
    when "paused"
      notify(:run_reactivated, subject)
      notify(:run_reactivated_observer, subject)
    else 
      fail "Cannot activate run in state '#{old_state}'"
    end
    notify_operators_of_completable_tasks(old_state)
    notify_operators_of_future_completable_tasks(old_state)
    notify_planners_of_completable_tasks(old_state)
    notify_planners_of_completable_processes(old_state)
    notify_planners_about_unassigned_tasks
    notify_observers_of_completable_tasks(old_state)
    notify_observers_of_completable_processes(old_state)
    notify_observers_about_unassigned_tasks
  end

  private

  def notify_operators_of_completable_tasks(old_state)
    completable_tasks.select(&:operator).each do |task|
      case task.operator
        when User
          case old_state
            when "draft" then notify(:first_task_ready, task)
            when "paused" then notify(:task_ready, task)
            else fail "Cannot activate run in state '#{old_state}'"
          end
        when OperatorGroup
          case old_state
            when "draft" then notify(:first_group_task_ready, task)
            when "paused" then notify(:group_task_ready, task)
            else fail "Cannot activate run in state '#{old_state}'"
          end
        else fail "Cannot activate run in state '#{old_state}'"
      end
    end
  end

  def notify_operators_of_future_completable_tasks(old_state)
    future_tasks.select(&:operator).group_by {|t| [t.operator_id, t.operator_type]}.each do |operator, tasks|
      case operator[1]
        when "User"
          case old_state
            # TODO FIX ME: I'm not the right assigning user
            when "draft" then notify(:operator_assigned, tasks, subject.assignment.planners.first)
            when "paused" then notify(:operator_assigned, tasks, subject.assignment.planners.first)
            else fail "Cannot activate run in state '#{old_state}'"
          end
        when "OperatorGroup"
          case old_state
            # TODO FIX ME: I'm not the right assigning user
            when "draft" then notify(:group_assigned, tasks, subject.assignment.planners.first)
            when "paused" then notify(:group_assigned, tasks, subject.assignment.planners.first)
            else fail "Cannot activate run in state '#{old_state}'"
          end
        else fail "Cannot activate run in state '#{old_state}'"
      end
    end
  end

  def notify_planners_of_completable_tasks(old_state)
    completable_tasks.each do |task|
      case old_state
      when "draft" then notify(:first_task_ready_planner, task)
      when "paused" then notify(:task_ready_planner, task)
      else fail "Cannot activate run in state '#{old_state}'"
      end
    end
  end

  def notify_planners_of_completable_processes(old_state)
    completable_processes.each do |process|
      case old_state
      when "draft" then notify(:process_ready, process)
      when "paused" then notify(:process_ready, process)
      else fail "Cannot activate run in state '#{old_state}'"
      end
    end
  end

  def notify_planners_about_unassigned_tasks
    completable_tasks.select { |t| !t.operator }.each do |task|
      notify(:no_operator_assigned, task)
    end
  end

  def notify_observers_of_completable_tasks(old_state)
    completable_tasks.each do |task|
      case old_state
      when "draft" then notify(:first_task_ready_observer, task)
      when "paused" then notify(:task_ready_observer, task)
      else fail "Cannot activate run in state '#{old_state}'"
      end
    end
  end

  def notify_observers_of_completable_processes(old_state)
    completable_processes.each do |process|
      case old_state
      when "draft" then notify(:process_ready_observer, process)
      when "paused" then notify(:process_ready_observer, process)
      else fail "Cannot activate run in state '#{old_state}'"
      end
    end
  end

  def notify_observers_about_unassigned_tasks
    completable_tasks.select { |t| !t.operator }.each do |task|
      notify(:no_operator_assigned_observer, task)
    end
  end

  def completable_tasks
    subject.completable_tasks
  end

  def future_tasks
    subject.future_tasks
  end

  def completable_processes
    subject.completable_processes
  end
end
