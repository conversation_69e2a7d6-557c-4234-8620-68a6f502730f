App.ProcessInsertMarkers = Backbone.View.extend
  ###
  ###

  #
  # Constants
  #

  CHILD_INDEX_ATTR: 'data-pms-child-index'
  DRAG_TYPE: 'applicaton/vnd.pms-process-id'

  #
  # Backbone.View options
  #

  className: 'pms-process-insert-markers'

  events:
    'dragenter': '_showMarkers'
    'dragleave': '_hideMarkers'
    'dragenter .pms-process-insert-marker': '_highlightMarker'
    'dragover .pms-process-insert-marker': '_makeMarkerDroppable'
    'dragleave .pms-process-insert-marker': '_unhighlightMarker'
    'dragend .pms-process-insert-marker': '_hide'
    'drop .pms-process-insert-marker': '_moveProcess'

  #
  # Methods
  #

  initialize: (options) ->
    ###
    Override of Backbone.View#initialize.
    ###
    @_graph = options.graph
    @_processPosition = options.processPosition
    @render()

  render: ->
    ###
    Override of Backbone.View#render.
    ###
    @_setProcessNameAttribute()
    @_position()
    @_renderMarkers()

  _setProcessNameAttribute: ->
    @$el.attr('data-pms-process-name', @model.get('name'))

  _position: (options) ->
    @$el.css('position', 'absolute')
    @$el.css('left', @_processPosition.x - @_graph.DOT_RADIUS)
    @$el.css('top', @_processPosition.y + @_graph.DOT_RADIUS)
    @$el.css('width',  @_graph.BRANCH_SPACING * (@_numMarkers()))
    @$el.css('height', @_graph.DOT_RADIUS * 2)

  _renderMarkers: ->
    nchildren = @model.get('children').length
    for childIndex in [0...@_numMarkers()]
      @_renderMarker(childIndex)

  _numMarkers: ->
    # We need one marker per child of the process (so that the user
    # can move another process to any of the branches) plus another
    # one so that another process can be moved to a new branch.
    @model.get('children').length + 1

  _renderMarker: (childIndex) ->
    marker = $('<div class="pms-process-insert-marker"></div>')
    marker.attr(@CHILD_INDEX_ATTR, childIndex)
    marker.css('position', 'absolute')
    marker.css('width', @_graph.DOT_RADIUS * 2)
    marker.css('height', @_graph.DOT_RADIUS * 2)
    marker.css('left', childIndex * @_graph.BRANCH_SPACING)
    marker.css('top', 0)
    marker.css('border-radius', @_graph.DOT_RADIUS)

    marker.attr('data-toggle', 'popover')
    marker.popover(
      trigger: 'manual',
      placement: 'top',
      container: 'body',
      animation: false,
      html: true,
      content: @_markerPopoverMessage(childIndex))

    marker.appendTo(@$el)

  _markerPopoverMessage: (childIndex) ->
    children = @model.get('children')
    child = children.at(childIndex)
    if child
      _.template(
        '<small>Vor <b><%- childName %></b> verschieben</small>'
      )(childName: child.get('name'))
    else if children.length > 0
      _.template(
        '<small>In neuen Zweig unter <b><%- name %></b> verschieben</small>'
      )(name: @model.get('name'))
    else
      _.template(
        '<small>Unter <b><%- name %></b> verschieben</small>'
      )(name: @model.get('name'))

  _show: ->
    @$('.pms-process-insert-marker').show()

  _hide: ->
    @$('.pms-process-insert-marker').hide()

  _highlightDroppable: (marker) ->
    marker.addClass('draghover')
    marker.popover('show')

  _unhighlightDroppable: (marker) ->
    marker.removeClass('draghover')
    marker.popover('hide')

  #
  # Event Handlers
  #

  _showMarkers: (event) ->
    @$el.addClass('draghover')
    @_show()

  _hideMarkers: (event) ->
    @$el.removeClass('draghover') if event.target == @el
    if !@$el.hasClass('draghover') and @$('.draghover').length == 0
      @_hide()

  _highlightMarker: (event) ->
    if _.include(event.originalEvent.dataTransfer.types, @DRAG_TYPE)
      @_highlightDroppable($(event.target))
      event.preventDefault() # allow drop

  _makeMarkerDroppable: (event) ->
    if _.include(event.originalEvent.dataTransfer.types, @DRAG_TYPE)
      event.preventDefault() # allow drop

  _unhighlightMarker: (event) ->
    @_unhighlightDroppable($(event.target))

  _moveProcess: (event) ->
    @_unhighlightDroppable($(event.target))
    @_hide()
    otherId = event.originalEvent.dataTransfer.getData(@DRAG_TYPE)
    return if otherId == @model.id
    other = App.Process.find(otherId)
    childIndex = $(event.target).attr(@CHILD_INDEX_ATTR)
    other.insertAfter(@model, childIndex)
