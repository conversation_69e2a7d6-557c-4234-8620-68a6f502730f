#=require "views/SearchDialogItem"

App.OperatorSearchResultItem = App.SearchDialogItem.extend
  ###
  An item in the OperatorSearchDialog search results.
  ###

  #
  # Marionette.ItemView options
  #

  template: JST['OperatorSearchResultItem']

  #
  # Methods
  #

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    if @model instanceof App.OperatorGroup
      users = @model.get('users')
      _.extend(@model.toJSON(), usernames: users.pluck('username'))
    else
      @model.toJSON()
