#=require "models/ArticleTask"
#=require "collections/ArticleTaskCollection"
#=require "views/ArticleTaskView"
#=require "views/behaviors/ConditionalClassNames"
#=require "views/behaviors/Editable"
#=require "views/behaviors/NameDataAttribute"
#=require "views/support/TemplateHelpers"

App.ArticleTaskHolder = Marionette.CollectionView.extend
  ###
  Displays a article_task on an assignment page.
  ###

  #
  # Backbone.View options
  #

  tagName: 'tbody'

  # className: 'pms-task'

  events:
    'click .pms-task-article-delete-btn': '_removeArticleTask'


  #
  # Marionette.ItemView options
  #

  # template: JST['ArticleTaskView']

  modelEvents:
  #   'change:completed' : 'render'
  #   'change:operator' : 'render'
  #   'change:completable': 'render'
  #   'change:completion_undoable': 'render'
    'change:article_tasks' : 'render'

  #
  # Marionette.CollectionView options
  #

  childView: App.ArticleTaskView
  childViewOptions: (model, index) ->
    return {editable: (@model.get('editable') || (@model.get('reassignable') && App.user?.isAssignedTo(@model)))}
  childViewContainer: '.pms-articles'


  initialize: ->
    @collection = @model.get('article_tasks')
    
  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###

    _.extend @model.toJSON(),
      assignedToUser: App.user?.isAssignedTo(@model)
      operator: @model.get('operator')?.toJSON() || null
      completed_by: @model.get('completed_by')?.toJSON() || null,
      assignmentState: @model.get('process').get('run').get('state')

  _removeArticleTask: (event) ->
    articleTaskId = $(event.currentTarget).data('article-task-id')
    @collection.remove({id: articleTaskId}, {wait: true})
    @model.save(
          task: {remove_article: articleTaskId},
          {patch: true, wait: true})