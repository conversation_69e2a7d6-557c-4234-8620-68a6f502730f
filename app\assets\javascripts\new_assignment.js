//= require angular

angular.module("pms.newAssignment",  [])

.controller("NewAssignmentController", function($scope) {
  if (window.data)
    $scope.templates = window.data;
  $scope.searchText = "";

  $scope.newAssignment = {
    name: "",
    project: "",
    templateId: null,
    description: "",
    due: "",
    priority: 3
  };

  $scope.selectEmptyAssignment = function() {
    $scope.newAssignment.templateId = null;
    $scope.newAssignment.templateName = "";
    $scope.newAssignment.name = "";
    $scope.newAssignment.project = "";
  };

  $scope.selectTemplate = function(template) {
    $scope.newAssignment.templateId = template.id;
    $scope.newAssignment.templateName = template.name;
    $scope.newAssignment.name = template.name;
    $scope.newAssignment.description = template.description;
    $scope.newAssignment.project = "";
    $scope.newAssignment.priority = template.priority;
  };

  $scope.createAssignment = function() {
    if (window.data)
      $("#pms-new-assignment-modal form").submit();
    else
      $("#pms-new-assignment-dialog-modal form").submit();
  };

  $("#pms-new-assignment-modal").on("shown.bs.modal", function() {
    var projectInput = $(this).find("input[name=project]");
    projectInput.focus();
  });
});
