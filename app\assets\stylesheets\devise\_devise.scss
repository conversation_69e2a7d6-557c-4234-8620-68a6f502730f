// Styles for the pages controlled by the <PERSON><PERSON> gem (login, password
// change, forgot password).

.pms-login-page {
  @extend .modal;
  display: block;
}

.pms-login-dialog {
  @extend .modal-dialog;
}

.pms-login-dialog-content {
  @extend .modal-content;
}

.pms-login-dialog-header {
  @extend .modal-header;
  background-color: $fraunhofer-green;
  /**
   * Add rounded corners, which are normally defined by .model-body,
   * for modal-header necessary when background-color is used.
   */
  -webkit-border-top-left-radius: 5px;
  -webkit-border-top-right-radius: 5px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-topright: 5px;
   border-top-left-radius: 5px;
   border-top-right-radius: 5px;

   .modal-title {
    color: white;
    font-style: bold;
   }
}

.pms-login-dialog-body {
  @extend .modal-body;
}

.pms-login-alert {
  @extend .alert;
  @extend .alert-danger;
}

.pms-login-form {
  @extend .form-horizontal;
}

.pms-login-form-field {
  @extend .form-group;

  label {
    @extend .col-lg-3;
    @extend .control-label;
  }

  div {
    @extend .col-lg-9;
  }

  input, textarea {
    @extend .form-control;
  }
}

.pms-login-remember-me {
  @extend .form-group;

  div {
    @extend .col-lg-offset-3;
    @extend .col-lg-9;
  }
}