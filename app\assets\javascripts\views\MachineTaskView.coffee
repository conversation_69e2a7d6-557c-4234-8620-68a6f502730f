#=require "models/MachineTask"
#=require "views/MachineView"
#=require "views/behaviors/Editable"
#=require "views/support/TemplateHelpers"

App.MachineTaskView = Marionette.CompositeView.extend
  ###
  Displays a task template in the admin area.
  ###

  #
  # Backbone.Model optins
  #

  className: 'pms-task-machines'

  # events:
  #   'click .pms-responsible-assign-btn': '_showAssignResponsibleDialog'
  #   'click .pms-deputy-assign-btn': '_showAssignDeputyDialog'
    
  #
  # Marionette.View options
  #

  # ui:
  #   nameInput: '.pms-task-name-input'
  #   instructionInput: '.pms-task-instruction-input'
  # 
  # behaviors:
  #   Editable:
  #     name:
  #       type: 'string'
  #       target: '.pms-task-name'
  #       inputClass: 'form-control pms-task-name-input'
  #     instruction:
  #       type: 'text'
  #       target: '.pms-task-instruction'
  #       inputClass: 'form-control pms-task-instruction-input'

  # templateHelpers:
  #   prettyUser: App.TemplateHelpers.prettyUser
  #   prettyUserMachine: App.TemplateHelpers.prettyUserMachine
    
  #
  # Marionette.ItemView options
  #

  template: JST['MachineTaskView']

  # modelEvents:
  #   'change:responsible' : 'render'
  #   'change:deputy' : 'render'

  #
  # Marionette.CollectionView options
  #

  childView: App.MachineView
  childViewContainer: '.pms-machines'

  #
  # Methods
  #

  initialize: ->
  #  @collection = @model.get('tasks')

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      machine_ident: @model.get('machine')?.toJSON() || null

  #
  # Event handlers
  #

  # _showAssignResponsibleDialog: (event) ->
  #   event.preventDefault()
  #   new App.OperatorSearchDialog().show (responsible) =>
  #       if responsible instanceof App.User
  #         $('#machine_responsible_id').val(responsible.id)
  #         operatorSpec = {id: responsible.id, type: responsible.get('type')}
  #         @model.set({responsible: operatorSpec})

  # _showAssignDeputyDialog: (event) ->
  #   event.preventDefault()
  #   new App.OperatorSearchDialog().show (deputy) =>
  #       if deputy instanceof App.User
  #         $('#machine_deputy_id').val(deputy.id)
  #         operatorSpec = {id: deputy.id, type: deputy.get('type')}
  #         @model.set({deputy: operatorSpec})
