#=require "jquery.ui.core"
#=require "jquery.ui.datepicker"
#=require "jquery.ui.sortable"
#
#=require "underscore"
#=require "backbone"
#=require "backbone-relational"
#=require "backbone.marionette"
#
#=require "./vendor/moment"
#=require "./vendor/moment-de"
#
#=require_tree "../templates"
#
#=require "modernizr-custom"

# Create the application root.
window.App = new Marionette.Application()

# Define view regions.
App.addRegions
  mainRegion: '#main'
  notificationsRegion: '#notifications'

# Create a namespace for Marionette view behaviors.
App.Behaviors = {}
Marionette.Behaviors.behaviorsLookup = -> App.Behaviors

# Extend Backbone's sync() to add a CSS class "pms-wait" to the body
# tag while a POST, PUT or DELETE request is in progress. This is used
# to indicate to the user that an action is being applied.
BackboneSync = Backbone.sync
Backbone.sync = (method, model, options) ->
  if method != 'read'
    $(document.body).addClass('pms-wait')
    return BackboneSync.call(Backbone, method, model, options).done =>
      $(document.body).removeClass('pms-wait')
  else
    return BackboneSync.call(Backbone, method, model, options)

App.onSmallScreen = ->
  ###
  Return true if PMS is currently displayed in smartphone size.
  ###
  window.innerWidth < 768 # size "xs" in Bootstrap

App.on 'start', ->
  # Tell moment.js to use German date formats.
  moment.lang('de');

  # Tell backbone-relational where to find related model classes which
  # are referenced through strings (e.g. "Task").
  Backbone.Relational.store.addModelScope(App)