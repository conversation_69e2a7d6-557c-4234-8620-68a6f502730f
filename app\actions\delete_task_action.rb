# Executed if a user deletes a task from a process.
class DeleteTaskAction < Action
  # Initializes the action.
  #
  # task - The task to delete.
  def initialize(task)
    super(task)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.destroy!
    handle_possibly_moved_tasks_in_process
    handle_possibly_completed_process
    handle_possibly_completed_run
  end

  private

  def handle_possibly_moved_tasks_in_process
    other_tasks = subject.process.tasks - [subject]
    other_tasks.each { |s| mark_changed(s) }
  end

  def handle_possibly_completed_process
    return unless subject.process.completed?
    subject.process.update!(process_completed: true)
    mark_changed(subject.process)
    handle_now_completable_child_processes
  end

  def handle_now_completable_child_processes
    subject.process.children.each do |child|
      mark_changed(child)
      mark_changed(child.tasks.first)
    end
  end

  def handle_possibly_completed_run
    run = subject.process.run
    return unless run.done? && run.state != 'draft' && run.state != 'template'
    update!(run, state: "completed")
    update!(run, completed_at: Time.now)
    mark_changed(run.assignment)
  end
end
