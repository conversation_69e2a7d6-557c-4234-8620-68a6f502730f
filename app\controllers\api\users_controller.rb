# Provides a JSON API for searching for users.
class Api::UsersController < ApplicationController
  # Search for users by username, first name and last name.
  #
  # search - The search term.
  #
  # Returns all matching users as JSON.
  def index
    if params[:search].present?
      by_username = search_by("username")
      by_first_name = search_by("first_name")
      by_last_name = search_by("last_name")
      result = (by_username + by_first_name + by_last_name).uniq
    else
      # Don't return all users at once to avoid too much traffic
      result = []
    end
    render json: result
  end

  private

  def search_by(attribute)
    User.active
        .includes(:workgroup)
        .where("lower(#{attribute}) like lower(?)", "#{params[:search]}%")
        # .where(workgroups: { extern: false }) # keep semantics as-is; uncomment if needed
  end
end
