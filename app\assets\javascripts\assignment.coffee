#=require "./_backbone"
#=require "dymo.connect.framework.full_fix"

#=require "models/Assignment"
#=require "views/AssignmentPage"
#=require "views/NotificationBar"
#=require "views/AssignmentNoAccessView"

# JS to enable delayed plugin startup, with IE browser switch
`if (window.navigator.userAgent.indexOf("Trident") !== -1) { var dymoprint = 1; } else { var dymoprint = 0;
var url = new URL(window.location.href);
if (url.searchParams.get("dymoprint") == 1) { dymoprint = 1; } }`

initializeAssignmentPage = ->
  if (dymoprint == 1)
    App.BoxLabelService.initialize ->
      parseParameters()
      renderAssignmentPage()
  else
    parseParameters()
    renderAssignmentPage()

parseParameters = ->
  App.user = new App.User(App.user, parse: true)
  App.assignment = new App.Assignment(App.assignment, parse: true)

  taskLink = document.location.hash.match(/#aufgabe(\d+)/)
  if taskLink
    taskId = Number(taskLink[1])
    App.taskToJumpTo = taskId

renderAssignmentPage = ->
  if App.user.get('extern') && App.user.get('workgroup_id') not in
        App.assignment.get('extern_workgroups').pluck('id')
    page = new App.AssignmentNoAccessView(model: App.assignment)
  else
    page = new App.AssignmentPage(model: App.assignment)
  page.render()
  App.mainRegion.show(page)

App.addInitializer ->
  setTimeout(initializeAssignmentPage, 0)
  App.notificationsRegion.show(new App.NotificationBar())
