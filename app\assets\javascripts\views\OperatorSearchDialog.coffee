#=require "collections/OperatorSearchResultCollection"
#=require "views/OperatorSearchResultItem"
#=require "views/SearchDialog"

App.OperatorSearchDialog = App.SearchDialog.extend
  ###
  A dialog that allows a user to search for and assign a user or
  operator group to a task.
  ###

  #
  # Marionette.CollectionView options
  #

  childView: App.OperatorSearchResultItem

  #
  # App.SearchDialog options
  #

  title: 'Aufgabe zuweisen'
  searchLabel: 'Benutzer- oder Gruppenname'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    App.SearchDialog::initialize.call(this)
    @collection = new App.OperatorSearchResultCollection()
