# Represents a user in the PMS system.
#
# Note that an account can be *deactivated* if a user leaves the IZM.
# This prevents the user from logging in and removes the possibility
# to assign the user to a task.
class User < ActiveRecord::Base

# Further Option
# (?=.*[[:^alnum:]]) # Must contain a symbol
# (?=.{8,})          # Must contain 8 or more characters
# (?=.*[A-Z])        # Must contain an upper case character
  PASSWORD_FORMAT = /\A
    (?=.*\d)           # Must contain a digit
    (?=.*[a-z])        # Must contain a lower case character
  /x

  belongs_to :workgroup
  #has_and_belongs_to_many :operator_groups
  has_many :operator_group_users
  has_many :operator_groups, through: :operator_group_users

  has_many :planner_memberships,
           -> { where(role: "planner") },
           foreign_key: "member_id",
           class_name: "AssignmentMembership"
  has_many :assignments,
           through: :planner_memberships
  has_many :tasks, foreign_key: "operator_id"

  has_many :responsible_machines, class_name: "Machine", foreign_key: "responsible_id"
  has_many :deputy_machines, class_name: "Machine", foreign_key: "deputy_id"

  validates :username, presence: true, uniqueness: true
  validates :first_name, presence: true
  validates :last_name, presence: true
  validates :email, presence: true, uniqueness: true
  validates :password, length: { minimum: 6 },  format: {with: PASSWORD_FORMAT},
            unless: "password.nil?", confirmation: true
  devise :database_authenticatable, :recoverable, :rememberable, :registerable

  # Query for users which are not deactivated.
  #
  # Returns the users as an ActiveRecord::Relation.
  scope :active, -> { where(deactivated: false) }

  # Method used by Devise to check whether a user account is active.
  #
  # Returns false if the user has been deactivated, true otherwise.
  def active_for_authentication?
    !deactivated?
  end

  # Generates the user's full name from the first and last name.
  # Useful for display purposes.
  #
  # Returns the user's full name as string.
  def name
    "#{first_name} #{last_name}"
  end

  # Determine whether the user has currently any responsiblity in a
  # specific assignment, such as by being its planner or having a task
  # assigned that is still open. Past responsibilities (e.g. assigned
  # tasks that have already been completed) do not count as current
  # involvement.
  #
  # assignment - The assignment to check current involvment for.
  #
  # Returns true if the user is currently involved in the assignment,
  #   or false if not.
  def currently_involved?(assignment)
    assignment.has_work_for_user?(self) || assignment.planners.include?(self)
  end
end
