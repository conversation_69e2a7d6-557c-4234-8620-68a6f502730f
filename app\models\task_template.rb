# Represents a task in a ProcessTemplate.
class TaskTemplate < ActiveRecord::Base
  # Associations
  belongs_to :process_template
  belongs_to :operator, polymorphic: true
  
  has_many :machine_task_templates
  has_many :template_machines, through: :machine_task_templates, :source => :machine
  has_many :article_task_templates
  has_many :template_articles, through: :article_task_templates, :source => :article

  # Validations
  validates :name, presence: true
  validates :process_template, presence: true
  before_save :fill_in_defaults

  acts_as_list scope: :process_template

  private

  def fill_in_defaults
    self.instruction ||= ""
  end
end
