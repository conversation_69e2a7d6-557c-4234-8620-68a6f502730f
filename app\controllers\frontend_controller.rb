# -*- coding: utf-8 -*-

# Serves the PMS user interface. For the admin area, see the
# controllers in the admin/ subdirectory.
class FrontendController < ApplicationController
  layout "layouts/main"
  before_action :authenticate_user!, except: [:index]

  # Redirect either to the my-assignments or the login page, depending
  # on whether the user is logged in.
  #
  # Returns a redirect.
  def index
    if user_signed_in?
      @extern = current_user.workgroup.extern
      redirect_to(:my_assignments)
    else
      # `new_user_session_path` comes from Devise.
      redirect_to(new_user_session_path)
    end
  end

  # Display a list of all non-failed assignments in which the current
  # user is involved, either as planner or as operator.
  #
  # Returns a page based on the "frontend/assignments" template.
  def my_assignments
    @overview_tab = :my_assignments
    @extern = current_user.workgroup.extern

    @operator_groups = current_user.operator_groups.pluck(:id).join(', ')
    @operator_groups = "0" if @operator_groups == ""

    # Read preferences only from cookies (URL stays clean)
    @completed_range = cookies[:completed_range].presence || '1W'
    @hide_user_work = cookies[:hide_user_work] == '1'

    assignments = Assignment.find_by_sql(sql_get_my_assignments(@completed_range))

    @active_assignments_highest_priority = assignments.select { |a| a.assign_state == "active" && a.priority == 5}
    @active_assignments = assignments.select { |a| a.assign_state == "active" && a.priority < 5}
    @draft_assignments = assignments.select { |a| a.assign_state == "draft" }
    @paused_assignments = assignments.select { |a| a.assign_state == "paused" }
    @completed_assignments = assignments.select { |a| a.assign_state == "completed" }

    @active_projects_highest_priority = @active_assignments_highest_priority.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    @active_projects = @active_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    @draft_projects = @draft_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    @paused_projects = @paused_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    @completed_projects = @completed_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    
    ids = assignments.map(&:id)
    @immediate_work_ids = Assignment.immediate_work_for_user_ids(ids, current_user).to_set
    @any_work_ids       = Assignment.any_work_for_user_ids(ids, current_user).to_set
  end

  # Display a list of active assignments in the system which:
  #   (1) are planned by a member of the current user's workgroup
  #   (2) or in which the user is involved as an operator.
  #
  # Returns a page based on the "frontend/assignments" template.
  def active_assignments
    @overview_tab = :active_assignments
    @extern = current_user.workgroup.extern

    if @extern
      redirect_to(:my_assignments)
    else
      @operator_groups = current_user.operator_groups.pluck(:id).join(', ')
      @operator_groups = "0" if @operator_groups == ""

      @assignments = Assignment.find_by_sql(sql_active_assignments)

      ids = @assignments.map(&:id)
      @immediate_work_ids = Assignment.immediate_work_for_user_ids(ids, current_user).to_set
      @any_work_ids       = Assignment.any_work_for_user_ids(ids, current_user).to_set

      @projects = @assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    end
  end

  # Display a list of machines in the system with the
  # assignments of the users workgroups assigned to the machine.
  def active_machines
    @overview_tab = :active_machines
    @extern = current_user.workgroup.extern

    if @extern
      redirect_to(:my_assignments)
    else
      @machine_groups = current_user.workgroup.operator_groups
    end
  end

  # Create a new assignment and redirect to the new assignment's
  # page. The current user is automatically made a planner of the
  # new assignment.
  #
  # name        - The new assignment's name.
  # project     - The new assignment's project name.
  # template_id - The ID of the assignment template to base the
  #               assignment on (optional). If omitted, the assignment
  #               is created with an empty run.
  #
  # Returns a redirect to the #index action.
  def create_assignment
    name = params[:name] || "Neuer Auftrag"
    project = params[:project] || ""
    datapath = params[:datapath] || ""
    description = params[:description] || ""
    due = params[:due] || ""
    template_id = params[:template_id]
    template = template_id ? Assignment.find(template_id) : nil
    priority = params[:priority] || 3

    action = CreateAssignmentAction.new(name,
                                        project,
                                        current_user,
                                        datapath,
                                        description,
                                        due,
                                        priority,
                                        copy_of: template)

    assert_action_allowed(action) || return
    action.execute!

    unless template
      first_process_action = CreateProcessAction.new("Prozess 1", action.created_record.runs.first)
      assert_action_allowed(first_process_action) || return
      first_process_action.execute!

      first_task_action = CreateTaskAction.new(first_process_action.created_record, "Aufgabe 1", current_user)
      assert_action_allowed(first_task_action) || return
      first_task_action.execute!
    end

    redirect_to action: :assignment, id: action.created_record.id
  end

  # Display a page where the user can create a new assignment, either
  # an empty one or one based on a template.
  #
  # Returns a page based on the "frontend/new_assignment" template.
  def new_assignment
    @overview_tab = :new_assignment
    @extern = current_user.workgroup.extern

    if @extern
      redirect_to(:my_assignments)
    else
      @templates = Assignment.joins(:runs).where("runs.state" => "template").order(:name)
    end
  end

  # Display a list of completable processes across all assignments in the system
  # that are relevant to the users workgroup. This is the case either when
  #   - a user from the users workgroup is part of a process
  # The processes are ordered by operator_groups from the users workgroup
  #
  # Returns a page based on the "frontend/open_tasks" template.
  def open_tasks
    @overview_tab = :open_tasks
    @extern = current_user.workgroup.extern

    if @extern
      redirect_to(:my_assignments)
    else
      @operator_groups = OperatorGroup
                          .all
                          .joins(:users)
                          .where("users.workgroup_id" => current_user.workgroup_id)
                          .distinct
    end
  end

  # Display a form that lets the user search for assignments by project
  # and assignment name. If a search query is passed, it is processed
  # and the results are displayed as well.
  #
  # q - The search query string (optional). It is matched against the
  #     names, project names and IDs of all assignments that the user is
  #     allowed to see. If not passed, only the search form is displayed.
  #
  # Returns a page based on the "frontend/search_assignments" template.
  def search_assignments
    @overview_tab = :search_assignments
    @extern = current_user.workgroup.extern
    @operator_groups = current_user.operator_groups.pluck(:id).join(', ')
    @operator_groups = "0" if @operator_groups == ""

    @query = params[:q1] || params[:q2]
    # remove all '%' from search query to prevent output of all assignments
    @query.gsub! '%', '' if @query.present?
    @states = []
    if params[:state]
      @states = params[:state]
    end
    if params[:planner]
      @planner = params[:planner]
    end
    if params[:member]
      @member = params[:member]
    end
    if params[:fulltext]
      @fulltext = params[:fulltext]
    end
    if params[:categoryview]
      @categoryview = params[:categoryview]
    end
    if current_user.can_edit_users? && params[:advanced_timeline]
      @advanced_timeline = params[:advanced_timeline]
    else
      @advanced_timeline = false
    end
    @order = params[:order] || "updated_at DESC"
    @orderings = ordering_options

    if !@fulltext && @query =~ /^\d+$/
      # A number is interpreted as an assignment ID (if its not a fulltextsearch) and takes the user
      # directly to the corresponding assignment.
      redirect_to(action: "assignment", id: @query)
    elsif (@query.present? && @query.length >= 2) || @member || @planner
      if @query == "" || @query == nil
        @search_results = sql_search(@order, @states, @member, @planner, @extern)
      elsif @fulltext
        @search_results = sql_search(@order, @states, @member, @planner, @extern, "%"+@query+"%", @fulltext)
      else
        @search_results = sql_search(@order, @states, @member, @planner, @extern, "%"+@query+"%")
      end

      @active_assignments = @search_results.select { |a| a.assign_state == "active" }
      @draft_assignments = @search_results.select { |a| a.assign_state == "draft" }
      @paused_assignments = @search_results.select { |a| a.assign_state == "paused" }
      @completed_assignments = @search_results.select { |a| a.assign_state == "completed" }
      @template_assignments = @search_results.select { |a| a.assign_state == "template" }
      @failed_assignments = @search_results.select { |a| a.assign_state == "failed" }

      @projects = @search_results.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
      @active_projects = @active_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
      @draft_projects = @draft_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
      @paused_projects = @paused_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
      @completed_projects = @completed_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
      @template_projects = @template_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
      @failed_projects = @failed_assignments.uniq { |a| a.project.downcase.delete(' ') }.map(&:project)
    end
  end

  # Display a page for viewing and interacting with a specific
  # assignment.
  #
  # id - The ID of the assignment to show.
  #
  # Returns a page based on the "frontend/assignment" template.
  def assignment
    @extern = current_user.workgroup.extern

    @assignment = Assignment
      .includes(
        :members, :planners, :observers,
        :operator_groups, :extern_workgroups,
        runs: [
          {
            processes: [
              :run,
              {
                tasks: [
                  :completed_by,
                  :attachments,
                  { operator: :workgroup },
                  { machines: [:responsible, :deputy, :instructed_users_group, :attachments, { location: :building, operator_groups: :users }] },
                  { article_tasks: { article: :unit } }
                ]
              }
            ]
          },
          { assignment: :planners },
        ]
      )
      .find(params[:id])

    if @extern && !@assignment.extern_workgroups.exists?(current_user.workgroup.id)
      logger.warn "[SECURITY] Extern tried to open intern assignment. Extern: #{current_user.name} (#{current_user.id}) | Assignment: ##{@assignment.id}"
      redirect_to(:my_assignments)
    end
  end

  private

  def ordering_options
    [
      ["Abschlussdatum", "completed_at DESC, updated_at DESC"],
      ["Änderungsdatum", "updated_at DESC"],
      ["Erstellungsdatum", "created_at DESC"],
      ["Auftragsname", "name ASC"],
      ["Projektname", "project ASC"],
      ["Priorität", "priority DESC, name ASC"]    ]
  end


  # Query for all assignments
  #
  # Returns the matching assignments as ActiveRecord::Relation.
  def sql_search(order, states, member, planner, extern, substring = '', fulltext = false)
    substring_query = ""
    states_query = ""
    member_planner_query = ""

    # build subqueries for states, member/planner and extern
    if states != []
      count = states.length
      if count == 1
        states_query = [states_query, " AND r1.state = '", states.first, "'"].join
      else
        states_query = [states_query, " AND (r1.state = '", states[count-1], "'"].join
        count = count - 1
        until count == 1
          states_query = [states_query, " OR r1.state = '", states[count-1], "'"].join
          count = count - 1
        end
        states_query = [states_query, " OR r1.state = '", states[count-1], "')"].join
      end
    end

    if member || planner
      member_planner_query = " AND (assignment_memberships.member_id = #{current_user.id} AND assignment_memberships.member_type = 'User'
      OR (assignment_memberships.member_id IN (#{@operator_groups}) AND assignment_memberships.member_type = 'OperatorGroup'))"

      if planner
        member_planner_query = [member_planner_query, " AND assignment_memberships.role = 'planner'"].join
      end
    end

    if extern
      extern_sql = "JOIN workgroups on (workgroups.extern = #{sql_boolean(true)} AND workgroups.id = #{current_user.workgroup_id})
      JOIN "
    else
      extern_sql = "LEFT JOIN "
    end

    if fulltext
      Assignment.find_by_sql [["SELECT result.*, r1.state AS assign_state, r1.completed_at as completed_at
      FROM (SELECT DISTINCT assignments.*, MAX(assignment_memberships.role) AS assignment_role,
          COUNT(Distinct assignment_memberships.role ) AS role_count
        FROM runs r1
        JOIN assignments on assignments.id = r1.assignment_id
        ", extern_sql, "assignment_memberships on (assignment_memberships.assignment_id = assignments.id AND
        ((assignment_memberships.member_id = #{current_user.id} AND assignment_memberships.member_type = 'User')
        OR
         (assignment_memberships.member_id IN (#{@operator_groups}) AND assignment_memberships.member_type = 'OperatorGroup')))
        WHERE Assignments.id IN (SELECT runs.assignment_id FROM runs
          JOIN pms_processes ON runs.id=pms_processes.run_id
          WHERE lower(pms_processes.name) like lower(:s)
            OR lower(pms_processes.instruction) like lower(:s))
            OR Assignments.id IN (SELECT runs.assignment_id FROM runs
              JOIN pms_processes ON runs.id=pms_processes.run_id
              JOIN tasks ON pms_processes.id = tasks.process_id
              WHERE lower(tasks.name) like lower(:s)
                OR lower(tasks.instruction) like lower(:s)
                OR lower(tasks.note) like lower(:s))
                OR lower(assignments.project) like lower(:s)
                OR lower(assignments.name) like lower(:s)
                OR lower(assignments.description) like lower(:s)
                OR lower(assignments.datapath) like lower(:s)
              GROUP BY assignments.name, assignments.id, assignments.project, assignments.datapath, assignments.description, assignments.due,
              assignments.created_at, assignments.updated_at, assignments.priority, assignments.created_by_id, assignments.board_count) as result

        JOIN runs r1 ON result.id = r1.assignment_id
        LEFT JOIN runs r2 ON (result.id = r2.assignment_id AND (r1.version < r2.version))
        WHERE r2.id IS NULL
        ", states_query, member_planner_query, "
        ORDER BY ", order].join, :s => substring]

    else
      if substring != ''
        substring_query = " AND (lower(assignments.project) like lower(:s) OR lower(assignments.name) like lower(:s))"
      end

      Assignment.find_by_sql [["SELECT DISTINCT assignments.*, r1.state AS assign_state, r1.completed_at as completed_at,
          MAX(assignment_memberships.role) AS assignment_role, COUNT(Distinct assignment_memberships.role ) AS role_count
        FROM runs r1
        JOIN assignments on assignments.id = r1.assignment_id
        ", extern_sql, "assignment_memberships on (assignment_memberships.assignment_id = assignments.id AND
        ((assignment_memberships.member_id = #{current_user.id} AND assignment_memberships.member_type = 'User')
        OR
         (assignment_memberships.member_id IN (#{@operator_groups}) AND assignment_memberships.member_type = 'OperatorGroup')))
        LEFT JOIN runs r2 ON (assignments.id = r2.assignment_id AND (r1.version < r2.version))
        WHERE r2.id IS NULL
        ", substring_query, states_query, member_planner_query, "
        GROUP BY assignments.name, assignments.id, assignments.project, assignments.datapath, assignments.description, assignments.due,
          assignments.created_at, assignments.updated_at, assignments.created_by_id, assignments.priority, assignments.board_count, r1.state, r1.completed_at
        ORDER BY ", order].join, :s => substring]
    end
  end

  def sql_get_my_assignments(timeframe = nil)
    <<-SQL
    SELECT Distinct assignments.*, r1.state AS assign_state, MAX(assignment_memberships.role) AS assignment_role,
      COUNT(Distinct assignment_memberships.role ) AS role_count
      FROM assignments
      JOIN assignment_memberships ON assignment_memberships.assignment_id = assignments.id
      JOIN runs r1 ON (assignments.id = r1.assignment_id)
      LEFT OUTER JOIN runs r2 ON (assignments.id = r2.assignment_id AND (r1.version < r2.version))

      LEFT JOIN pms_processes ON r1.id = pms_processes.run_id
      LEFT JOIN tasks ON pms_processes.id = tasks.process_id

      WHERE r2.id IS NULL
      AND r1.state != 'failed'
      AND (
        ((assignment_memberships.member_id = #{current_user.id} AND assignment_memberships.member_type = 'User')
             OR
            (assignment_memberships.member_id IN (#{@operator_groups}) AND assignment_memberships.member_type = 'OperatorGroup'))
        AND r1.state = 'active'
        AND tasks.completed = #{sql_boolean(false)}
        AND ((tasks.operator_id = #{current_user.id} AND operator_type = 'User')
          OR
          (tasks.operator_id IN (#{@operator_groups}) AND operator_type = 'OperatorGroup'))
      OR
        ((assignment_memberships.member_id = #{current_user.id}
         AND assignment_memberships.member_type = 'User'
         AND (assignment_memberships.role = 'planner' OR assignment_memberships.role = 'observer'))
         AND (
            (r1.state = 'completed'
             AND #{sql_timespace(timeframe)}
             )
            OR
            (r1.state != 'completed'
             )
            )
         )
      OR
        (assignment_memberships.member_id = #{current_user.workgroup.id}
         AND assignment_memberships.member_type = 'Workgroup'
        )
      )
      GROUP BY assignments.id, assignments.name, assignments.project, assignments.datapath, assignments.description,
      assignments.due, assignments.created_at, assignments.updated_at, assignments.created_by_id,
      assignments.priority, assignments.board_count, r1.state
      ORDER BY assignments.priority DESC, assignments.project, assignments.name
    SQL
  end

  def sql_get_workgroup_assignments
    <<-SQL
    SELECT distinct assignments.*, runs.state AS assign_state, assignment_memberships.role AS assignment_role,
      1 AS role_count
      FROM assignments
      JOIN runs ON runs.assignment_id = assignments.id
      JOIN assignment_memberships ON assignment_memberships.assignment_id = assignments.id
        AND assignment_memberships.role = 'planner'
        AND assignment_memberships.member_type = 'User'
      JOIN users ON users.id = assignment_memberships.member_id
      WHERE runs.state = 'active'
      AND users.workgroup_id = #{current_user.workgroup_id}
    SQL
  end

  def sql_active_assignments
    <<-SQL
    SELECT Distinct assignments.*, r1.state AS assign_state, MAX(my_membership.role) AS assignment_role,
      COUNT(Distinct my_membership.role) AS role_count
      FROM assignments
      JOIN assignment_memberships ON assignment_memberships.assignment_id = assignments.id
      LEFT JOIN assignment_memberships my_membership ON my_membership.assignment_id = assignments.id
      AND ((my_membership.member_id = #{current_user.id} AND assignment_memberships.member_type = 'User')
        OR
         (assignment_memberships.member_id IN (#{@operator_groups}) AND assignment_memberships.member_type = 'OperatorGroup'))
      JOIN users ON assignment_memberships.member_id = users.id
      JOIN runs r1 ON (assignments.id = r1.assignment_id)
      LEFT OUTER JOIN runs r2 ON (assignments.id = r2.assignment_id AND (r1.version < r2.version))
      LEFT JOIN pms_processes ON r1.id = pms_processes.run_id
      LEFT JOIN tasks ON pms_processes.id = tasks.process_id
      WHERE r2.id IS NULL
      AND r1.state = 'active'
      AND ((((assignment_memberships.member_id = #{current_user.id} AND assignment_memberships.member_type = 'User')
        OR
         (assignment_memberships.member_id IN (#{@operator_groups}) AND assignment_memberships.member_type = 'OperatorGroup'))
        AND tasks.completed = #{sql_boolean(false)}
        AND ((tasks.operator_id = #{current_user.id} AND operator_type = 'User')
        OR (tasks.operator_id IN (#{@operator_groups}) AND operator_type = 'OperatorGroup')))
      OR
        (users.workgroup_id = #{current_user.workgroup.id}
        AND assignment_memberships.member_type = 'User'
        AND (assignment_memberships.role = 'planner' OR assignment_memberships.role = 'observer')))
      GROUP BY assignments.id, assignments.name, assignments.project, assignments.datapath, assignments.description,
      assignments.due, assignments.created_at, assignments.updated_at, assignments.created_by_id,
      assignments.priority, assignments.board_count, r1.state
      ORDER BY assignments.priority DESC, assignments.project, assignments.name
    SQL
  end

  def sql_boolean(bool)
    if bool
      if ActiveRecord::Base.connection.adapter_name == "MSSQL"
        "1"
      else
        "\"t\""
      end
    else
      if ActiveRecord::Base.connection.adapter_name == "MSSQL"
        "0"
      else
        "\"f\""
      end
    end
  end

  def sql_timespace(range_code = nil)
    # Accepts '1W', '1M', '1Y' and defaults to 14 days if nil/unknown
    range_code = (range_code.presence || '1W').upcase

    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      since = case range_code
              when '1W' then DateTime.now - 7.days
              when '1M' then DateTime.now - 1.month
              when '1Y' then DateTime.now - 1.year
              else DateTime.now - 14.days
              end
      "r1.completed_at >= CONVERT(datetime,'#{since.strftime("%Y-%d-%m")}')"
    else
      modifier = case range_code
              when '1W' then "-7 days"
              when '1M' then "-1 month"
              when '1Y' then "-1 year"
              else "-14 days"
              end
      "r1.completed_at >= datetime('now', '#{modifier}')"
    end
  end
end
