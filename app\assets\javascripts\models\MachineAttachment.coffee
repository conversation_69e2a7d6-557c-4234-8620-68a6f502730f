#=require "models/Model"
#=require "models/User"

App.MachineAttachment = App.Model.extend
  ###
  Client-side representation of a MachineAttachment.
  (See app/models/machine_attachment.rb)
  ###

  #
  # Backbone.Model options
  #

  defaults:
    filename: ''
    content_type: 'application/octet-stream'
    created_by_id: ''

  #
  # App.Model options
  #

  jsonSingularRoot: 'machine_attachment'
  jsonPluralRoot: 'machine_attachments'

  #
  # Methods
  #

  urlRoot: ->
    machineId = @get('machine').id
    "/api/machine_attachments/#{machineId}"

  url: ->
    ###
    See Backbone.Model#url
    ###
    filename = @get('filename')
    "#{@urlRoot()}/#{encodeURIComponent(filename)}"

  upload: (file) ->
    ###
    Upload `file` as content of the atttachment.
    ###
    @_sendUploadRequest(file).success (data) =>
      @set(data.machine_attachment)
      @trigger('sync', this)

  _sendUploadRequest: (file) ->
    data = new FormData()
    data.append('file', file)
    $.ajax
      type: 'POST'
      url: @urlRoot()
      data: data
      processData: false
      contentType: false
