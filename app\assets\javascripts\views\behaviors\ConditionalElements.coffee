App.Behaviors.ConditionalElements = Marionette.Behavior.extend
  ###
  Toggles the visibility of certain elements within the view based on
  the value of an attribute of the model, or on an arbitrary condition
  passed as a function. Example:

    behaviors:
      ConditionalElements:
        '.pms-add-task-form': 'editable'
        '.pms-process-tag-assigned': -> @model.hasTaskForUser()
        '.pms-process-tag-near': -> @model.hasNearTaskForUser()
        '.pms-process-tag-immediate': -> @model.hasImmediateTaskForUser()

  Visibility is automatically readjusted whenever the model triggers a
  "change" event.
  ###

  _toggleElements: ->
    for selector, condition of @options
      if _.isFunction(condition)
        enabled = condition.call(@view)
      else
        enabled = !!@view.model.get(condition)
      if enabled
        @view.$(selector).show()
      else
        @view.$(selector).hide()

  onShow: ->
    @listenTo(@view.model, 'change', @_toggleElements)

  onRender: ->
    @_toggleElements()
