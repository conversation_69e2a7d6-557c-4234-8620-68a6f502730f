{"_args": [["estraverse@~1.5.0", "/pms/node_modules/escodegen"]], "_from": "estraverse@>=1.5.0 <1.6.0", "_id": "estraverse@1.5.1", "_inCache": true, "_installable": true, "_location": "/estraverse", "_npmUser": {"email": "<EMAIL>", "name": "constellation"}, "_npmVersion": "1.4.3", "_phantomChildren": {}, "_requested": {"name": "estraverse", "raw": "estraverse@~1.5.0", "rawSpec": "~1.5.0", "scope": null, "spec": ">=1.5.0 <1.6.0", "type": "range"}, "_requiredBy": ["/escodegen"], "_resolved": "https://registry.npmjs.org/estraverse/-/estraverse-1.5.1.tgz", "_shasum": "867a3e8e58a9f84618afb6c2ddbcd916b7cbaf71", "_shrinkwrap": null, "_spec": "estraverse@~1.5.0", "_where": "/pms/node_modules/escodegen", "bugs": {"url": "https://github.com/Constellation/estraverse/issues"}, "dependencies": {}, "description": "ECMAScript JS AST traversal functions", "devDependencies": {"chai": "~1.7.2", "coffee-script": "~1.6.3", "jshint": "2.1.5", "mocha": "~1.12.0", "xyz": "^0.4.0"}, "directories": {}, "dist": {"shasum": "867a3e8e58a9f84618afb6c2ddbcd916b7cbaf71", "tarball": "http://registry.npmjs.org/estraverse/-/estraverse-1.5.1.tgz"}, "engines": {"node": ">=0.4.0"}, "homepage": "https://github.com/Constellation/estraverse", "licenses": [{"type": "BSD", "url": "http://github.com/Constellation/estraverse/raw/master/LICENSE.BSD"}], "main": "estraverse.js", "maintainers": [{"name": "constellation", "email": "<EMAIL>"}], "name": "estraverse", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/Constellation/estraverse.git"}, "scripts": {"lint": "jshint estraverse.js", "release-major": "xyz --increment major", "release-minor": "xyz --increment minor", "release-patch": "xyz --increment patch", "test": "npm run-script lint && npm run-script unit-test", "unit-test": "mocha --compilers coffee:coffee-script"}, "version": "1.5.1"}