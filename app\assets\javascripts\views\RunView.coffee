#=require "views/ProcessGraphView"
#=require "views/ProcessView"
#=require "views/behaviors/ReactToSort"
#=require "views/support/TemplateHelpers"

App.RunView = Marionette.CompositeView.extend
  ###
  Displays a run within an assignment page.
  ###

  #
  # Backbone.View options
  #

  tagName: 'li'
  className: 'tab-pane panel-group pms-run'

  events:
    'click .pms-run-add-first-process': '_addFirstProcess'

  #
  # Marionette.View options
  #

  ui:
    processList: '.pms-processes'

  behaviors:
    ReactToSort: {}

  templateHelpers:
    prettyDate: App.TemplateHelpers.prettyDate
    prettyState: App.TemplateHelpers.prettyState
    stateLabelClasses: App.TemplateHelpers.stateLabelClasses

  #
  # Marionette.ItemView options
  #

  template: JST['RunView']

  modelEvents:
    'change:state': 'render'

  #
  # Marionette.CollectionView options
  #

  childView: App.ProcessView
  childViewContainer: '.pms-processes'

  collectionEvents:
    'add': '_reorderProcesses'
    'destroy': '_updateAfterProcessDestroy'
    'move': '_reorderProcesses'
    'addParent': '_reorderProcesses'
    'removeParent': '_reorderProcesses'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @collection = @model.get('processes')
    @graphView = window.graphView = new App.ProcessGraphView(model: @model)

    @listenTo(this, 'childview:expand', @_renderGraph)
    @listenTo(this, 'childview:collapse', @_renderGraph)
    @listenTo(@graphView, 'resize', @_adjustProcessListWidthOnGraphResize)

  onShow: ->
    ###
    Override of Marionette.View#onShow.
    ###
    completable = @model.get('processes').where(completable: true)
    assigned = _.filter(completable, (p) -> p.hasTaskForUser())
    if assigned.length == 1
      view = @children.findByModel(assigned[0])
      view.expand()
    @_renderGraph()


  onBeforeRender: ->
    ###
    Override of Marionette.View#onBeforeRender.
    ###
    # Make sure the process list is sorted correctly. Don't make a
    # sound, otherwise we wake up ReactToSort which will do
    # unnecessary work.
    @collection.sort(silent: true)
    @_layoutProcesses()

  onRender: ->
    ###
    Override of Marionette.View#onRender.
    ###
    # Give the view a unique ID.
    @$el.attr('id', "pms-run-#{@model.id}")
    @$el.attr('data-pms-run-version', @model.get('version'))

    # Make the state popover window work.
    @$('.pms-run-state-label').popover(trigger: 'focus')

    # Wait for the process views to render and draw the process graph
    # afterwards. (ProcessGraphView needs to know the positions and
    # dimensions of the process views.)
    unless @collection.isEmpty()
      setTimeout(@_renderGraph.bind(this), 0)

  _renderGraph: ->
    graphViewEl = @$('.pms-process-graph').get(0)
    @graphView.setElement(graphViewEl)
    @graphView.render(@children)

  onReorder: ->
    ###
    Handler for ReactToSort's "reorder" trigger.
    ###
    @_renderGraph()

  _layoutProcesses: ->
    ###
    The layout algorithm.
    ###
    currentRow = 1
    currentColumn = 1

    @collection.each (process) ->
      # Detect when to move to the next layouting row.
      row = process.get('level') + 1
      if row != currentRow
        currentRow = row
        currentColumn = 1

      # Make sure each process is at least on the same column as the
      # left-most parent process (or further to the right if neeeded).
      if process.get('parents').length > 0
        parentColumn = _.min(process.get('parents').pluck('layoutColumn'))
        currentColumn = Math.max(currentColumn, parentColumn)

      process.set('layoutColumn', currentColumn)

      # If the process has children, reserve horizontal room for them
      # by skipping columns.
      currentColumn += Math.max(1, process.get('children').length)

  #
  # Event Handlers
  #

  _adjustProcessListWidthOnGraphResize: (_graph, graphWidth) ->
    @ui.processList.css('margin-left', graphWidth)

  _reorderProcesses: ->
    # Wait for Marionette to react to process additions / removals.
    setTimeout(@collection.sort.bind(@collection), 0)

  _updateAfterProcessDestroy: ->
    if @collection.isEmpty()
      # We need to show the "no processes" alert.
      @render()
    else
      @_reorderProcesses()

  _addFirstProcess: (event) ->
    event.preventDefault() # don't navigate to the link's href
    new App.NewProcessDialog().show (template) =>
      if template.id
        promise = @collection.create(template_id: template.id)
      else
        promise = @collection.create(name: template.get('name'))
      promise.done => @render()
