App.AttachmentsPictureView = Marionette.ItemView.extend
  ###
  A dialog displaying all attachments of a task. Allows attachments to
  be opened and uploaded.
  ###

  #
  # Backbone.View options
  #

  className: 'pms-attachments-pictures-dialog modal'
  events:
    'click .pms-attachment-pictures-prev-zone': '_prevPicture'
    'click .pms-attachment-pictures-next-zone': '_nextPicture'
    'click .pms-attachment-pictures-close': 'close'
    'click .pms-attachment-picture-zoom-in': '_zoomIn'
    'click .pms-attachment-picture-zoom-out': '_zoomOut'
    'click .pms-attachment-picture-zoom-reset': '_zoomReset'

  #
  # Marionette.ItemView options
  #

  template: JST['AttachmentsPictureView']

  #
  # Methods
  #

  initialize: (options) ->
    ###
    Override of Backbone.View#initialize.
    ###
    handleKeydown = (event) =>
      ###
      Handle arrow key events.
      ###
      switch event.which
        when 37 then @._prevPicture()
        when 39 then @._nextPicture()
        when 27 then @close()

    handleWheel = (event) =>
      ###
      Handle scroll wheel for zoom in/out.
      ###
      if event.originalEvent.deltaY < 0
        @zoomLevel = Math.min(@zoomLevel + 0.5, 5)
      else if event.originalEvent.deltaY > 0
        @zoomLevel = Math.max(@zoomLevel - 0.5, 1)
      @applyZoomPan()
      event.preventDefault()

    $(document).on 'keydown', handleKeydown
    # Attach wheel handler after rendering, so use a Marionette event
    @listenTo @, 'render', =>
      @$el.find('.pms-attachment-picture-container').off('wheel').on 'wheel', handleWheel    
    @title = options.title
    @pictures = @model.get('attachments').filter (attachment) -> attachment.get('content_type').match(/^image\/(?!tiff|bmp)/)
    @preloadedImages = []
    @preloadPictures()
    @currentPictureIndex = 0
    @currentPictureUrl = @pictures[0].url()
    @zoomLevel = 1.0
    @panX = 0
    @panY = 0
    @dragging = false
    @lastMouse = null
    
  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      url: @currentPictureUrl
      title: @title
      filename: @pictures[@currentPictureIndex].get('filename')

  show: ->
    ###
    Display the attachments dialog.
    ###
    @render()
    @$el.modal('show')
    @setupZoomPanHandlers()

  close: ->
    ###
    Close the attachments dialog.
    ###
    @render()
    @$el.modal('hide')
    $(document).off 'keydown'

  reRender: ->
    ###
    Re-render the dialog, otherwise changing quickly stacked modals
    ###
    @$el.modal('hide')
    @render()
    @$el.modal('show')
    @setupZoomPanHandlers()

  setupZoomPanHandlers: ->
    $img = @$el.find('.pms-attachment-picture')
    $container = @$el.find('.pms-attachment-picture-container')
    # Set initial transform
    @applyZoomPan()
    # Remove previous handlers
    $img.off('mousedown mousemove mouseup mouseleave')
    $img.off('touchstart touchmove touchend touchcancel')
    # Mouse events
    $img.on 'mousedown', (e) =>
      e.preventDefault()
      @dragging = true
      @lastMouse = {x: e.pageX, y: e.pageY}
      $img.css('cursor', 'grabbing')
    $img.on 'mousemove', (e) =>
      return unless @dragging
      dx = e.pageX - @lastMouse.x
      dy = e.pageY - @lastMouse.y
      @panX += dx
      @panY += dy
      @lastMouse = {x: e.pageX, y: e.pageY}
      @applyZoomPan()
    $img.on 'mouseup mouseleave', (e) =>
      @dragging = false
      $img.css('cursor', 'grab')
    # Touch helpers
    getTouch = (evt) ->
      oe = evt.originalEvent ? evt
      t = (oe.touches && oe.touches[0]) or (oe.changedTouches && oe.changedTouches[0])
      return { oe, t }
    # Touch events
    $img.on 'touchstart', (e) =>
      {oe, t} = getTouch(e)
      return unless t? and oe.touches? and oe.touches.length is 1
      # prevent page scroll on drag when allowed
      if oe.cancelable then e.preventDefault()
      @dragging = true
      @lastMouse = {x: t.pageX, y: t.pageY}
    $img.on 'touchmove', (e) =>
      return unless @dragging
      {oe, t} = getTouch(e)
      return unless t?
      # Only pan for single-touch moves (ignore pinch)
      return unless !oe.touches? or oe.touches.length is 1
      if oe.cancelable then e.preventDefault()
      dx = t.pageX - @lastMouse.x
      dy = t.pageY - @lastMouse.y
      @panX += dx
      @panY += dy
      @lastMouse = {x: t.pageX, y: t.pageY}
      @applyZoomPan()
    $img.on 'touchend', (e) =>
      @dragging = false
      $img.css('cursor', 'grab')
    $img.on 'touchcancel', (e) =>
      @dragging = false
      $img.css('cursor', 'grab')

  applyZoomPan: ->
    $img = @$el.find('.pms-attachment-picture')
    $img.css('transform', "translate(" + @panX + "px, " + @panY + "px) scale(" + @zoomLevel + ")")

  _zoomIn: (e) ->
    e.preventDefault()
    @zoomLevel = Math.min(@zoomLevel + 0.5, 5)
    @applyZoomPan()

  _zoomOut: (e) ->
    e.preventDefault()
    @zoomLevel = Math.max(@zoomLevel - 0.5, 1)
    @applyZoomPan()
  
  _zoomReset: (e) ->
    e.preventDefault()
    @zoomLevel = 1.0
    @panX = 0
    @panY = 0
    @applyZoomPan()

  preloadPictures: ->
    ###
    Preload all pictures in the collection.
    ###
    @preloadedImages = []
    for picture in @pictures
      img = new Image()
      img.src = picture.url()
      @preloadedImages.push(img)

  #
  # Event handlers
  #

  _nextPicture: ->
    ###
    Show the next picture in the collection.
    ###
    @currentPictureIndex = (@currentPictureIndex + 1) % @pictures.length
    @currentPictureUrl = @pictures[@currentPictureIndex].url()
    @zoomLevel = 1.0
    @panX = 0
    @panY = 0
    @reRender()

  _prevPicture: ->
    ###
    Show the previous picture in the collection.
    ###
    @currentPictureIndex = ((@currentPictureIndex - 1) + @pictures.length) % @pictures.length
    @currentPictureUrl = @pictures[@currentPictureIndex].url()
    @zoomLevel = 1.0
    @panX = 0
    @panY = 0
    @reRender()