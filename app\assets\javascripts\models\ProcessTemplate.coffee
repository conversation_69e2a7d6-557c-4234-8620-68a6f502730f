#= require "models/Model"
#= require "models/TaskTemplate"

App.ProcessTemplate = App.Model.extend
  ###
  Client-side representation of ProcessTemplate.
  ###

  #
  # Backbone.Model options
  #

  urlRoot: '/api/process_templates'

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasMany
    key: 'task_templates'
    keySource: 'task_template_ids'
    relatedModel: App.TaskTemplate
    includeInJSON: false
    reverseRelation:
      key: 'process_template'
      keySource: 'process_template_id'
      includeInJSON: 'id'
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'process_template'
  jsonPluralRoot: 'process_templates'

  #
  # Methods
  #

  #  Implementing User start
  initialize: ->
    ###
    Override of Backbone.Model#initialize.
    ###
    @listenTo(@get('task_templates'), 'add', @_triggerTaskTemplateAdd)
    @listenTo(@get('task_templates'), 'remove', @_triggerTaskTemplateRemove)
    @listenTo(@get('task_templates'), 'change:operator', @_triggerChangeAfterAssign)

  #
  # Event handlers
  #

  _triggerTaskTemplateAdd: (taskTemplate) ->
    @trigger('taskTemplate:add', taskTemplate, this)

  _triggerTaskTemplateRemove: (taskTemplate) ->
    @trigger('taskTemplate:remove', taskTemplate, this)

  _triggerChangeAfterAssign: ->
    # If a task has been assigned to the current user and the proces
    # has no task assigned this way before, the process has changed as
    # hasTasksForUser now returns true. Trigger a "change" event so
    # that listeners can react to this change.
    @trigger('change', this)
  #  Implementing User end