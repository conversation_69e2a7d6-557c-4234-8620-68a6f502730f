# A run ("Durchlauf") is a plan for completing an assignment. It
# arranges a set of processes (PmsProcess) into a partial order
# which defines when which tasks should be worked on.
#
# Initially, every assignment contains just a single run. However, if
# a run is canceled (see below), a new run is added to the assignment
# and the old one is kept for archival purposes. On the other hand,
# while an assignment may have multiple runs, there is only one
# "current" run at any point in time.
#
# Run States
#
# Every run is a state machine consisting of five states. Every run
# starts as a "draft"; planners of the containing assignment may add,
# connect and remove processes, but manipulation by operators is
# prohibited. As soon as the run is ready to begin, a planner can
# "activate" the run, which causes it to transition to the "active"
# state. In this state, operators are allowed to work on the run's
# processes and complete their contained tasks. If all processes in
# the run have been completed, the run automatically transitions into
# the "completed" state (which results in the containing assignment to
# be considered completed).
#
# At any time, planners can "pause" a run's activation to make
# modifications to the run without operators interfering. This is also
# handy if some form of clarification is needed before the run can be
# completed (for instance, if a question comes up whose answer
# determines how the run will be continued).
#
# If, for some reason, something goes wrong during a run, a planner
# can choose to "cancel" it, thus putting the run into the "failed"
# state. This state doesn't prevent operators from completing tasks in
# the run, though; doing can still result in more experience for the
# next run.
#
# A special run state is "template". This is essentially like "draft",
# with the exception that a template run cannot be activated; instead,
# its only purpose is to be copied from an assignment template into a
# concrete assignment (see Assignment).
class Run < ActiveRecord::Base
  # Associations
  belongs_to :assignment, touch: true
  has_many :processes, class_name: "PmsProcess", dependent: :destroy
  has_many :process_dependencies
  has_many :tasks, through: :processes
  validates :state,
            inclusion: { in: %w(template draft active completed failed paused) }
  before_validation :fill_in_defaults
  after_update :invalidate_caches, if: :state_changed?

  # # Validations
  # validates :created_at, presence: true
  # validates :completed_at, presence: true

  amoeba do
    set state: "draft"
    nullify :completed_at
    customize(lambda do |original, copy|
      copy.version = original.version + 1
    end)
  end

  # Duplicate the run and its contents. The copied run will initially
  # not belong to any assignment.
  #
  # Return the duplicate run.
  def copy
    run_copy = amoeba_dup
    copies_by_original_id = copy_processes(run_copy)
    copy_process_dependencies(run_copy, copies_by_original_id)
    run_copy
  end

  # Find the processes at the beginning of the run (that is, the
  # processes which don't depend on any other process).
  #
  # Returns the run's first processes as array.
  def first_processes
    processes.select { |p| p.parents.empty? }
  end

  # Gets all completable processes of the run.
  #
  # Returns all completable processes of the run as an array.
  def completable_processes
    processes.select(&:completable?)
  end

  # Gets all completable tasks of the run.
  #
  # Returns all completable tasks of the run as an array.
  def completable_tasks
    tasks.select(&:completable?)
  end

  # tasks that can be completed in the future, but are not completable right now
  def future_tasks
    completable_task_ids = completable_tasks.map { |task| task.id }
    tasks.where(completed: false).where.not(id: completable_task_ids)
  end

  # Determine whether all processes in the run have been
  # completed. This is different from checking if the run is in the
  # "completed" state, as it is possible to continue working on a run
  # after it has already been canceled; thus it may become "done"
  # without being successful.
  #
  # Returns true if the run is considered done, or false otherwise.
  def done?
    !tasks.exists?(completed: false)
  end

  # Check if the run's current state forbids operators to work on the
  # contained processes.
  #
  # Returns true if the run is currently inactive, or false otherwise.
  def inactive?
    state != "active"
  end

  # Check if it is currently possible to activate the run. This depends
  # on the run's current state.
  #
  # Returns true if the run can be activated, or false otherwise.
  def activatable?
    state == "draft" || state == "paused"
  end

  # Check if a specific user is currently allowed to activate the run.
  # Implies checking #activatable?.
  #
  # user - The user to check with.
  #
  # Returns true if activation is possible for the user, or false
  # otherwise.
  def activatable_by_user?(user)
    activatable? && assignment.editable_by_user?(user)
  end

  # Check if is currently possible to change the run's contents. In
  # some states, this is not allowed (for example "completed").
  #
  # Returns true if the run is currently editable, or false otherwise.
  def editable?
    state != "completed" && state != "failed"
  end

  # Check if a specific user is currently allowed to edit the run's
  # contents. Implies checking #editable?.
  #
  # user - The user to check with.
  #
  # Returns true if editing is possible for the user, or false
  # otherwise.
  def editable_by_user?(user)
    editable? && assignment.editable_by_user?(user)
    #editable? && assignment.editable_by_user?(user) && !user.workgroup.extern
  end

  # Check if a specific user is currently allowed to repeat the run.
  #
  # user - The user to check with.
  #
  # Returns true if the user is allowed to repeat the run, or false
  # otherwise.
  def repeatable_by_user?(user)
    state == "completed" && assignment.editable_by_user?(user)
  end

  private

  def fill_in_defaults
    self.state ||= "draft"
    self.version ||= 1
  end

  def copy_processes(run_copy)
    copies_by_original_id = {}
    processes.each do |original|
      copy = original.copy
      run_copy.processes.push(copy)
      copies_by_original_id[original.id] = copy
      template_with_same_name = ProcessTemplate.where(name: original.name).last
      if template_with_same_name
        if template_with_same_name.updated_at && template_with_same_name.updated_at > original.updated_at
          copy.name = "#{original.name} (Alt)"
        end
      end
    end
    copies_by_original_id
  end

  def copy_process_dependencies(run_copy, copies_by_original_id)
    processes.each do |process|
      process.child_dependencies.each do |dep|
        parent_copy = copies_by_original_id[dep.parent_id]
        child_copy = copies_by_original_id[dep.child_id]

        dep_copy = ProcessDependency.new(parent: parent_copy,
                                         child: child_copy,
                                         child_index: dep.child_index)

        run_copy.process_dependencies.push(dep_copy)
        parent_copy.child_dependencies.push(dep_copy)
        child_copy.parent_dependencies.push(dep_copy)
      end
    end
  end

  def invalidate_caches
    processes.each do |process|
      process.invalidate_caches
    end
  end
end
