# Executed if a user creates a new assignment.
class CreateAssignmentAction < CreateAction
  # Initialize the action.
  #
  # name     - The assignment name.
  # project  - The name of the project the assignment should belong to.
  # creator  - The user which creates the assignment.
  # options  - Additional options influencing the created assignment (optional).
  #            template - Whether to create an assignment template rather than a
  #                       concrete assignment. Default is false.
  #            copy_of  - If passed, the assignment will be copied from the
  #                       passed other assignment rather than created from
  #                       scratch.. Default is nil (create from scratch).
  def initialize(name, project, creator, datapath, description, due, priority, options = {})
    super(nil)
    @name = name
    @project = project
    @creator = creator
    @created_by_id = creator.id
    @datapath = datapath
    @description = description
    @due = due
    @priority = priority
    @board_count = 0
    @create_template = options[:template] || false
    @copy_of = options[:copy_of]
  end

  def allowed?(_current_user)
    true
  end

  def do_create!
    assignment = @copy_of ? create_from_template : create_from_scratch
    make_creator_a_planner(assignment)
    assignment.reload
  end

  private

  def initial_run_state
    @create_template ? "template" : "draft"
  end

  def create_from_template
    @assignment = @copy_of.copy
    @assignment.name = @name
    @assignment.project = @project
    @assignment.description = @description
    @assignment.due = @due
    @assignment.priority = @priority
    @assignment.board_count = 0
    @assignment.created_by_id = @created_by_id
    @assignment.runs.first.state = initial_run_state
    @assignment.save!
    copy_attachments_from_template if Dir.exist?(Pathname.new(Rails.configuration.pms_attachments_root) + "Auftrag #{@copy_of.id}" + "Durchlauf #{@copy_of.runs.last.version}")
    @assignment
  end

  def copy_attachments_from_template
    dest = Pathname.new(Rails.configuration.pms_attachments_root)
    dest += "Auftrag #{@assignment.id}"
    dest += "Durchlauf #{@assignment.runs.last.version}"
    FileUtils.mkdir_p(dest.dirname) 
    src = Pathname.new(Rails.configuration.pms_attachments_root)
    src += "Auftrag #{@copy_of.id}"
    src += "Durchlauf #{@copy_of.runs.last.version}"
    FileUtils.cp_r src, dest
  end

  def create_from_scratch
    assignment = Assignment.create!(name: @name, project: @project, datapath: @datapath,
      description: @description, due: @due, created_by_id: @created_by_id,
      priority: @priority, board_count: @board_count)
    assignment.runs.create!(version: 1, state: initial_run_state)
    assignment
  end

  def make_creator_a_planner(assignment)
    AssignmentMembership.ensure_planner(@creator, assignment)
  end
end
