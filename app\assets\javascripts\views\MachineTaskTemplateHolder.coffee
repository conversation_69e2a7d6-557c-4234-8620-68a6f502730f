#=require "models/Task"
#=require "views/AttachmentsDialog"
#=require "views/OperatorSearchDialog"
#=require "views/MachineSearchDialog"
#=require "views/MachineTaskView"
#=require "views/MachineView"
#=require "views/behaviors/ConditionalClassNames"
#=require "views/behaviors/Editable"
#=require "views/behaviors/NameDataAttribute"
#=require "views/support/TemplateHelpers"

App.MachineTaskTemplateHolder = Marionette.CollectionView.extend
  ###
  Displays a task on an assignment page.
  ###

  #
  # Backbone.View options
  #

  tagName: 'tbody'

  # className: 'pms-task'

  events:
    'click .pms-task-machine-delete-btn': '_removeMachine'


  #
  # Marionette.View options
  #

  # ui:
  #   attachmentsCounter: '.pms-task-num-attachments'
  #   checkButton: '.pms-task-check-btn'
  #   moveButton: '.pms-task-move-btn'
  #   addUtilityButton: '.pms-task-add-utility-btn'

  # behaviors:
  #   ConditionalClassNames:
  #     'pms-completed': 'completed'
  #     'pms-completable': 'completable'
  #     'pms-future': -> !@model.get('completed') && !@model.get('completable')
  #     'pms-movable': 'movable'
  #   Editable:
  #     name:
  #       type: 'string'
  #       guard: 'editable'
  #       target: '.pms-task-name'
  #       inputClass: 'form-control pms-task-name-input'
  #     instruction:
  #       type: 'text'
  #       guard: 'editable'
  #       target: '.pms-task-instruction'
  #       inputClass: 'form-control pms-task-instruction-input'
  #     note:
  #       type: 'text'
  #       guard: 'annotatable'
  #       target: '.pms-task-note'
  #       inputClass: 'form-control pms-task-note-input'
  #   NameDataAttribute: {}

  # templateHelpers:
  #   valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
  #   prettyUser: App.TemplateHelpers.prettyUser
  #   prettyOperatorGroup: App.TemplateHelpers.prettyOperatorGroup
  #   prettyDateTime: App.TemplateHelpers.prettyDateTime
  #   promptAddMachineOrMaterial: ->
  #       _.template("""
  #         <div class='pms-task-add-machine-or-material'>
  #           <div>
  #             <button class='btn btn-sm btn-default pms-task-add-material-btn'>
  #               <i class='fas fa-cube' title='Material'></i> Material
  #             </button>
  #             <button class='btn btn-sm btn-default pms-task-add-machine-btn'>
  #               <i class='fas fa-cog' title='Maschine'></i> Maschine
  #             </button>
  #           </div>
  #         </div>
  #       """)(this)
  #   promptTaskCompletion: ->
  #       _.template("""
  #         <div class='pms-task-completion-prompt'>
  #           Soll diese Aufgabe, zu der Sie nicht zugewiesen sind, wirklich abgeschlossen werden?
  #           <div style='margin-top: 5px;'>
  #             <button class='btn btn-sm btn-default pms-task-completion-cancel-btn'>
  #               Nein
  #             </button>
  #             <button class='btn btn-sm btn-default pms-task-completion-confirm-btn'>
  #               Ja
  #             </button>
  #           </div>
  #         </div>
  #       """)(this)

  #
  # Marionette.ItemView options
  #

  # template: JST['TaskView']

  modelEvents:
  #   'change:completed' : 'render'
  #   'change:operator' : 'render'
  #   'change:completable': 'render'
  #   'change:completion_undoable': 'render'
    'change:machines' : 'render'

  #
  # Marionette.CollectionView options
  #

  childView: App.MachineView
  childViewOptions: (model, index) ->
    return {editable: true}
  childViewContainer: '.pms-machines'


  initialize: ->
    @collection = @model.get('machines')
    @listenTo @model.get('machines'), 'add', this.render
    @listenTo @model.get('machines'), 'remove', this.render

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###

    _.extend @model.toJSON(),
      assignedToUser: App.user?.isAssignedTo(@model)
      operator: @model.get('operator')?.toJSON() || null
      completed_by: @model.get('completed_by')?.toJSON() || null,
      assignmentState: @model.get('process').get('run').get('state')

  # onRender: ->
    ###
    Override of Marionette.ItemView#onRender.
    ###
    # @ui.checkButton.popover()
    # @ui.addUtilityButton.popover()
    # @_adjustAttachmentsCounter()
    # @_setCheckButtonState()
    # # hide machine field if empty
    # if (@model.get('machines').length > 0)
    #   @$el.find('.pms-task-machines-holder').show()
    # else
    #   @$el.find('.pms-task-machines-holder').hide()
    # # Make the associated task's ID available through the DOM. This is
    # # used by the task drag-and-drop code in ProcessView.
    # @$el.attr('data-pms-id', @model.id)

  #
  # Event Handlers
  #

  # _showAttachmentsDialog: ->
  #   dialog = new App.AttachmentsDialog
  #     model: @model
  #     title: @model.get('name')
  #   dialog.show()

  # _adjustAttachmentsCounter: ->
  #   numAttachments = @model.get('attachments').length
  #   counter = @ui.attachmentsCounter
  #   counter.text(numAttachments)
  #   # Setting the "nonzero" CSS class on the attachments counter lets
  #   # us highlight tasks with attachments.
  #   if numAttachments > 0
  #     counter.addClass('nonzero')
  #   else
  #     counter.removeClass('nonzero')

  # _setCheckButtonState: ->
  #   @ui.checkButton.attr('disabled', !@model.get('completable') && !@model.get('completion_undoable'))

  # _toggleTaskCompletionState: ->
  #   if @model.get('completion_undoable')
  #     @_uncompleteTask()
  #   else
  #     if App.user?.isAssignedTo(@model)
  #       @_completeTask()

  # _completeTask: ->
  #   @model.save('completed', true, patch: true, wait: true).then =>
  #     @model.updateCompletionUndoable()
  #     for predecessor in @model.predecessors()
  #       predecessor.updateCompletionUndoable()

  # _uncompleteTask: ->
  #   @model.save('completed', false, patch: true, wait: true).then =>
  #     for predecessor in @model.predecessors()
  #       predecessor.updateCompletionUndoable()

  # _deleteTask: ->
  #   @model.destroy() # also deletes the view

  # _showAssignDialog: ->
  #   assignment = @model.get('process').get('run').get('assignment')
  #   extern_workgroup_ids = assignment.get('extern_workgroups').pluck('id')
  #   new App.OperatorSearchDialog().show (operator) =>
  #     if operator.get('type') == "user" && operator.get('extern') &&
  #        operator.get('workgroup_id') not in extern_workgroup_ids
  #         confirm "Benutzer kann nicht zugewiesen werden, da der Auftrag nicht für ihn verfügbar ist. (siehe Auftragsdetails - Extern verfügbar)"
  #     else
  #       operatorSpec = {id: operator.id, type: operator.get('type')}
  #       @model.save(
  #         task: {operator: operatorSpec},
  #         {patch: true, wait: true})

  # _cancel: ->
  #   @ui.checkButton.popover('hide')

  # _addMaterial: ->
  #   @ui.addUtilityButton.popover('hide')

  # _addMachine: ->
  #   @ui.addUtilityButton.popover('hide')

  # _showAddMachineDialog: ->
  #   @ui.addUtilityButton.popover('hide')
  #   new App.MachineSearchDialog().show (machine) =>
  #       @model.save(
  #         task: {machine: machine.id},
  #         {patch: true, wait: true})

  _removeMachine: (event) ->
    machineId = $(event.currentTarget).data('machine-id')
    @collection.remove({id: machineId}, {wait: true})
    @model.save(
          task_template: {remove_machine: machineId},
          {patch: true, wait: true})