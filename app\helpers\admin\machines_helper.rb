module Admin::MachinesHelper
  def pretty_state(state)
    case state
    when "ready"
      "Bereit" 
    when "used"
      "In Benutzung"
    when "out_of_service"
      "Außer Betrieb"
    end
  end

  def state_icon(state)
    case state
    when "ready"
      "<i class='fas fa-circle pms-smaller-icon pms-state-green-icon' title='#{pretty_state(state)}'></i>".html_safe
    when "used"
      "<i class='fas fa-circle pms-smaller-icon pms-state-yellow-icon' title='#{pretty_state(state)}'></i>".html_safe
    when "out_of_service"
      "<i class='fas fa-circle pms-smaller-icon pms-state-red-icon' title='#{pretty_state(state)}'></i>".html_safe
    end
  end
end
