#=require "collections/RunCollection"
#=require "collections/UserCollection"
#=require "collections/WorkgroupCollection"
#=require "models/Model"
#=require "models/Run"
#=require "models/User"
#=require "models/OperatorGroup"

App.Assignment = App.Model.extend
  ###
  The client-side representation of Assignment.
  ###

  #
  # Backbone.Model options
  #

  urlRoot: '/api/assignments'

  defaults:
    name: 'Neuer Auftrag'

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasMany
    key: 'runs'
    keySource: 'run_ids'
    relatedModel: App.Run
    collectionType: App.RunCollection
    includeInJSON: false
    reverseRelation:
      key: 'assignment'
      keySource: 'assignment_id'
      includeInJSON: 'id'
  ,
    type: Backbone.HasMany
    key: 'members'
    keySource: 'member_ids'
    relatedModel: App.User
    collectionType: App.UserCollection
    includeInJSON: 'id'
  ,
    type: Backbone.HasMany
    key: 'operator_groups'
    keySource: 'operator_group_ids'
    relatedModel: App.OperatorGroup
    includeInJSON: 'id'
  ,
    key: 'planners'
    keySource: 'planner_ids'
    type: Backbone.HasMany
    relatedModel: App.User
    collectionType: App.UserCollection
    includeInJSON: 'id'
  ,
    key: 'observers'
    keySource: 'observer_ids'
    type: Backbone.HasMany
    relatedModel: App.User
    collectionType: App.UserCollection
    includeInJSON: 'id'
  ,
    key: 'extern_workgroups'
    keySource: 'extern_workgroup_ids'
    type: Backbone.HasMany
    relatedModel: App.Workgroup
    collectionType: App.WorkgroupCollection
    includeInJSON: false
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'assignment'
  jsonPluralRoot: 'assignments'

  #
  # Methods
  #

  latestRun: ->
    ###
    Return the most recently created run in the assignment.
    ###
    @get('runs').max((s) -> s.get('version'))

  isOverdue: ->
    if @get('state') == 'completed' || @get('state') == 'failed'
      @get('due') && new Date(@get('runs').last().get('completed_at')).setHours(0,0,0,0) > new Date(@get('due'))
    else
      @get('due') && new Date().setHours(0,0,0,0) > new Date(@get('due'))
