# Executed if a user uncompletes a previously completed task.
class UndoTaskCompletionAction < Action
  def initialize(task)
    super(task)
  end

  def allowed?(user)
    subject.completion_undoable_by_user?(user)
  end

  def do_execute!
    reset_successors
    reset_possibly_completed_process
    reset_possibly_completed_run
    reset_delayed_job
    subject.update!(completed: false, completed_at: nil, completed_by: nil, completion_quality: 0)
  end

  private

  def reset_possibly_completed_process
    return unless subject.process.completed?
    subject.process.update!(process_completed: false)
    mark_changed(subject.process)
  end

  def reset_successors
    subject.successors.select(&:completable?).each do |successor|
      mark_changed(successor)

      # If the successor is in another process than the subject, it
      # must be the first task of a process that has now become
      # completable. In that case, the process has changed, too.
      next unless successor.process != subject.process

      mark_changed(successor.process)
    end
  end

  def reset_possibly_completed_run
    run = subject.process.run
    return unless run.state == "completed"

    run.update!(state: "active")
    run.update!(completed_at: nil)
    mark_changed(run)
    mark_changed(run.assignment)
  end

  def reset_delayed_job
    # next task ready
    next_task_jobs = Delayed::Job.where("handler LIKE '%method_name: :next_task_ready%' AND
      handler LI<PERSON> '%id: #{subject.id}%'")
    if next_task_jobs.exists?
      next_task_jobs.last.destroy
    end

    # next group task ready
    next_group_task_jobs = Delayed::Job.where("handler LIKE '%method_name: :next_group_task_ready%' AND
      handler LIKE '%id: #{subject.id}%'")
    if next_group_task_jobs.exists?
      next_group_task_jobs.last.destroy
    end

    # next task ready planner
    next_task_planner_jobs = Delayed::Job.where("handler LIKE '%method_name: :next_task_ready_planner%' AND
      handler LIKE '%id: #{subject.id}%'")
    if next_task_planner_jobs.exists?
      next_task_planner_jobs.last.destroy
    end

    # task completed
    Delayed::Job.where("handler LIKE '%method_name: :task_completed%' AND
      handler LIKE '%id: #{subject.process.id}%'").each do |job|
        job.destroy
    end

    # group task completed
    Delayed::Job.where("handler LIKE '%method_name: :group_task_completed%' AND
      handler LIKE '%id: #{subject.process.id}%'").each do |job|
        job.destroy
    end

    # process completed
    Delayed::Job.where("handler LIKE '%method_name: :process_completed%' AND
      handler LIKE '%id: #{subject.process.id}%'").each do |job|
        job.destroy
    end

    # process ready
    Delayed::Job.where("handler LIKE '%method_name: :process_ready%' AND
      handler LIKE '%id: #{subject.process.id}%'").each do |job|
        job.destroy
    end

    # assignment completed
    Delayed::Job.where("handler LIKE '%method_name: :assignment_completed%' AND
      handler LIKE '%id: #{subject.id}%'").each do |job|
        job.destroy
    end

  end

end
