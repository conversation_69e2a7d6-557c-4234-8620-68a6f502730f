# Executed when a planner removes another planner from an assignment.
class RemovePlannerAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to remove the planner to.
  # user       - The planner to remove.
  def initialize(assignment, user)
    super(assignment)
    @user = user
  end

  def allowed?(user)
    return (subject.editable_by_user?(user) && subject.planners.count > 1) 
  end

  def do_execute!
    AssignmentMembership.ensure_not_planner(@user, subject)
    subject.touch
  end
end
