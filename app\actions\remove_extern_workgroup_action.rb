# Executed when a planner removes teh extern workgroup.
class RemoveExternWorkgroupAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to make it available.
  # workgroup  - The workgroup to add as member.
  def initialize(assignment, workgroup)
    super(assignment)
    @workgroup = workgroup
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    AssignmentMembership
      .where(assignment: subject, member: @workgroup)
      .delete_all
  end
end