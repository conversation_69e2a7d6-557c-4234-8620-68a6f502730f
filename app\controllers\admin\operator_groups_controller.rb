# Provides an administrator interface for creating and editing
# operator groups.
class Admin::OperatorGroupsController < Admin::AdminController
  before_action :require_permission

  # List all operator groups.
  #
  # Returns a page listing all groups.
  def index
    @groups = OperatorGroup.order(:name).includes(:machine)
  end

  def new
    @group = OperatorGroup.new
    @users = User.active.order(:username)
    @workgroups = Workgroup.order(:name)
    render :edit
  end

  # Create an operator group.
  #
  # operator_group - A hash with the group's attributes.
  #
  # Returns a redirect to the group's edit page, with flash messages
  #   set to report either success or failure.
  def create
    @group = OperatorGroup.new(permitted_params)
    if @group.save
      flash[:edit_notice] = "Die Gruppe wurde angelegt."
      redirect_to action: :index
    else
      render :edit # with error messages
    end
  end

  # Show an edit page for an existing operator group.
  #
  # id - The group's ID.
  #
  # Returns the edit page.
  def edit
    @group = OperatorGroup.find(params[:id])
    @users = User.active.order(:username)
    if @group.machine 
      @users = @group.machine.instructed_users_group.users
    else
      @users = User.active.order(:username)
    end
    @workgroups = Workgroup.order(:name)
  end

  # Update an operator group.
  #
  # id             - The group's ID.
  # operator_group - A hash with the group's attributes.
  #
  # Returns a redirect to the group's edit page, with flash messages
  #   set to report either success or failure.
  def update
    @group = OperatorGroup.find(params[:id])
    if @group.update(permitted_params)
      flash[:edit_notice] = "Die Gruppe wurde gespeichert."
      redirect_to action: :index
    else
      @users = User.active.order(:username)
      @workgroups = Workgroup.order(:name)
    
      render :edit # with error messages
    end
  end

  private

  def require_permission
    return if current_user && current_user.can_edit_users?
    redirect_to root_path
  end

  def permitted_params
    if @group.present? && @group.machine.present?
      params.require(:operator_group).permit(user_ids: [])
    else
      params.require(:operator_group).permit(:name, user_ids: [])
    end
  end
end
