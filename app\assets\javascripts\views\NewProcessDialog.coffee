#=require "collections/ProcessCollection"
#=require "collections/ProcessTemplateCollection"
#=require "views/NewProcessItem"
#=require "views/SearchDialog"

App.NewProcessDialog = App.SearchDialog.extend
  ###
  A dialog for creating a new process, either by starting from scratch
  or by searching for an existing one to copy.
  ###

  #
  # Backbone.View options
  #

  className: 'modal pms-new-process-dialog'

  #
  # Marionette.CollectionView options
  #

  childView: App.NewProcessItem

  #
  # App.SearchDialog options
  #

  title: 'Neuer Prozess'
  searchLabel: 'Prozessname'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of App.SearchDialog#initialize.
    ###
    App.SearchDialog::initialize.call(this)
    if App.useProcessTemplates
      @collection = new App.ProcessTemplateCollection()
    else
      @collection = new App.ProcessCollection()

  defaultResultItems: (searchTerm) ->
    ###
    See SearchDialog.
    ###
    if searchTerm.trim()
      [@_freshProcessItem(searchTerm)]
    else
      []

  _freshProcessItem: (name) ->
    if App.useProcessTemplates
      new App.ProcessTemplate(id: null, name: name)
    else
      new App.Process(id: null, name: name)
