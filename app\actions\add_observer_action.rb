# Executed when a planner adds an observer to an assignment.
class AddObserverAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to add an observer to.
  # user       - The user to add as observer.
  def initialize(assignment, user)
    super(assignment)
    @user = user
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    AssignmentMembership.ensure_observer(@user, subject)
    subject.touch
    notify(:info_added_observer, subject, @user)
    notify(:info_added_observer_observer, subject, @user)
    notify(:added_observer, subject, @user)
  end
end
