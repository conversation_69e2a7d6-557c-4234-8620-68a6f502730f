// Defines style variables and customizes the variables of Bootstrap.

//
// Custom Variables
//

// The green of the Fraunhofer logo.
$fraunhofer-green: rgb(0, 148, 116);

// The color that the site background should have (visible on the left
// and the right of the page on desktop platforms).
$pms-body-background: #f3f3f3;

// Variables controlling the look of the always-visible top navigation
// bar.
$pms-top-bar-background: $fraunhofer-green;
$pms-top-bar-text: white;
$pms-top-bar-height: 50px;

// The colors of completed and completable tasks.
$pms-completed-bg: #dff0d8;
$pms-completable-bg: #fcf8e3;
$pms-completed-bad-bg: #fce3e3;

$pms-background-gray: #ccc;
$pms-border-gray: #aaa;

//
// Bootstrap Customization
//

// Make some blue-by-default things Fraunhofer green.
$btn-primary-bg: $fraunhofer-green;

// Align the button border color with the other PMS borders.
$btn-default-border: $pms-border-gray;

// Customize link and hover  colors.
$link-color: $fraunhofer-green;
$component-active-bg: $fraunhofer-green;

// Bootstrap standard colors
$brand-primary: #337ab7;
$brand-success: #5cb85c;
$brand-warning: #f0ad4e;
$brand-danger:  #d9534f;
$brand-danger-bright:  #ec4c48;
