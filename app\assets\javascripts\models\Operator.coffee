#=require "models/Model"

class App.Operator extends App.Model
  ###
  A dummy model used as type for App.Task's "operator" relation, which
  can point to either a User or an OperatorGroup. Backbone Relational
  doesn't support such polymorphic relations, so we work around it by
  overriding its findModel() and build() methods here and delegating
  to their equivalents in User or OperatorGroup as needed.
  ###

  @findModel: (attrs, options) ->
    ###
    See Backbone.RelationalModel.
    ###
    @_modelType(attrs).findModel(attrs, options)

  @build: (attrs, options) ->
    ###
    See Backbone.RelationalModel.
    ###
    @_modelType(attrs).build(attrs, options)

  @_modelType: (attrs) ->
    switch attrs.type
      when "user" then App.User
      when "operator_group" then App.OperatorGroup
