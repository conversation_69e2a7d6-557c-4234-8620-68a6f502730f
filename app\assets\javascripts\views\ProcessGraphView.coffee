#=require "views/ProcessHandle"
#=require "views/ProcessInsertMarkers"

App.ProcessGraphView = Backbone.View.extend
  ###
  A graphical representation of the processes of a run as well as
  the dependencies between them. On the desktop, it is shown as a
  sidebar within a RunView.
  ###

  #
  # Constants
  #

  DOT_RADIUS: 10
  BRANCH_SPACING: 24
  CURVE_HEIGHT: 24

  COMPLETED_COLOR: 'rgb(0, 148, 116)'
  COMPLETABLE_COLOR: 'black'
  UNCOMPLETABLE_COLOR: '#aaa'

  events:
    'click .pms-add-process-below-btn': '_saveAction'
    'click .pms-add-process-before-btn': '_saveAction'
    'click .pms-add-process-beside-btn': '_saveAction'
    'click .pms-remove-connection-before-btn': '_saveAction'
    'click .pms-remove-connection-after-btn': '_saveAction'
    'click .pms-move-process-above-btn': '_moveAbove'
    'click .pms-move-process-below-btn': '_moveBelow'
    'click .pms-move-process-beside-btn': '_moveBeside'
    'click .pms-submit-btn': '_runAction'
    'click .pms-cancel-btn': '_cancel'

  contentSubmit: (process) ->
    _.template(
      '<div class="pms-popover-submit">
        Soll die Verbindung zu diesem Prozess hergestellt/getrennt werden?
        <div>
          <button class="btn btn-sm btn-default pms-submit-btn" id="<%= id %>">
            OK
          </button>
          <button class="btn btn-sm btn-default pms-cancel-btn">
            Abbrechen
          </button>
        </div>
      </div>'
    )(_.extend(process))

  contentInsertActions: (process) ->
    @child_movable = process.get('children').any (c) -> (c.get('editable'))
    @parent_movable = process.get('parents').any (p) -> (p.get('editable'))
    _.template(
      '<div class="pms-popover-insert-actions">
        <div>
          <button class="btn btn-sm btn-default pms-move-process-above-btn"
            id="<%= id %>" value="moveAbove" <% if (!parent_movable || no_parent) { %> disabled <% } %>>
            <i class="fas fa-caret-up" title="Prozess nach oben verschieben"></i>
          </button>
          <button class="btn btn-sm btn-default pms-remove-connection-before-btn"
            id="<%= id %>" value="removeBefore" title="Verbindung darüber trennen"
            <% if (!parent_movable) { %> disabled <% } %>>
            <i class="fas fa-cut"></i>
          </button>
          <button class="btn btn-sm btn-default pms-add-process-before-btn"
            id="<%= id %>" value="addBefore" title="Vorgängerprozess hinzufügen"
            <% if (!movable) { %> disabled <% } %>>
            <div class="pms-add-process-icon"><i class="fas fa-caret-up"></i></div>
            <i class="fas fa-ellipsis-v"></i>
          </button>
        </div>
        <div>
          <i class="fas fa-bars pms-process-icon"></i>
          <button class="btn btn-sm btn-default pms-move-process-beside-btn"
            id="<%= id %>" value="moveBeside" title="Prozess aus der Reihe lösen"
            <% if (no_parent && no_child) { %> disabled <% } %>>
            <i class="fas fa-caret-right"></i>
          </button>
          <button class="btn btn-sm btn-default pms-add-process-beside-btn"
            id="<%= id %>" value="addBeside" title="Parallelen Prozess hinzufügen">
            <i class="fas fa-ellipsis-h"></i>
            <i class="fas fa-caret-right"></i>
          </button>
        </div>
        <div>
          <button class="btn btn-sm btn-default pms-move-process-below-btn"
            id="<%= id %>" value="moveBelow" title="Prozess nach unten verschieben"
            <% if (!movable || no_child) { %> disabled <% } %>>
            <i class="fas fa-caret-down"></i>
          </button>
          <button class="btn btn-sm btn-default pms-remove-connection-after-btn"
            id="<%= id %>" value="removeAfter" title="Verbindung darunter trennen"
            <% if (!child_movable) { %> disabled <% } %>>
            <i class="fas fa-cut"></i>
          </button>
          <button class="btn btn-sm btn-default pms-add-process-below-btn"
            id="<%= id %>" value="addBelow" title="Nachfolgerprozess hinzufügen"
            <% if (!child_addable) { %> disabled <% } %>>
            <div class="pms-add-process-icon"><i class="fas fa-ellipsis-v"></i></div>
            <i class="fas fa-caret-down"></i>
          </button>
        </div>
      </div>'
    )(_.extend(process, movable: process.get('editable'),
      no_child: process.get('children').isEmpty(),
      no_parent: process.get('parents').isEmpty(),
      child_addable: @child_movable || process.get('children').isEmpty(),
      child_movable: @child_movable,
      parent_movable: @parent_movable || false ))

  #
  # Methods
  #

  initialize: ->
    @collection = @model.get('processes')
    @listenTo(@collection, 'change:completed', @renderLater)
    @listenTo(@collection, 'change:parent_ids', @renderLater)
    @listenTo(@collection, 'destroy', @renderLater)
    @listenTo(@collection, 'task:add', @renderLater)
    @listenTo(@collection, 'task:remove', @renderLater)

  _canvas: ->
    @$('canvas')[0] || @$el.html("<canvas>").find("canvas")[0]

  render: (processViews) ->
    ###
    Override of Backbone.View#render. Draw the process graph onto the
    view's canvas element. The vertical positions of the process nodes
    are derived from the positions of the passed process views (passed
    as a Backbone.ChildViewContainer).
    ###
    @_processViews ?= processViews
    if @_processViews.isEmpty()
      @_canvas().width = 0
      @_canvas().height = 0
    else
      @_layoutProcesses()
      @_resizeCanvas()
      @_drawGraph()
      if !App.onSmallScreen()
        @_renderHotSpots()

  renderLater: ->
    ###
    Wait for process views to be relayouted and render afterwards (to
    pick up the correct vertical process view positions, which we need
    to position the process dots in the graph).
    ###
    processHandleList = document.getElementsByClassName("pms-process-handle")
    for p in processHandleList
      $(p).popover('hide')
    setTimeout(@render.bind(this), 0)

  #
  # Methods: Layout
  #

  _saveAction: (event) ->
    @action = event.currentTarget.value
    @firstProcess = App.Process.find(event.currentTarget.id)
    if @action == "removeAfter" && @firstProcess.get('children').size() == 1
      @_showLoadingSpinner()
      @_remove(@firstProcess, @firstProcess.get('children').first())
    else if @action == "removeBefore" && @firstProcess.get('parents').size() == 1
      @_showLoadingSpinner()
      @_remove(@firstProcess.get('parents').first(), @firstProcess)
    else
      @_setPopoverContent(@contentSubmit)
      App.vent.trigger("info",
        title: "Hinweis",
        description: "Klicke den Prozess an zu dem die Verbindung hergestellt/getrennt werden soll")

  _runAction: (event) ->
    App.vent.trigger("hide")
    @_showLoadingSpinner()
    @secondProcess = App.Process.find(event.currentTarget.id)
    @secondProcessChild = @secondProcess.get('children').first()
    if !@secondProcess.get("completed")
      switch @action
        when "addBelow" then @_addBelow(@firstProcess, @secondProcess)
        when "addBefore" then @_addBefore(@firstProcess, @secondProcess)
        when "addBeside" then @_addBeside(@firstProcess, @secondProcess)
        when "removeAfter" then @_remove(@firstProcess, @secondProcess)
        when "removeBefore" then @_remove(@secondProcess, @firstProcess)
    else if !@secondProcessChild.get("completed") && @action == "addBefore"
      @_addBefore(@firstProcess, @secondProcess)
    else
      App.vent.trigger("error",
        title: "Verbindung kann nicht hergestellt werden",
        description: "Eine mögliche Ursache ist, dass einer der Prozesse bereits abgeschlossen ist.
                      Eine Verschiebung dieses Prozesses ist dann nicht mehr möglich.")

  _remove: (parent, child) ->
    child.removeParent(parent).done =>
      App.vent.trigger("hide")

  _addBefore: (child, parent) ->
    # Remove old parents of child
    oldParents = child.get('parents')
    if !oldParents.isEmpty()
      oldParent = oldParents.first()
      parent.addParent(oldParent)
      child.removeParent(oldParent)
    # Add new parent to child
    child.addParent(parent).done =>
      App.vent.trigger("hide")

  _addBelow: (parent, child) ->
    # Save old parent from child
    oldParents = child.get('parents')
    if !oldParents.isEmpty()
      oldParent = oldParents.first()
      if oldParent.get("layoutColumn") == 1 && parent.get("layoutColumn") == 1
        child.removeParent(oldParent)
    # Remove old children from parent
    oldChildren = parent.get('children')
    if !oldChildren.isEmpty()
      oldChild = oldChildren.first()
      if oldChild.get("layoutColumn") == 1 && child.get("layoutColumn") == 1
        oldChild.removeParent(parent)
        # Add last child as new parent
        newParent = child
        newParent = newParent.get('children').first() while !newParent.get('children').isEmpty()
        oldChild.addParent(newParent)
    # Add new parent to child
    child.addParent(parent).done =>
      App.vent.trigger("hide")

  _addBeside: (parent, child) ->
    for p in child.get('parents').models
      child.removeParent(p)
    if parent.hasChild(child)
      child.addParent(parent).done =>
        App.vent.trigger("hide")
    else
      childIndex = parent.get('children').size()
      child.insertAfter(parent, childIndex + 1).done =>
        App.vent.trigger("hide")

  _moveAbove: (event) ->
    @_showLoadingSpinner()
    process = App.Process.find(event.currentTarget.id)
    parent = process.get('parents').first()
    children = process.get('children')
    grandparents = parent.get('parents')
    if !children.isEmpty()
      child = children.first()
      child.removeParent(process).done =>
        child.addParent(parent)
    if !grandparents.isEmpty()
      grandparent = grandparents.first()
      parent.removeParent(grandparent).done =>
        process.addParent(grandparent)
    process.removeParent(parent).done =>
      parent.addParent(process).done =>
        App.vent.trigger("hide")

  _moveBelow: (event) ->
    @_showLoadingSpinner()
    process = App.Process.find(event.currentTarget.id)
    parents = process.get('parents')
    child = process.get('children').first()
    grandchildren =  child.get('children')
    if !parents.isEmpty()
      parent = parents.first()
      process.removeParent(parent).done =>
        child.addParent(parent)
    if !grandchildren.isEmpty()
      grandchild = grandchildren.first()
      grandchild.removeParent(child).done =>
        grandchild.addParent(process)
    child.removeParent(process).done =>
      process.addParent(child).done =>
        App.vent.trigger("hide")

  _moveBeside: (event) ->
    @_showLoadingSpinner()
    process = App.Process.find(event.currentTarget.id)
    parents = process.get('parents').models
    children = process.get('children').models
    if parents.length > 0 && children.length > 0
      for p in parents
        process.removeParent(p)
        for c in children
          c.removeParent(process).done =>
            c.addParent(p).done =>
              App.vent.trigger("hide")
    else if parents.length > 0
      for p in parents
        process.removeParent(p).done =>
          App.vent.trigger("hide")
    else
      for c in children
        c.removeParent(process).done =>
          App.vent.trigger("hide")

  _cancel: (event) ->
    @_setPopoverContent(@contentInsertActions)

  _showLoadingSpinner: ->
    App.vent.trigger("info",
        spinner: true,
        title: "Wird geladen...")

  _setPopoverContent: (content) ->
    processHandleList = document.getElementsByClassName("pms-process-handle")
    for p in processHandleList
      $(p).popover('hide')
      $(p).attr('data-content', content(App.Process.find(p.id)))


  _layoutProcesses: ->
    ###
    The layout algorithm.
    ###
    currentRow = 1
    currentColumn = 1

    @collection.sort()

    @collection.each (process) ->
      # Detect when to move to the next layouting row.
      row = process.get('level') + 1
      if row != currentRow
        currentRow = row
        currentColumn = 1

      # Make sure each process is at least on the same column as the
      # left-most parent process (or further to the right if neeeded).
      if process.get('parents').length > 0
        parentColumn = _.min(process.get('parents').pluck('layoutColumn'))
        currentColumn = Math.max(currentColumn, parentColumn)

      process.set('layoutColumn', currentColumn)

      # If the process has children, reserve horizontal room for them
      # by skipping columns.
      currentColumn += Math.max(1, process.get('children').length)

  _resizeCanvas: ->
    @_adjustCanvasWidth()
    @_adjustCanvasHeight()
    @trigger('resize', this, @_canvas().width, @_canvas().height)

  _adjustCanvasWidth: ->
    rightmostProcess = @collection.max (p) -> p.get('layoutColumn')
    if rightmostProcess
      numColumns = rightmostProcess.get('layoutColumn')
      if App.onSmallScreen()
        @_canvas().width = (numColumns + 1) * @BRANCH_SPACING
      else
        @_canvas().width = (numColumns + 2) * @BRANCH_SPACING

  _adjustCanvasHeight: ->
    processListEl = @_processViews.first().$el.parent()
    @_canvas().height = processListEl.height()

  #
  # Methods: Drawing
  #

  _drawGraph: ->
    context = @_canvas().getContext('2d')
    @_drawProcessDots(context)
    @_drawLinesBetweenProcesses(context)

  _processDotPosition: (process) ->
    processEl = @_processViews.findByModel(process).$el
    return {
      x: process.get('layoutColumn') * @BRANCH_SPACING
      y: processEl.position().top + (processEl.height() / 2)
    }

  _processColor: (process) ->
    if process.get('completed')
      @COMPLETED_COLOR
    else if process.get('completable')
      @COMPLETABLE_COLOR
    else
      @UNCOMPLETABLE_COLOR

  _drawProcessDots: (context) ->
    @collection.each (process) =>
      @_drawProcessDot(context, process, @_processColor(process))

  _drawProcessDot: (context, process, color)->
    pos = @_processDotPosition(process)
    context.beginPath()
    context.arc(pos.x, pos.y, @DOT_RADIUS, 0, 2 * Math.PI)
    context.fillStyle = color
    context.fill()
    context.closePath()

  _drawLinesBetweenProcesses: (context) ->
    @collection.each (process) =>
      process.get('children').each (successor) =>
        from = @_processDotPosition(process)
        to = @_processDotPosition(successor)

        # Don't draw the lines over the dots.
        from.y += @DOT_RADIUS
        to.y -= @DOT_RADIUS

        context.strokeStyle = @_processColor(successor)
        if from.x <= to.x
          @_drawStraightOrBranchingLine(context, from, to)
        else
          @_drawMergingLine(context, from, to)

  _drawStraightOrBranchingLine: (context, from, to) ->
    @_drawCurvedLine(context, from, to, from.y)

  _drawMergingLine: (context, from, to) ->
    @_drawCurvedLine(context, from, to, to.y - @CURVE_HEIGHT)

  _drawCurvedLine: (context, from, to, curveStartY) ->
    curveCenterX = (from.x + to.x) / 2
    curveCenterY = curveStartY + this.CURVE_HEIGHT / 2
    curveEndY = curveCenterY + this.CURVE_HEIGHT / 2
    context.beginPath()
    context.moveTo(from.x, from.y)
    context.lineTo(from.x, curveStartY)
    context.quadraticCurveTo(from.x, curveCenterY, curveCenterX, curveCenterY)
    context.quadraticCurveTo(to.x, curveCenterY, to.x, curveEndY)
    context.lineTo(to.x, to.y)
    context.stroke()
    context.closePath()

  #
  # Methods: Process Handles
  #

  _renderHotSpots: ->
    @_processHandles.call('remove') if @_processHandles
    @_insertMarkers.call('remove') if @_insertMarkers
    @_processHandles = new Backbone.ChildViewContainer()
    @_insertMarkers = new Backbone.ChildViewContainer()
    @collection.each (process) =>
      pos = @_processDotPosition(process)
      @_renderProcessHandle(process, pos)
      @_renderProcessInsertMarkers(process, pos)

  _renderProcessHandle: (process, pos) ->
      pos = @_processDotPosition(process)
      handle = new App.ProcessHandle
        model: process
        x: pos.x
        y: pos.y
        radius: @DOT_RADIUS
      @$el.append(handle.el)
      @_processHandles.add(handle)

  _renderProcessInsertMarkers: (process, pos) ->
      markers = new App.ProcessInsertMarkers
        graph: this
        model: process
        processPosition: pos
      @$el.append(markers.el)
      @_insertMarkers.add(markers)
