# Serves the adin pages for managing process templates.
class Admin::ProcessTemplatesController < Admin::AdminController
  before_action :require_permission

  # Show a page listing all process templates in the system.
  #
  # Returns a page based on the "admin/process_templates/index" template.
  def index
    @process_templates = ProcessTemplate.order(:name)
  end

  # Show a page for editing a process template.
  #
  # id - The process template's ID.
  #
  # Returns a page based on the "admin/process_templates/edit" template.
  #   The page is a Backbone.js application.
  def edit
    @template = ProcessTemplate.find(params[:id])
  end

  # Create a new process template and show it's edit page.
  #
  # Returns a redirect to #edit.
  def new
    @template = ProcessTemplate.create!(name: "Neue Prozessvorlage")
    redirect_to action: :edit, id: @template.id
  end

  private

  def require_permission
    return if current_user && current_user.can_edit_templates?
    redirect_to root_path
  end
end
