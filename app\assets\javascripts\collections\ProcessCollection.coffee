#=require "collections/Collection"
#=require "models/Process"

App.ProcessCollection = App.Collection.extend
  ###
  Collection class for Process.
  ###

  model: App.Process
  url: '/api/processes'

  _topLevelProcesses: ->
    @filter (p) -> p.get('parents').isEmpty()

  sort: (options) ->
    ###
    Override of Backbone.Collection#sort. Sort the collection so that
    each process comes after all of its (transitive) parents and all
    direct siblings with a lower child index (where "direct" means
    "with the same immediate parent"). While traversing the process
    graph, set each process's "level" attribute (the number of parents
    above the process on the longest path up).
    ###

    # We do a breadth-first traversal of the processes using
    # queues. While processing `currentLevelQueue`, we put each
    # visited process's children into `nextLevelQueue` until the
    # current queue is empty. After that, `nextLevelQueue` becomes the
    # new current queue. With this two-queue design, we can find out
    # when we change to a new child level.

    currentLevel = 0
    currentLevelQueue = @_topLevelProcesses()
    nextLevelQueue = []
    sortedModels = []

    until _.isEmpty(currentLevelQueue)
      process = currentLevelQueue.shift()
      process.set('level', currentLevel)
      sortedModels.unshift(process) # see below

      process.get('children').each (c) ->
        nextLevelQueue.push(c)

      if _.isEmpty(currentLevelQueue)
        currentLevel++
        currentLevelQueue = nextLevelQueue
        nextLevelQueue = []

    # As a process may have multiple parents, `sortedModels` may have
    # duplicates after the above loop. To reach our sorting goal, we
    # need to remove all but the last occurrence of each process
    # (otherwise, there will be at least one parent which comes after
    # its child).
    #
    # Clever as we are, we built up `sortedModels` in reverse order,
    # so we can use Underscore's uniq() (which removes all but the
    # *first* occurrence of an item) and then call reverse() to get
    # the end result.
    @models = _(sortedModels).uniq().reverse()

    # Trigger a "sort" event as expected from Backbone.Collection#sort.
    options ?= {}
    @trigger('sort', this, options) unless options['silent']

    # Allow call chaining.
    return this
