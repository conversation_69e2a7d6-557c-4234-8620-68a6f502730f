#=require "collections/UserCollection"
#=require "models/Model"
#=require "models/User"

App.OperatorGroup = App.Model.extend
  ###
  Client-side representation of an OperatorGroup.
  ###

  #
  # App.Model options
  #

  jsonSingularRoot: 'operator_group'
  jsonPluralRoot: 'operator_groups'

  relations: [
    type: Backbone.HasMany
    key: 'users'
    keySource: 'user_ids'
    relatedModel: App.User
    collectionType: App.UserCollection
    includeInJSON: false
  ]
