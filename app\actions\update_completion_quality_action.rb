# An Action which updates the completion quality of a task.
# It ensures that the task is completed before allowing the update,
# and that the new quality value is within the acceptable range.
class UpdateCompletionQualityAction < Action
  # Initializes the action.
  #
  # subject - The task to modify.
  # quality - The new completion quality value to set.
  def initialize(subject, quality)
    super(subject)
    @quality = quality
  end

  # See Action.
  # Checks if the user is allowed to edit the task and if the task is completed.
  def allowed?(user)
    subject.annotatable_by_user?(user) && subject.completed?
  end

  # See Action.
  # Updates the completion quality
  def do_execute!
    subject.update!(completion_quality: @quality)
  end
end
