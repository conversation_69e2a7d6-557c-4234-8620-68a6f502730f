App.Behaviors.Editable = Marionette.Behavior.extend
  ###
  Behavior for mapping attributes of a view's model to child elements
  which allow the corresponding attribute to be directly edited.

  Usage
  =====

  behaviors:
    Editable:
      attribute1:
        type: 'string'
        guard: 'attribute1_editable'
        target: '.target'
        inputClass: 'input-class'
      attribute2:
        type: 'date'
        ...

  For every attribute you want to make editable through an element,
  you define a `type`, a `guard` and a `target`. The type determines
  which kind of input element to expose to the user for editing;
  possible values are:

  - "string" for single-line string values (<input type="text">)
  - "text" for multi-line string values (<textarea>)
  - "date" for dates stored in ISO8601 format (<input type="date">)

  `guard` specifies the boolean attribute of the model which
  determines if the attribute is really editable, e.g. "editable".

  The chosen input element is shown if the specified `target` element
  is clicked or tapped (the target element is hidden while
  editing). If specified, the input element is given the CSS classes
  specified in the `inputClass` option.

  When the user completes the editing, the model attribute is set to
  the given value and saved to the server, and the input element is
  replaced with the original target element again.

  Manual Trigger
  ==============

  As an alternative to edit-on-click, a view can manually trigger the
  editing process by calling

      @triggerMethod('editable:edit', attr)

  `attr` is the name of an attribute specified in the Editable
  configuration. To disable edit-on-click completely, add `trigger:
  'manual'` to the attribute's configuration, like this:

      behaviors:
        Editable:
          ...
          name:
            type: 'string'
            trigger: 'manual'
            ...
          ..
  ###

  onRender: ->
    for attr of @options when @_isEditable(attr)
      unless @options[attr].trigger == 'manual'
        @_markTargetEditable(attr)
        @_addClickHandlerToTarget(attr)

  _isEditable: (attr) ->
    guardAttr = @options[attr].guard      
    if guardAttr
      @view.model.get(guardAttr) == true
    else if @view.editable == false
      false    
    else
      true

  _markTargetEditable: (attr) ->
    @$(@options[attr].target).addClass('pms-editable')

  _addClickHandlerToTarget: (attr) ->
    handler = @onEditableEdit.bind(this, attr)
    @$(@options[attr].target).on('click', handler)

  onEditableEdit: (attr) ->
    ###
    Handles "editable:edit" events, which can be used by the view to
    manually trigger an edit, as well as click-to-edit events.
    ###
    switch @options[attr].type
      when 'string'
        new App.StringInput(this, attr, @options[attr])
      when 'text'
        new App.TextInput(this, attr, @options[attr])
      when 'date'
        new App.DateInput(this, attr, @options[attr])
      when 'material_amount'
        # FIX ME this is only a temporally solution because backbone doesn't map material_task correkt
        new App.MaterialAmountInput(this, attr, @options[attr])
      when 'material_amount_template'
        # FIX ME this is only a temporally solution because backbone doesn't map material_task_template correkt
        new App.MaterialAmountTemplateInput(this, attr, @options[attr])
      when 'number'
        new App.NumberInput(this, attr, @options[attr])
      else
        throw new Error("Unknown attribute type '#{type}'")

  onEditingEnded: (attr) ->
    @_addClickHandlerToTarget(attr) unless @options[attr].trigger == 'manual'


class App.Input
  ###
  Abstraction for controlling the process of editing an attribute, with
  a subclass for every attribute type.
  ###

  # Set to true in a subclass if pressing the Enter key should end editing
  # and save the edited model attribute. True by default.
  completeOnSave: true

  inputElement: ->
    ###
    Overridden by subclasses to return the input element used for the
    editing process.
    ###

  formatUpdatedAttributeValue: (value) ->
    ###
    Return the HTML that the target element should contain after
    editing has ended, i.e. the formatted version of the edited model
    attribute's value. The default implementation applies
    App.TemplateHelpers.valueOrPlaceholder() to the value.
    ###
    App.TemplateHelpers.valueOrPlaceholder(value)

  onStartEditing: ->
    ###
    Can be overridden by subclasses to define additional actions that should
    be performed when the user has started te editing process. The target
    element has already been replaced by the input element in the DOM when
    this method is called.
    ###

  onEndEditing: ->
    ###
    Can be overridden by subclasses for performing additional actions after
    the editing process was completed or cancelled by the user.
    ###

  constructor: (editable, attr, options) ->
    ###
    Make `view`, `attr` and `options` available to subclass methods and
    call initialize().
    ###
    @editable = editable
    @view = editable.view
    @model = @view.model
    @attr = attr
    @options = options
    @target = @view.$(options.target)
    @_startEditing()

  _startEditing: ->
    ###
    Replace the target element with the input returned by @inputElement()
    and register event handlers.
    ###
    @input = @inputElement()
    @input.attr('class', @options.inputClass)
    @target.replaceWith(@input)
    @input.focus()
    @input.select()
    @onStartEditing()
    @input.on('keyup', @_onKeyup.bind(this))

  _onKeyup: (event) ->
    ###
    Save the modified value if Enter is pressed, and cancel the editing
    process (without saving) if the user presses Escape.
    ###
    if event.keyCode == 27 # Escape
      @_cancelEditing()
    if event.keyCode == 13 and @saveOnEnter # Enter
      event.stopPropagation()
      event.preventDefault()
      @input.blur() # causes the blur event handler to run

  completeEditing: ->
    ###
    Save the model with the modified attribute value and restore the
    (updated) target element.
    ###
    oldValue = @model.get(@attr)
    newValue = @input.val()
    @target.html(@formatUpdatedAttributeValue(newValue))
    @input.replaceWith(@target)

    if newValue != oldValue
      @model.save(@attr, newValue, patch: true).fail ->
        App.vent.trigger 'error',
          title: 'Speichern fehlgeschlagen :-('
          description: """
          Deine Änderung konnte leider nicht auf dem Server gespeichert werden.
          <br>
          <a href='javascript:location.reload()'>
            Lade die Seite neu
          </a>
          und versuch es nochmal, oder
          <a href='mailto:<EMAIL>'>
            meld dich bei uns.
          </a>
          """

    @onEndEditing()
    @editable.onEditingEnded(@attr)

  _cancelEditing: ->
    ###
    Discard any changes made in the input element and restore the
    target element.
    ###
    @input.val(@model.get(@attr))
    @input.blur() # cause the blur event handler to run

class App.StringInput extends App.Input
  ###
  App.Input subclass for `string` attributes.
  ###
  saveOnEnter: true

  constructor: ->
    super
    @input.on('keydown', @_onKeydown.bind(this))

  _onKeydown: (event) ->
    ###
    Prevent propagation Enter key presses to avoid side effects.
    ###
    if event.keyCode == 13
      event.stopPropagation()
      event.preventDefault()

  inputElement: ->
    $('<input type="text">').val(@model.get(@attr))

  onStartEditing: ->
    @input.on('blur', @completeEditing.bind(this))

class App.TextInput extends App.Input
  ###
  App.Input subclass for `text` attributes.
  ###
  saveOnEnter: false

  formatUpdatedAttributeValue: (value) ->
    App.TemplateHelpers.formatText(value, @view.model.get('process')?.get('run').get('assignment').id)

  inputElement: ->
    $('<textarea rows="6"></textarea>').text(@model.get(@attr))

  onStartEditing: ->
    # Prevent editable opening when clicking on links (inserted with regex when referencing assignments)
    if event.target.tagName is 'A'
      @completeEditing()
    else
      setTimeout (=> @input[0].selectionStart = @input[0].selectionEnd = 10000), 0
      @input.on('blur', @completeEditing.bind(this))

class App.DateInput extends App.StringInput
  ###
  App.Input subclass for `date` attributes.
  ###
  saveOnEnter: true

  inputElement: ->
    if Modernizr.inputtypes.date
      $('<input type="date">').val(@model.get(@attr))
    else
      $('<input type="text" id="pms-date-field">').val(@model.get(@attr))

  formatUpdatedAttributeValue: (value) ->
    App.TemplateHelpers.prettyDate(value)

  onStartEditing: ->
    $("#pms-date-field").datepicker
      dateFormat: "yy-mm-dd"

    @input.focus()
    @input.select()

    if Modernizr.inputtypes.date
      @input.on('blur', @completeEditing.bind(this))
    else
      @input.on('change', @completeEditing.bind(this))

class App.MaterialAmountInput extends App.Input
  ###
  App.Input subclass for `string` attributes.
  ###
  saveOnEnter: true

  constructor: ->
    super
    @input.on('keydown', @_onKeydown.bind(this))
    @model = new App.ArticleTask(@view.model, parse: true)

  _onKeydown: (event) ->
    ###
    Prevent propagation Enter key presses to avoid side effects.
    ###
    if event.keyCode == 13
      event.stopPropagation()
      event.preventDefault()

  inputElement: ->
    $('<input type="text">').val(@model.get(@attr))

  onStartEditing: ->
    @input.on('blur', @completeEditing.bind(this))

class App.MaterialAmountTemplateInput extends App.Input
  ###
  App.Input subclass for `string` attributes.
  ###
  saveOnEnter: true

  constructor: ->
    super
    @input.on('keydown', @_onKeydown.bind(this))
    @model = new App.ArticleTaskTemplate(@view.model, parse: true)

  _onKeydown: (event) ->
    ###
    Prevent propagation Enter key presses to avoid side effects.
    ###
    if event.keyCode == 13
      event.stopPropagation()
      event.preventDefault()

  inputElement: ->
    $('<input type="text">').val(@model.get(@attr))

  onStartEditing: ->
    @input.on('blur', @completeEditing.bind(this))

class App.NumberInput extends App.Input
  ###
  App.Input subclass for `number` attributes.
  ###
  saveOnEnter: true

  inputElement: ->
    $('<input type="number">').val(@model.get(@attr))

  onStartEditing: ->
    @input.focus()
    @input.select()
    @input.on('blur', @completeEditing.bind(this))