# Action subclass for actions which create a record. It offers an
# additional attribute #created_record to give access to the created
# record.
#
# Examples
#
#   action = CreateTaskAction.new(...)
#   action.execute!
#   puts action.created_record
#   #=> #<Task id:...>
class CreateAction < Action
  # The record that was created by the action.
  attr_reader :created_record

  # Implementation of Action#do_execute! which sets the
  # #created_record attribute. Subclasses should override #do_create!
  # instead of this method.
  #
  # Returns nothing.
  def do_execute!
    @created_record = do_create!
  end

  protected

  # Specifies the behavior of #execute! and the record returned by
  # #created_record. To be overridden by subclasses.
  #
  # Returns the record to set #created_record to.
  def do_create!
  end
end
