require 'zip'

# Provides a REST API for accessing, uploading and deleting machine
# attachments.
class Api::MachineAttachmentsController < ApplicationController
  # Upload an attachment.
  #
  # file    - The file (ActionDispatch::Http::UploadedFile) to
  #           upload. Includes the filename to use.
  # machine_id - ID of the machine to attach the file to.
  #
  # Returns the new machine attachment's metadata as JSON.
  #   The filename might have been altered to avoid name conflicts.
  # Raises IOError if writing the attachment file fails.

  helper_method :clear_filename

  def upload
    machine = Machine.find(params[:machine_id])
    upload = params[:file]

    #if machine.annotatable_by_user?(current_user)
      attachment = create_attachment(machine, upload, current_user)
      begin
        attachment.store(upload)
        ThumbnailService.thumbnail(attachment.file_path) if attachment.image?
        render status: :created, json: attachment
      rescue IOError => e
        attachment.destroy!
        raise e
      rescue SystemCallError => e
        attachment.destroy!
        raise e
      rescue Errno::ENOENT => e
        attachment.destroy!
        raise e
      rescue Errno::EACCES => e
        attachment.destroy!
        raise e
      rescue Errno::EPROTO => e
        attachment.destroy!
        raise e
      end

    #else
    #  render status: :forbidden, nothing: true
    #end
  end

  # Get all attachments of a machine as a ZIP archive.
  #
  # machine_id  - ID of the machine to get the attachments of.
  #
  # Returns the attachments as a ZIP archive.
  def retrieve_all
    machine_id = params[:machine_id]
    attachments = MachineAttachment.where(machine_id: machine_id)

    archive = Tempfile.new(["pms-attachments-zip", ".zip"])
    Zip::OutputStream.open(archive) do |z|
      attachments.each do |attachment|
        z.put_next_entry(attachment.filename)
        z << IO.read(attachment.file_path, mode: "rb")
      end
    end

    send_file archive.path
  end

  # Get the contents of an attachment file.
  #
  # machine_id  - ID of the machine the attachment is attached to.
  # filename - The attachment's filename.
  #
  # Returns the attachment file's contents with an appropriate
  #   Content-Type header.
  def retrieve
    machine_id = params[:machine_id]
    filename = params[:filename]
    attachment = MachineAttachment.find_by(machine_id: machine_id, filename: filename)
    send_file(attachment.file_path, disposition: :inline)
  end

  def download
    machine_id = params[:machine_id]
    filename = params[:filename]
    attachment = MachineAttachment.find_by(machine_id: machine_id, filename: filename)
    send_file(attachment.file_path, disposition: :download)
  end

  # Get a thumbnail image representation of an attachment
  # file. Currently only supported for image files.
  #
  # machine_id  - ID of the machine the attachment is attached to.
  # filename - The attachment's filename.
  #
  # Returns a thumbnail image with the same file type as the original.
  def retrieve_thumbnail
    machine_id = params[:machine_id]
    filename = params[:filename]
    attachment = MachineAttachment.find_by(machine_id: machine_id, filename: filename)

    thumbnail_path = ThumbnailService.thumbnail_path(attachment.file_path)
    ThumbnailService.thumbnail(attachment.file_path) unless File.exist?(thumbnail_path)

    send_file(thumbnail_path, disposition: :inline)
  end

  # Delete a machine attachment.
  #
  # machine_id  - ID of the machine the attachment is attached to.
  # filename - The attachment's filename.
  #
  # Returns nothing.
  def destroy
    machine = Machine.find(params[:machine_id])
    filename = params[:filename]
    attachment = MachineAttachment.find_by(machine: machine, filename: filename)
    if attachment.attachment_deletable_by_user?(current_user)
      if File.exist?(attachment.file_path)
        File.delete(attachment.file_path)
      end
      thumbnail_path = ThumbnailService.thumbnail_path(attachment.file_path)
      if File.exist?(thumbnail_path)
        File.delete(thumbnail_path)
      end
      attachment.destroy!
      status = :ok
    else
      status = :forbidden
    end

    render status: status, json: {}
  end

  private

  def create_attachment(machine, upload, user)
    filename = I18n.transliterate(upload.original_filename)
    type = upload.content_type
    MachineAttachment.create(machine: machine, filename: filename, content_type: type, created_by: user)
  end

  def create_archive_for_attachments(attachments, file)

  end
end
