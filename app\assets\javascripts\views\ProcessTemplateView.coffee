#=require "models/ProcessTemplate"
#=require "views/TaskTemplateView"
#=require "views/behaviors/Editable"
#=require "views/support/TemplateHelpers"

App.ProcessTemplateView = Marionette.CompositeView.extend
  ###
  Displays a process template in the admin area.
  ###

  #
  # Backbone.View optins
  #

  className: 'pms-process'

  events:
    'submit .pms-new-task-form': '_addTask'

  #
  # Marionette.View options
  #

  behaviors:
    Editable:
      name:
        type: 'string'
        target: '.pms-process-name'
        inputClass: 'form-control pms-process-name-input'
      instruction:
        type: 'text'
        target: '.pms-process-instruction'
        inputClass: 'form-control pms-process-instruction-input'

  templateHelpers:
    valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
    prettyDateTime: App.TemplateHelpers.prettyDateTime

  #
  # Marionette.ItemView options
  #

  template: JST['ProcessTemplateView']
  ui:
    taskNameInput: '.pms-new-task-name-input'

  #
  # Marionette.CollectionView options
  #

  childView: App.TaskTemplateView
  childViewContainer: '.pms-tasks'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @collection = @model.get('task_templates')
    @updated_at = @model.get('updated_at')

  onDomRefresh: ->
    ###
    Overrride of Backbone.View#onDomRefresh.
    ###
    @$el.sortable
      items: '.pms-task:not(.pms-completed)'
      handle: '.pms-task-move-btn'
      axis: 'y'
      update: @_saveTaskPosition.bind(this)

  #
  # Event handlers
  #

  _saveTaskPosition: (_event, ui) ->
    index = ui.item.index()
    task = @collection.get(ui.item.attr('data-pms-id'))
    task.save('position', index + 1, patch: true)

  _addTask: (event) ->
    event.preventDefault()
    name = @ui.taskNameInput.val()
    @collection.create({name: name, operator: null, process_template_id: @model.id}, wait: true)
    @ui.taskNameInput.val('').focus()
