App.BoxLabelService =
  ###
  A service object for printing the labels for boxes which are related
  to a particular assignment. It is based on the DYMO Label Framework,
  for which documentation can be found at the following places:

  Sample:
  http://developers.dymo.com/2010/06/02/dymo-label-framework-javascript-library-samples-print-a-label/

  API Reference:
  http://labelwriter.com/software/dls/sdk/docs/DYMOLabelFrameworkJavaScriptHelp/index.html
  ###

  dlf: dymo.label.framework
  boxLabelPath: "/labels/box.dymo"

  initialize: (callback) ->
    ###
    Initialize the label printing service, then call the passed callback.

    callback - A function to call when initialization is done.

    Returns nothing.
    ###
    @dlf.trace = 1
    @dlf.init(callback)

  isAvailable: ->
    ###
    Determine whether it is possible to print labels.

    Returns true if the service is available, false otherwise.
    ###
    @dlf.getPrinters().length > 0

  listPrinters: ->
    ###
    Determine which label printers are available to the current
    machine.

    Returns the names of the found printers as array.
    ###
    printers = @dlf.getPrinters()
    localPrinters = _.filter(printers, (p) -> p.isLocal)
    remotePrinters = _.filter(printers, (p) -> !p.isLocal)
    localNames = _.pluck(localPrinters, 'name').sort()
    remoteNames = _.pluck(remotePrinters, 'name').sort()
    localNames.concat(remoteNames)

  renderPreview: (assignment) ->
    ###
    Render a box label for the passed assignment to a PNG image that
    serves as a preview. Useful during development.

    assignment - The assignment to print a label for.

    Returns the rendered label as (PNG) data URL, which can be directly
    used as the "src" attribute of an <img> tag.
    ###
    label = @_prepareBoxLabel(assignment)
    renderParams = @dlf.createLabelRenderParamsXml()
    output = @dlf.renderLabel(label, renderParams, '')
    "data:image/png;base64,#{output}"

  print: (assignment, printerName) ->
    ###
    Print a box label for the passed assignment using the specified
    printer.

    assignment - The assignment to print a label for.
    printerName - The name of the printer to print with. Must be one
                  of the printers returned by #listPrinters.

    Returns nothing.
    ###
    label = @_prepareBoxLabel(assignment)
    printParams = @dlf.createLabelWriterPrintParamsXml
      twinTurboRoll: @dlf.TwinTurboRoll.Right
    label.print(printerName, printParams)

  _prepareBoxLabel: (assignment) ->
    label = @_openBoxLabelFile()
    @_setLabelParameters(label, assignment)
    label

  _openBoxLabelFile: ->
    labelUrl = "#{document.location.origin}/#{@boxLabelPath}"
    @dlf.openLabelFile(labelUrl)

  _setLabelParameters: (label, assignment) ->
    label.setObjectText('id', assignment.id.toString())
    label.setObjectText('workgroup', @_assignmentWorkgroupNames(assignment))
    label.setObjectText('project', assignment.get('project'))
    label.setObjectText('name', assignment.get('name'))
    label.setObjectText('created', "Start: #{@_formatCreated(assignment)}")
    label.setObjectText('due', "Ende: #{@_formatDue(assignment)}")
    label.setObjectText('creator', App.user.fullName())
    label.setObjectText('link', @_assignmentUrl(assignment))

  _assignmentWorkgroupNames: (assignment) ->
    workgroupNames = assignment.get('planners').pluck('workgroup_name')
    workgroupNames.sort()
    _.uniq(workgroupNames).join(', ')

  _formatCreated: (assignment) ->
    @_formatDate(assignment.get('created_at'))

  _formatDue: (assignment) ->
    @_formatDate(assignment.get('due'))

  _formatDate: (date) ->
    if date
      moment(date).format('DD.MM.YYYY')
    else
      'unbekannt'

  _assignmentUrl: (assignment) ->
    "pms.izm.fraunhofer.de/a/#{assignment.id}"
