# Provides a JSON API for searching for articles.
class Api::ArticleSearchResultsController < ApplicationController
  # Search for article by name.
  #
  # query - The search term.
  #
  # Returns matching articles as JSON (using ArticleSearchResultSerializer).
  def search
    if params[:search].present?
      # remove all '%' from search params to prevent output of all articles
      params[:search].gsub! '%', ''
      if params[:search].length >= 2
        by_name = search_by("name", Article)#.includes(:main_class)
        # to add more search possibilitys like location, see Api::OperatorSearchResultsController
        result = (by_name).uniq
      else
        result = []
      end
    else
      # Don't return all articles at once to avoid too much traffic
      result = []
    end
    render json: result, root: :search_results
    # render json: result.to_json(:include => [:main_class, :sub_class], :root => true), root: :search_results
  end

  private

  def search_by(attribute, scope)
    scope.where("lower(#{attribute}) like lower(?)", "%#{params[:search]}%").where(archived: false)
  end
end
