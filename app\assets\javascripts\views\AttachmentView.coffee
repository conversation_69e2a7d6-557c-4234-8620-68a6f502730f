App.AttachmentView = Marionette.ItemView.extend
  ###
  Displays a link to an attachment.
  ###

  #
  # Backbone.View options
  #

  className: 'list-group-item pms-attachment'
  events:
    'click .pms-attachment-delete-btn': '_deleteAttachment'
    'change .pms-attachment-sticky-cb': (e) ->
      if(@model.get('sticky') != e.currentTarget.checked)
        @model.save('sticky', e.currentTarget.checked, patch: true)

  #
  # Marionette.ItemView options
  #

  template: JST['AttachmentView']

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @_deleting = false
    @listenTo(@model, 'sync', @render)

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      uploading: @model.isNew()
      deleting: @_deleting
      taskAnnotatable: @model.get('task').get('annotatable')
      url: @model.url()
      sticky: @model.get('sticky')

  #
  # Event handlers
  #

  _deleteAttachment: ->
    @model.destroy(wait: true)
    @_deleting = true
    @render()
