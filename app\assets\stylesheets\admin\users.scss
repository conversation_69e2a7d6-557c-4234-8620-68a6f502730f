// Styles for views of Admin::UsersController.

#operator_search {
  top: 60px;
  position: fixed;
  z-index: 99;
  width: auto;
  min-width: 50%;
}

.pms-admin-page .form-group {
  margin-top: 52px;
}

.pms-workgroup-list {

  margin-top: 20px;

  .label {
    margin-right: 0.4em;
  }

  .panel-heading {
    margin: -1px;
    display: block;
    font-weight: bold;
    padding: 10px 15px;
    border-top: 1px solid #AAAAAA;
    border-bottom: 1px solid #AAAAAA;

    .pms-badge-extern {
      background-color: #d9534f;
    }

    .pms-badge-count { background-color: #444; }
  }

  .list-group-item {
    border-style: solid;
    border-width: 1px 0px 0px 0px;
    margin: 1px;

    &:first-child {
      border: 0px;
    }
  }
}

#pms-user-operator-group-list label {
  font-weight: normal;

  .list-group-item {
    margin-top: -1px;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
  }
}

#pms-deactivate-user-dialog .modal-footer form,
#pms-deactivate-user-dialog .modal-footer div {
  display: inline;
}