#=require "models/Model"
#=require "models/Operator"

App.TaskTemplate = App.Model.extend
  ###
  Client-side representation of TaskTemplate.
  ###

  #
  # Backbone.Model options
  #

  urlRoot: '/api/task_templates'
  defaults:
    name: 'Neue Aufgabe'
    instruction: ''

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasOne,
    key: 'operator',
    keySource: 'operator',
    includeInJSON: 'id',
    relatedModel: App.Operator
  ,
    type: Backbone.HasMany
    key: 'article_task_templates'
    keySource: 'article_task_templates_ids'
    relatedModel: App.ArticleTaskTemplate
    collectionType: App.ArticleTaskTemplateCollection
    reverseRelation:
      key: 'task',
      keySource: 'task_id',
      includeInJSON: 'id'
  ,
    type: Backbone.HasMany
    key: 'machines'
    keySource: 'machine_ids'
    relatedModel: App.Machine
    collectionType: App.MachineCollection
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'task_template'
  jsonPluralRoot: 'task_templates'
