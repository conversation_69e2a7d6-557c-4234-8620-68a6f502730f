#=require "views/NotificationBar"
#=require "views/ProcessTemplateView"
#=require "views/support/TemplateHelpers"

App.ProcessTemplatePage = Marionette.LayoutView.extend
  ###
  The page shown when a process template is opened in the admin area.
  Instantiated in the admin/process_templates/edit Rails template.
  ###

  #
  # Backbone.View options
  #

  events:
    'click .pms-process-template-confirm-delete-btn': '_deleteProcessTemplate'

  #
  # Marionette.ItemView options
  #

  template: JST['ProcessTemplatePage']

  ui:
    saveButton: '.pms-process-template-save-btn'
    deleteButton: '.pms-process-template-delete-btn'

  templateHelpers:
    deleteConfirmationButton: App.TemplateHelpers.deleteConfirmationButton

  #
  # Marionette.Layout options
  #

  regions:
    main: '.pms-process-template-page-main'
    notifications: '#notifications'

  #
  # Methods
  #

  onRender: ->
    ###
    Override of Marionette.View#onRender.
    ###
    @main.show(new App.ProcessTemplateView(model: @model))
    @notifications.show(new App.NotificationBar)
    @ui.deleteButton.popover()

  #
  # Event handlers
  #

  _deleteProcessTemplate: ->
    @model.destroy().then =>
      document.location = '/admin/process_templates'
