{"_args": [["esprima@~1.1.1", "/pms/node_modules/escodegen"]], "_from": "esprima@>=1.1.1 <1.2.0", "_id": "esprima@1.1.1", "_inCache": true, "_installable": true, "_location": "/esprima", "_npmUser": {"email": "<EMAIL>", "name": "ariya"}, "_npmVersion": "1.4.3", "_phantomChildren": {}, "_requested": {"name": "esprima", "raw": "esprima@~1.1.1", "rawSpec": "~1.1.1", "scope": null, "spec": ">=1.1.1 <1.2.0", "type": "range"}, "_requiredBy": ["/escodegen"], "_resolved": "https://registry.npmjs.org/esprima/-/esprima-1.1.1.tgz", "_shasum": "5b6f1547f4d102e670e140c509be6771d6aeb549", "_shrinkwrap": null, "_spec": "esprima@~1.1.1", "_where": "/pms/node_modules/escodegen", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "bugs": {"url": "http://issues.esprima.org"}, "dependencies": {}, "description": "ECMAScript parsing infrastructure for multipurpose analysis", "devDependencies": {"complexity-report": "~0.6.1", "eslint": "~0.4.3", "istanbul": "~0.1.27", "jscs": "~1.2.4", "jslint": "~0.1.9", "json-diff": "~0.3.1", "optimist": "~0.6.0", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0"}, "directories": {}, "dist": {"shasum": "5b6f1547f4d102e670e140c509be6771d6aeb549", "tarball": "http://registry.npmjs.org/esprima/-/esprima-1.1.1.tgz"}, "engines": {"node": ">=0.4.0"}, "files": ["bin", "esprima.js", "test/compat.js", "test/reflect.js", "test/run.js", "test/runner.js", "test/test.js"], "homepage": "http://esprima.org", "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "main": "esprima.js", "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "name": "esprima", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/ariya/esprima.git"}, "scripts": {"analyze-complexity": "node tools/list-complexity.js", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 17 --silent -l -w esprima.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement -8 --branch -19 --function 100", "check-version": "node tools/check-version.js", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "generate-regex": "node tools/generate-identifier-regex.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity"}, "version": "1.1.1"}