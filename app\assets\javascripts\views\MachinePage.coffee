#=require "models/Machine"
#=require "views/MachineAttachmentsDialog"
#=require "views/behaviors/Editable"
#=require "views/support/TemplateHelpers"
#=require "views/OperatorSearchDialog"

App.MachinePage = Marionette.ItemView.extend
  ###
  Displays the machine data in the admin area.
  ###

  #
  # Backbone.Model optins
  #

  className: 'pms-machine pms-future'
  events:
    'click .pms-task-attachments-btn': '_showAttachmentsDialog'
    'click .pms-responsible-assign-btn': '_showAssignResponsibleDialog'
    'click .pms-deputy-assign-btn': '_showAssignDeputyDialog'
    
  #
  # Marionette.View options
  #

  # ui:
  #   nameInput: '.pms-task-name-input'
  #   instructionInput: '.pms-task-instruction-input'
  # 
  # behaviors:
  #   Editable:
  #     name:
  #       type: 'string'
  #       target: '.pms-task-name'
  #       inputClass: 'form-control pms-task-name-input'
  #     instruction:
  #       type: 'text'
  #       target: '.pms-task-instruction'
  #       inputClass: 'form-control pms-task-instruction-input'

  templateHelpers:
    prettyUser: App.TemplateHelpers.prettyUser
    prettyUserMachine: App.TemplateHelpers.prettyUserMachine
    
  #
  # Marionette.ItemView options
  #

  template: JST['MachinePage']

  modelEvents:
    'change:responsible' : 'render'
    'change:deputy' : 'render'

  #
  # Methods
  #

  # initialize: ->
  #   @listenTo @model.get('attachments'), 'add', @_adjustAttachmentsCounter
  #   @listenTo @model.get('attachments'), 'remove', @_adjustAttachmentsCounter

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      # assignedToUser: App.user?.isAssignedTo(@model)
      responsible: @model.get('responsible')?.toJSON() || null
      deputy: @model.get('deputy')?.toJSON() || null

  #
  # Event handlers
  #

  _showAttachmentsDialog: (event) ->
    event.preventDefault()
    dialog = new App.MachineAttachmentsDialog
      model: @model
      title: @model.get('name')
    dialog.show()

  _showAssignResponsibleDialog: (event) ->
    event.preventDefault()
    new App.OperatorSearchDialog().show (responsible) =>
        if responsible instanceof App.User
          $('#machine_responsible_id').val(responsible.id)
          operatorSpec = {id: responsible.id, type: responsible.get('type')}
          @model.set({responsible: operatorSpec})

  _showAssignDeputyDialog: (event) ->
    event.preventDefault()
    new App.OperatorSearchDialog().show (deputy) =>
        if deputy instanceof App.User
          $('#machine_deputy_id').val(deputy.id)
          operatorSpec = {id: deputy.id, type: deputy.get('type')}
          @model.set({deputy: operatorSpec})
