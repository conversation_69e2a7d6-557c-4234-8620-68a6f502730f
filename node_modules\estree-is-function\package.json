{"_args": [["estree-is-function@^1.0.0", "/pms/node_modules/scope-analyzer"]], "_from": "estree-is-function@>=1.0.0 <2.0.0", "_id": "estree-is-function@1.0.0", "_inCache": true, "_installable": true, "_location": "/estree-is-function", "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/estree-is-function-1.0.0.tgz_1513017165298_0.2710053368937224"}, "_npmUser": {"email": "<EMAIL>", "name": "goto-bus-stop"}, "_npmVersion": "5.6.0", "_phantomChildren": {}, "_requested": {"name": "estree-is-function", "raw": "estree-is-function@^1.0.0", "rawSpec": "^1.0.0", "scope": null, "spec": ">=1.0.0 <2.0.0", "type": "range"}, "_requiredBy": ["/scope-analyzer"], "_resolved": "https://registry.npmjs.org/estree-is-function/-/estree-is-function-1.0.0.tgz", "_shasum": "c0adc29806d7f18a74db7df0f3b2666702e37ad2", "_shrinkwrap": null, "_spec": "estree-is-function@^1.0.0", "_where": "/pms/node_modules/scope-analyzer", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/goto-bus-stop/estree-is-function/issues"}, "dependencies": {}, "description": "check if an AST node is a function of some sort", "devDependencies": {"acorn": "^5.2.1", "standard": "^10.0.3", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-nSCWn1jkSq2QAtkaVLJZY2ezwcFO161HVc174zL1KPW3RJ+O6C3eJb8Nx7OXzvhoEv+nLgSR1g71oWUHUDTrJA==", "shasum": "c0adc29806d7f18a74db7df0f3b2666702e37ad2", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsH5nAcMmP5UkVcCOf/B157vXk/y2U8fnYCiBW/ednOQIgcmCUopNjq69RA781RotinWso2wv3uvw4UyO4Znp9wOY="}], "tarball": "https://registry.npmjs.org/estree-is-function/-/estree-is-function-1.0.0.tgz"}, "gitHead": "38285099666f4ca31ed4b1390105700bf881c8e5", "homepage": "https://github.com/goto-bus-stop/estree-is-function", "keywords": ["arrow-function", "check", "detect", "estree", "function", "functiondeclaration", "functionexpression"], "license": "Apache-2.0", "main": "index.js", "maintainers": [{"name": "goto-bus-stop", "email": "<EMAIL>"}], "name": "estree-is-function", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/goto-bus-stop/estree-is-function.git"}, "scripts": {"test": "standard && node test.js"}, "version": "1.0.0"}