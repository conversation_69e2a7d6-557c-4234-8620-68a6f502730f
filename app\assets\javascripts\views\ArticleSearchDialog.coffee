#=require "collections/ArticleSearchResultCollection"
#=require "views/ArticleSearchResultItem"
#=require "views/SearchDialog"

App.ArticleSearchDialog = App.SearchDialog.extend
  ###
  A dialog that allows a user to search for and 
  assign a article to a task.
  ###

  #
  # Marionette.CollectionView options
  #

  childView: App.ArticleSearchResultItem

  #
  # App.SearchDialog options
  #

  title: 'Material zuweisen'
  searchLabel: 'Materialname'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    App.SearchDialog::initialize.call(this)
    @collection = new App.ArticleSearchResultCollection()
