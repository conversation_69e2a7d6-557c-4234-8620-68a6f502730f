# Provides a JSON API for creating, updating and deleting process
# templates.
class Api::ProcessTemplatesController < ApplicationController
  before_action :require_permission, only: [:create, :update, :destroy]

  # Return either all process templates or search for specific
  # templates by name.
  #
  # search - The search term to filter process templates with
  #          (optional).
  #
  # Returns the requested process templates as JSON.
  def index
    if params[:search].present?
      prefix = params[:search].gsub("'", "\\'")
      templates = ProcessTemplate
                    .where("lower(name) like lower('%#{prefix}%')")
                    .includes(:task_templates)
      result = as_json(templates.to_a, include_tasks: true)
    elsif params[:search] =~ /^\s*$/
      # It's only whitespace.
      result = { process_templates: [] }
    else
      result = ProcessTemplate.all.includes(:task_templates)
    end
    render json: result
  end

  # Create a new process template.
  #
  # process_template - Process template attributes.
  #                    :name        - The process template's name (mandatory).
  #                    :instruction - The process template's instruction
  #                                   (optional).
  def create
    if params[:copy_of]
      copy_of = PmsProcess.find(params[:copy_of])
      template_process = ProcessTemplate.create!(name: copy_of.name)
      copy_of.tasks.each do |task|
        TaskTemplate.create(name: task.name,
        instruction: task.instruction,
        process_template_id: template_process.id)
      end
      template_process.update!(updated_at: copy_of.updated_at)
    else
      template_process = ProcessTemplate.create!(permitted_params)
    end
    render json: template_process
  end

  # Update an existing process template.
  #
  # id               - The process template's ID.
  # process_template - Process template attributes (all optional).
  #                    :name        - The process template's new name.
  #                    :instruction - The process template's new instruction.
  #
  # Returns the updated process template as JSON.
  def update
    template = ProcessTemplate.find(params[:id])
    template.update!(permitted_params)
    render json: template
  end

  # Delete a process template.
  #
  # id - The process template's ID.
  #
  # Returns nothing.
  def destroy
    template = ProcessTemplate.find(params[:id])
    template.destroy!
    render json: {}
  end

  private

  def require_permission
    return if current_user.can_edit_templates?
    render(status: :forbidden, nothing: :true)
  end

  def permitted_params
    params.require(:process_template)
      .slice(:name)
      .permit!
  end
end
