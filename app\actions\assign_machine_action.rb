# Executed when a user assigns a machine to a task
class AssignMachineAction < Action
  # Initialize an AssignMachineAction.
  #
  # task     - The task to assign.
  # machine  - The machine to assign to.
  def initialize(task, machine)
    super(task)
    @machine = machine
  end

  # See Action.
  def allowed?(user)
    subject.reassignable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.machines << @machine
    subject.save!
  end
  
end
