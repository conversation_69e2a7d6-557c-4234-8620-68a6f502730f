App.Behaviors.ReactToSort = Marionette.Behavior.extend
  ###
  Listens for "sort" events of the view's collection and reorders
  the contained item views accordingly. Calls triggerMethod("reorder")
  on the view after each reordering (see Marionette.triggerMethod).
  ###

  collectionEvents:
    'sort': ->
      container = @view.$(@view.itemViewContainer)
      @view.collection.each (model) =>
        itemView = @view.children.findByModel(model)
        container.append(itemView.el)
      @view.triggerMethod('reorder')
