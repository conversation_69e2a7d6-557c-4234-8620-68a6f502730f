# Provides a JSON API for updating article task templates.
class Api::ArticleTaskTemplatesController < ApplicationController
  ##
  # Manipulate a article task template and return it, as well as all other models
  # whose state is affected by the change (PATCH /tasks/:id).

  # Update an existing task.
  #
  # id   - The article_task_template's ID.
  # article_task - ArticleTaskTemplate attributes (all optional).
  #    :amount   - the give amount of an article
  #
  # Returns the new article_task_template, as well as any other records which changed,
  #   as JSON.
  def update
    article_task_template = ArticleTaskTemplate.find(params[:id])
    action = new_update_action(article_task_template, permitted_update_params)
    execute_action_and_respond(action)
  end

  private

  def permitted_update_params
    params
      .require(:article_task_template)
      .permit(:amount)
  end

  # rubocop:disable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
  def new_update_action(article_task_template, params)
    if params[:amount]
      UpdateAttributeAction.new(article_task_template, :amount, params[:amount])
    end
  end
  # rubocop:enable Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity

end
