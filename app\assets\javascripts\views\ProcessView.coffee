#=require "models/Process"
#=require "views/NewProcessDialog"
#=require "views/TaskView"
#=require "views/behaviors/ConditionalClassNames"
#=require "views/behaviors/ConditionalElements"
#=require "views/behaviors/Editable"
#=require "views/behaviors/NameDataAttribute"
#=require "views/support/TemplateHelpers"

App.ProcessView = Marionette.CompositeView.extend
  ###
  Displays a process as part of an assignment page.
  ###

  #
  # Backbone.Model options
  #

  className: 'pms-process'

  events:
    'click .pms-process-header': '_onHeaderClicked'
    'show.bs.collapse .pms-process-body': '_onExpanding'
    'shown.bs.collapse .pms-process-body': '_onExpanded'
    'hide.bs.collapse .pms-process-body': '_onCollapsing'
    'hidden.bs.collapse .pms-process-body': '_onCollapsed'
    'click .pms-process-rename-btn': '_makeNameEditable'
    'click .pms-process-add-child-btn': '_createChild'
    'click .pms-process-delete-btn': '_deleteProcess'
    'click .pms-process-clone-btn': '_cloneProcess'
    'click .pms-process-create-template-btn': '_createTemplate'
    'submit .pms-new-task-form': '_createTask'
    'input .pms-new-task-name-input': '_enableNewTaskButton'
    'click .pms-process-outdated-dismiss-btn': '_dismissOutdated'
    'click .pms-process-outdated-icon': 'expand'
  #
  # Marionette.View options
  #

  ui:
    header: '.pms-process-header'
    name: '.pms-process-name'
    deleteButton: '.pms-process-delete-btn'
    addChildButton: 'pms-process-add-child-btn'
    cloneButton: 'pms-process-clone-btn'
    createTemplateButton: 'pms-process-create-template-btn'
    moveButton: '.pms-process-move-btn'
    body: '.pms-process-body'
    newTaskNameInput: '.pms-new-task-name-input'
    newTaskButton: '.pms-new-task-btn'
    attachmentsIcon: '.pms-task-attachments-icon'
    overdueIcon: '.pms-process-overdue-icon'
    processOptionsButton: '.pms-process-options-btn'
    pmsProcessOutdatedIcon: '.pms-process-outdated-icon'
    pmsProcessOutdatedDismissBtn: '.pms-process-outdated-dismiss-btn'

  behaviors:
    ConditionalClassNames:
      'pms-completed': 'completed'
      'pms-completed-bad': ->
        @model.get('tasks').some (task) -> task.get('completion_quality') == 2
      'pms-completable': 'completable'
      'pms-uncompletable': -> !@model.get('completed') && !@model.get('completable')
    ConditionalElements:
      '.pms-process-tag-pause': ->
        @model.get('tasks').some (task) -> task.get('name').match?(/(^|\s)pause($|\s)/i)
      '.pms-process-tag-immediate': ->
        @model.hasImmediateTaskForUser() && !@model.get('completed')
      '.pms-process-tag-near': ->
        @model.hasNearTaskForUser() && !@model.get('completed') && !@model.hasImmediateTaskForUser()
      '.pms-process-tag-assigned': ->
        @model.hasTaskForUser() && !@model.get('completed') && !@model.hasNearTaskForUser() && !@model.hasImmediateTaskForUser()
      '.pms-new-task-form': 'editable'
    Editable:
      name:
        type: 'string'
        guard: 'editable'
        target: '.pms-process-name'
        inputClass: 'form-control pms-process-name-input'
        trigger: 'manual'
      instruction:
        type: 'string'
        guard: 'editable'
        target: '.pms-process-instruction'
        inputClass: 'form-control pms-process-instruction-input'
      due:
        type: 'date'
        guard: 'editable'
        dateFormat: 'dddd, DD.MM.YYYY'
        target: '.pms-process-due'
        inputClass: 'pms-date-input pms-process-due-input'

    NameDataAttribute: {}

  templateHelpers:
    valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
    prettyDate: App.TemplateHelpers.prettyDate

    processPopoverContent: ->
      _.template("""
          <button class='btn btn-sm btn-default pms-process-rename-btn'
                  title='Prozess umbenennen'
                  <% if (!editable) { %> disabled <% } %>>
            <i class='fas fa-pencil-alt'></i>
          </button>

          <button class='btn btn-sm btn-default pms-process-clone-btn'
                  title='Prozess duplizieren'>
            <i class='far fa-clone'></i>
          </button>

          <button class='btn btn-sm btn-default pms-process-delete-btn'
                    title='Prozess löschen'
                    <% if (!deletable) { %> disabled <% } %>>
              <i class='far fa-trash-alt'></i>
          </button>

          <% if (App.user.get('can_edit_templates')) { %>
            <button class='btn btn-sm btn-default pms-process-create-template-btn'
                    title='Aus dem Prozess eine Prozessvorlage erzeugen'>
              <i class='fas fa-share-square'></i>
            </button>
          <% } %>
      """)(this)

    outdatedPopoverContent: ->
      _.template("""
        <div class='pms-outdated-popover'>
          <p>
            Zum Zeitpunkt des Kopieren des Durchlaufs, existierte eine Prozessvorlage mit gleichem Namen und neuen Änderungen.
          </p>
          <button class='btn btn-sm btn-default pms-process-outdated-dismiss-btn'
                  title='Ignorieren'>
            Ignorieren
          </button>
        </div>
      """)(this)

  #
  # Marionette.ItemView options
  #

  template: JST['ProcessView']

  modelEvents:
    'change:completable': 'render'
    'change:completed': 'render'
    'change': '_setButtonStates'
    'change:due': '_updateOverdueIcon'
    'change:layoutColumn': 'render'
    'move': 'render'

  #
  # Marionette.CollectionView options
  #

  childView: App.TaskView
  childViewContainer: '.pms-tasks'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @collection = @model.get('tasks')
    @_expanded = @_shouldBeExpandedInitially()
    @listenTo(@model.get('children'), 'add', @_rerenderWhenFirstChildAdded)
    @listenTo(@model.get('children'), 'remove', @_rerenderWhenLastChildRemoved)
    @listenTo(@model.get('tasks'), 'change:completion_quality', @_rerenderWhenTaskQualityChanged)

  _shouldBeExpandedInitially: ->
    _.include(@model.get('tasks').pluck('id'), App.taskToJumpTo)

  expand: ->
    ###
    Reveal the process view's body content.
    ###
    @ui.body.collapse('show')

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      expand: @_expanded
      hasChildren: !@model.get('children').isEmpty(),
      hasTaskForUser: @model.hasTaskForUser(),
      hasImmediateTaskForUser: @model.hasImmediateTaskForUser(),
      overdue: @model.isOverdue()
      mightBeOutdated: !!@model.get('name').match(/\s\(Alt\)/)
      index: @model.get('run').get('processes').indexOf(@model) + 1

  onDomRefresh: ->
    ###
    Override of Marionette.View#onDomRefresh.
    ###
    @$el.sortable
      items: '.pms-task:not(.pms-completed)'
      handle: '.pms-task-move-btn'
      axis: 'y'
      update: @_saveTaskPosition.bind(this)

  onRender: ->
    ###
    Override of Marionette.ItemView#onRender.
    ###
    @_setAttachmentsIcon()
    @ui.processOptionsButton.popover()
    @ui.pmsProcessOutdatedIcon.popover()

    # Replicate ProcessGraphView column layout on bigger screens
    if !App.onSmallScreen()
      offset = ((@model.get('layoutColumn') - 1) * 24)
      this.$el.css('margin-left', offset)

  #
  # Event Handlers
  #

  _onHeaderClicked: (event) ->
    @_setAttachmentsIcon()
    clickedDirectlyOnHeader = (event.target == @ui.header.get(0))
    clickedOnName = (event.target == @ui.name.get(0))
    @ui.body.collapse('toggle') if clickedDirectlyOnHeader or clickedOnName

  _onExpanding: ->
    @_expanded = true

  _onCollapsing: ->
    @_expanded = false
    true # don't prevent bubbling

  _onExpanded: ->
    @trigger('expand', this)

  _onCollapsed: ->
    @trigger('collapse', this)

  _rerenderWhenFirstChildAdded: ->
    if @model.get('children').length == 1
      # The process went from having no children to one child,
      # which causes the way the process is rendered to change.
      @render()

  _rerenderWhenLastChildRemoved: ->
    if @model.get('children').isEmpty()
      # The process went from having children to not having any,
      # so we need to rerender.
      @render()

  _rerenderWhenTaskQualityChanged: (task) ->
    if task.get('completed') == true
      # When a new task is added the view should not be rerendered.
      @render()

  _setButtonStates: ->
    @ui.deleteButton.attr('disabled', !@model.get('deletable'))
    @ui.addChildButton.attr('disabled', !@model.get('child_creatable'))
    @ui.moveButton.attr('disabled', !@model.get('movable'))

  _makeNameEditable: ->
    # See App.Editable
    @triggerMethod('editable:edit', 'name')
    @ui.processOptionsButton.popover('hide')

  _deleteProcess: ->
    @model.destroy(wait: true) # also deletes the view
    @ui.processOptionsButton.popover('hide')

  _createChild: ->
    new App.NewProcessDialog().show (template) =>
      run = @model.get('run')
      createOptions = {insert: {parent_id: @model.id}}
      if template.id
        createOptions.template_id = template.id
      else
        createOptions.name = template.get('name')
      run.get('processes').create(createOptions)

  _cloneProcess: ->
    run = @model.get('run')
    createOptions = {insert: {parent_id: @model.id}}
    createOptions.copy_of = @model.id
    run.get('processes').create(createOptions)
    @ui.processOptionsButton.popover('hide')

  _createTemplate: ->
    newProcessTemplate = new App.ProcessTemplate()
    newProcessTemplate.save(copy_of: @model.id).done =>
      document.location = "/admin/process_templates/#{newProcessTemplate.id}/edit"
    @ui.processOptionsButton.popover('hide')

  _createTask: (event) ->
    # Don't submit the form, thereby reloading the page.
    event.preventDefault()

    name = @ui.newTaskNameInput.val()
    @collection.create({name: name}, {wait: true})

    # Make the task name form ready for adding the next task.
    @ui.newTaskNameInput.val('')
    @ui.newTaskNameInput.focus()
    @_enableNewTaskButton()

  _saveTaskPosition: (_event, ui) ->
    index = ui.item.index()
    task = @collection.get(ui.item.attr('data-pms-id'))
    task.save('position', index + 1, patch: true)

  _enableNewTaskButton: ->
    trimmedTaskName = @ui.newTaskNameInput.val().trim()
    @ui.newTaskButton.prop("disabled", trimmedTaskName.length == 0)

  _setAttachmentsIcon: ->
    attachmentsAvailable = @collection.models.some (task) ->
      task.get('attachments').length > 0
    icon = @ui.attachmentsIcon
    if attachmentsAvailable
      icon.removeClass('invisible')
    else
      icon.addClass('invisible')

  _updateOverdueIcon: ->
    if @model.isOverdue()
      @ui.overdueIcon.show()
    else
      @ui.overdueIcon.hide()

  _dismissOutdated: ->
    @ui.pmsProcessOutdatedIcon.popover('hide')
    @ui.pmsProcessOutdatedIcon.hide()
    @model.save('name', @model.get('name').replace(/\s\(Alt\)/, ''), patch: true).fail ->
        App.vent.trigger 'error',
          title: 'Speichern fehlgeschlagen :-('
          description: """
          Deine Änderung konnte leider nicht auf dem Server gespeichert werden.
          <br>
          <a href='javascript:location.reload()'>
            Lade die Seite neu
          </a>
          und versuch es nochmal, oder
          <a href='mailto:<EMAIL>'>
            meld dich bei uns.
          </a>
          """
    @render()
