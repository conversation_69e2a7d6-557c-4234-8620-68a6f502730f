#=require "collections/Collection"
#=require "models/OperatorGroup"
#=require "models/User"

App.OperatorSearchResultCollection = App.Collection.extend
  ###
  Collection class for OperatorSearchDialog search results. The
  collection is polymorphic; it contains a mix of users and operator
  groups as both of them can be assigned to a task.
  ###

  url: "/api/search/operators"
  jsonRoot: "search_results"

  model: (attrs) ->
    switch attrs.type
      when "user"
        App.User.findOrCreate(attrs, parse: true)
      when "operator_group"
        App.OperatorGroup.findOrCreate(attrs, parse: true)

  modelId: (attrs) ->
    "#{attrs.type}:#{attrs.id}"
