#= require "collections/TaskCollection"
#= require "models/Model"
#= require "models/Task"

App.Process = App.Model.extend
  ###
  The Client-side representation of PmsProcess.
  ###

  #
  # Backbone.Model options
  #

  urlRoot: '/api/processes'

  defaults:
    name: '<PERSON><PERSON><PERSON> Prozess'

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasMany
    key: 'parents'
    keySource: 'parent_ids'
    relatedModel: 'Process'
    collectionType: 'ProcessCollection'
  ,
    type: Backbone.HasMany
    key: 'children'
    keySource: 'child_ids'
    relatedModel: 'Process'
    collectionType: 'ProcessCollection'
  ,
    type: Backbone.HasMany
    key: 'tasks'
    keySource: 'task_ids'
    relatedModel: App.Task
    collectionType: App.TaskCollection
    includeInJSON: false
    reverseRelation:
      key: 'process'
      keySource: 'process_id'
      includeInJSON: 'id'
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'process'
  jsonPluralRoot: 'processes'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.Model#initialize.
    ###
    @listenTo(@get('tasks'), 'add', @_triggerTaskAdd)
    @listenTo(@get('tasks'), 'remove', @_triggerTaskRemove)
    @listenTo(@get('tasks'), 'change:operator', @_triggerChangeAfterAssign)

  hasChild: (other) ->
    ###
    Return true if the passed `other` process is a directly or
    transitively a child of this process.
    ###
    children = @get('children')
    if children.isEmpty()
      false
    else
      children.include(other) || (children.any (c) -> c.hasChild(other))

  getTasksForUser: ->
    ###
    Return all tasks of the process which are assigned to App.user or
    to an operator_group the user belongs to.
    ###
    @get('tasks').filter (t) ->
      t.get('operator') == App.user or
      (t.get('operator') and
      t.get('operator').get('type') == 'operator_group' and
      t.get('operator').get('users').include(App.user))

  hasTaskForUser: ->
    ###
    Return true if the process contains a task which is assigned to
    App.user or to an operator_group App.user belongs to.
    ###
    @getTasksForUser().length > 0

  hasNearTaskForUser: ->
    ###
    Return true if the process contains a task which is assigned to
    App.user and is after a comnpletable task.
    ###
    for task in @getTasksForUser()
      previousTask = task.predecessors()[0]
      if previousTask and previousTask.get('completable')
        return true
    false

  hasImmediateTaskForUser: ->
    ###
    Return true if the process contains a task which is assigned to
    App.user and is completable.
    ###
    @getTasksForUser().filter (t) -> t.get('completable')
      .length > 0


  addParent: (other) ->
    ###
    Tell the server to make `other` a parent of this process.
    ###
    @save('add_parent', other.id, patch: true).done =>
      @trigger('addParent', this, other)

  removeParent: (parent) ->
    ###
    Tell the server to remove `parent` from the list of this
    process's parents.
    ###
    @save('remove_parent', parent.id, patch: true).done =>
      @trigger('removeParent', this, parent)

  insertAfter: (parent, childIndex) ->
    ###
    Tell the server to move the underneath `parent` at the passed
    child index.
    ###
    options =
      parent_id: parent.id,
      child_index: childIndex
    @save('insert', options, patch: true).done =>
      @trigger('move')

  isOverdue: ->
    @get('due') && new Date().setHours(0,0,0,0) > new Date(@get('due')) && !@get('completed')

  #
  # Event handlers
  #

  _triggerTaskAdd: (task) ->
    @trigger('task:add', task, this)

  _triggerTaskRemove: (task) ->
    @trigger('task:remove', task, this)

  _triggerChangeAfterAssign: ->
    # If a task has been assigned to the current user and the proces
    # has no task assigned this way before, the process has changed as
    # hasTasksForUser now returns true. Trigger a "change" event so
    # that listeners can react to this change.
    @trigger('change', this)
