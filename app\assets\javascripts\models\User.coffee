#=require "models/Model"

App.User = App.Model.extend
  ###
  Client-side representation of a User.
  ###

  #
  # Backbone.Model options
  #

  urlRoot: '/api/users'

  #
  # App.Model options
  #

  jsonSingularRoot: 'user'
  jsonPluralRoot: 'users'

  #
  # Methods
  #

  fullName: ->
    ###
    Return the user's first and last name as a single string.
    ###
    "#{@get('first_name')} #{@get('last_name')}"

  isAssignedTo: (task) ->
    ###
    Return whether the user or an operator group the user belongs to
    is assigned to the passed task.
    ###
    operator = task.get('operator')
    if operator instanceof App.OperatorGroup
      operator.get('users').contains(this)
    else
      operator == this
