# Executed when a user deletes a process from a run.
class DeleteProcessAction < Action
  # Initializes the action.
  #
  # process - The process to delete.
  def initialize(process)
    super(process)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    call!(RemoveProcessAction, subject)
    subject.destroy!
    handle_possibly_completed_run
  end

  private

  def handle_possibly_completed_run
    # If, after deleting a process, all remaining processes in the
    # containing run are completed, we consider the run completed
    # as well.
    return unless subject.run.done? && subject.run.state != 'draft' && subject.run.state != 'template'
    update!(subject.run, state: "completed")
    update!(subject.run, completed_at: Time.now)
    mark_changed(subject.run.assignment)
  end
end
