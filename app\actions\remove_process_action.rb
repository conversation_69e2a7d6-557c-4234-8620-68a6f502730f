# Cleanly removes a process from a run's process graph without actually
# deleting the process. This is a building block for other actions
# which do process graph manipulation (such as MoveProcessAction).
class RemoveProcessAction < Action
  # Initialize the action.
  #
  # process - The process to remove from the containing run.
  def initialize(process)
    super(process)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    former_children = disconnect_subject_from_children
    connect_former_children_to_parents(former_children)
    disconnect_subject_from_parents
  end

  private

  def disconnect_subject_from_children
    deps = ProcessDependency.includes(:child).where(parent: subject)
    children = deps.map(&:child)
    children.each { |child| touch(child) }
    deps.delete_all
    children
  end

  def connect_former_children_to_parents(children)
    subject.parent_dependencies.includes(:parent).each do |dep|
      children_to_connect = children_unrelated_to(dep.parent, children)
      make_room_for_children(children_to_connect, dep.parent, dep.child_index)
      children_to_connect.each_with_index do |child, i|
        ProcessDependency.create!(parent: dep.parent,
                                  child: child,
                                  child_index: dep.child_index + i)
      end
    end
  end

  def children_unrelated_to(parent, children_to_filter)
    children_to_filter.select { |c| !c.depends?(parent) }
  end

  def make_room_for_children(children, parent, index)
    deps_to_update = parent.child_dependencies.where("child_index > ?", index)
	# Function update_all leads to problems with the adapter, switched to each loop
	deps_to_update.each{|d| d.update_attributes(:child_index => d[:child_index] + (children.size-1))}
  end

  def adopt_children(children, parent, child_index)
    children.each_with_index do |child, i|
      ProcessDependency.create!(parent: parent,
                                child: child,
                                child_index: child_index + i)
    end
  end

  def disconnect_subject_from_parents
    deps = ProcessDependency.includes(:parent).where(child: subject)
    deps.each { |dep| touch(dep.parent) }
    deps.delete_all
  end
end
