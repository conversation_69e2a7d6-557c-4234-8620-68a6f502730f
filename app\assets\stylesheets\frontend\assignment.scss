// Styles for assignment pages ("frontend/assignment" view and the
// Backbone templates of AssignmentPage and its nested views).

//
// Loading Message ("Laden ...")
//

.pms-assignment-loading-indicator {
  margin: 12px;
  text-align: center;
}


//
// General
//

// The "pms-editable" class is added by the Editable behavior
// (app/assets/javascripts/views/behaviors/Editable.coffee) to
// all elements that should be editable when clicked. On hover,
// we give these elements a subtile border and show a pointer
// finger cursor to show that the element is clickable.

.pms-editable {
  padding: 2px;
}

.pms-editable:hover {
  border: 1px solid #ccc;
  border-radius: 2px;
  padding: 1px;
  cursor: pointer;
}

//
// Assignment Header
//

@media (max-width: $screen-xs-max) {
  .pms-assignment-header {
    position: static !important;
  }
}

@media (min-width: 768px) {
  .pms-assignment-header {
    width: 749px;
  }
}

@media (min-width: 992px) {
  .pms-assignment-header {
    width: 969px;
  }
}

@media (min-width: 1200px) {
  .pms-assignment-header {
    width: 1169px;
  }
}

.pms-assignment-header {
  padding: 2px 0px 12px;
  background-color: $pms-background-gray;
  top: 50px;
  z-index: 100;
  position: fixed;

  .pms-assignment-details-btn {
    margin-left: 12px;
  }

  .pms-page-heading {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 100%;
    padding: 3px 18px;
    margin: 0 0 5px;
    text-align: left;
    border-radius: 0px;

    .pms-assignment-id {
      border: 1px solid #fff;
      border-radius: 3px;
      padding: 3px;
      margin-left: 3px;
    }

    .pms-assignment-board-count {
      border: 1px solid #fff;
      background-color: #fff;
      color: $brand-primary;
      border-radius: 3px;
      padding: 3px;
      margin-left: 6px;
    }

    .pms-assignment-copy-datapath-top {
      margin-left: auto;
      min-height: 28px;

      .pms-assignment-copy-datapath-btn {
        color: #fff;
        background-color: transparent;
        border: 0;
      }
    }
    @media (max-width: $screen-xs-max) {
      .pms-assignment-copy-datapath-top {
        display: none;
      }
    }
  }

  .pms-assignment-state-label {
    font-size: 100%;
    border: none;
    margin-left: 12px;
    margin-right: -3px;
  }

  .pms-assignment-confirm-print-label-btn {
    width: 100%;
  }

  .pms-assignment-cancel-reason {
    width: 100%;
    white-space: pre-line;
  }

  .pms-assignment-details {
    margin-top: 1em;
    margin-left: 0.6em;

    .pms-assignment-planners, .pms-assignment-observers, .pms-assignment-extern-workgroups {
      padding-left: 0;
      margin-bottom: 0;

      li {
        display: inline;
        list-style-type: none;
      }

      li:after {
        content: ', ';
      }

      li:last-child:after {
        content: '';
      }
    }

    .pms-creator {
      text-decoration: underline;
    }
  }

  .pms-assignment-datapath-container {
    display: flex;
    column-gap: 5px;
  }
}

//
// Run Tabs
//

@media (max-width: 768px) {
  .pms-run-views {
    padding-left: 5px;
  }
}

@media (min-width: 768px) {
  .pms-assignment-runs {
    width: 749px;
    margin-top: 97.5px;
  }

  .pms-run-views {
    padding-left: 35px;
  }
}

@media (min-width: 992px) {
  .pms-assignment-runs {
    width: 969px;
    margin-top: 75px;
  }
}

@media (min-width: 1200px) {
  .pms-assignment-runs {
    width: 1169px;
    margin-top: 75px;
  }
}

.pms-run-tabs {
  padding: $padding-base-vertical $padding-base-horizontal;
  padding: 0 12px;
  background-color: #ccc;
  border-bottom: 1px solid $pms-border-gray;

  li.pms-run-tabs-title {
    padding: 10px 10px 10px 0;
    font-weight: bold;
  }

  & > li.active > a {
    border: 1px solid #aaa;
    border-bottom: 1px solid white;
  }

  & > li:not(.active) > a {
    color: inherit;
  }
}

//
// Run Header
//

.pms-run-header {
  padding: 12px;
  border-bottom: 1px solid $pms-border-gray;

  .pms-run-state-label {
    font-size: 100%;
    border: none;
  }
}

.pms-run-alert {
  margin: 12px;
}

//
// Process Graph
//

.pms-process-graph {
  float: left;
  position: relative;

  body.pms-wait & {
    opacity: 0.5;
  }

  .pms-process-handle:hover {
    cursor: -moz-grab;
    cursor: -webkit-grab;
    opacity: 0.6;
  }

  .pms-process-insert-marker {
    border: 2px solid $brand-warning;
    background-color: white;
    display: none;

    &.draghover {
      background-color: $brand-warning;
    }
  }

  .pms-popover-insert-actions {
    width: 105px;

    .pms-process-icon {
      margin: 0px 8px;
    }
    .pms-add-process-icon {
      margin-bottom: -8px;
    }
    .pms-remove-connection-before-btn, .pms-remove-connection-after-btn {
      padding: 5px 6px;
      margin: 2px 0;
    }
    .pms-add-process-before-btn, .pms-add-process-below-btn {
      height: 30px;
      padding: 0 10px;
    }
    .pms-add-process-beside-btn {
      width: 30px;
      padding: 5px 0;
    }
  }
  .pms-popover-submit {

    font-size: 12px;
    width: 180px;
  }
}

//
// Process List
//

.pms-processes {
  padding: 12px 12px 0;
  // Make `offsetTop` of ProcessView elements relative to this container
  position: relative;
  // Make sure contained `float` and `clear` elements don't interfere
  // with the process graph (http://stackoverflow.com/questions/5725127/)
  overflow: hidden;

  .pms-process {
    margin-bottom: 12px;
    border: 1px solid $pms-border-gray;
    border-radius: 6px;

    &.pms-completed {
      background-color: $pms-completed-bg;
    }
  
    &.pms-completed-bad {
      background-color: $pms-completed-bad-bg;
    }

  }
}

@media (max-width: $screen-xs-max) {
  .pms-processes {
    font-size: 90%;
  }
}

//
// Process Header
//

.pms-process-header {
  margin: 0;
  padding: 6px;
  line-height: 1;

  &:hover {
    cursor: pointer;
  }

  .pms-process-number {
    position: relative;
    color: #ccc;
    font-size: 50%;
    display: inline-block;
    min-width: 16px;
    top: -12px;
  }

  .pms-process-name {
    display: inline-block;
    margin: 3px;
    
    .pms-process.pms-uncompletable & {
      color: #aaa;
    }
  }

  .pms-process-name-input {
    display: inline-block;
    width: 60%;
    height: 1.8em;
    margin: 0 0 -6px 0;
    font-weight: bold;
    font-size: inherit;
  }

  .pms-process-name-input:after {
    clear: none;
  }

  .pms-process-tag-assigned, .pms-process-tag-near, .pms-process-tag-immediate, .pms-process-tag-pause {
    font-size: 90%;
  }

  .pms-process-outdated-icon {
    color: #d9534f;
    font-size: 90%;
    padding: 0 6px;
  }

  .pms-process-due-icon {
    color: $brand-warning;
    font-size: 90%;
  }

  .pms-process-overdue-icon {
    font-size: 90%;
    color: #d9534f;
  }

  .pms-process-buttons {
    float: right;
    margin-left: 20px;

    .pms-task-attachments-icon {
      opacity: 1.0;
      font-size: 16px;
      padding: 2px 5px;
    }
  }
}

.pms-process-details {
  padding: 0 12px 12px 12px;
  margin: 0;
  border-bottom: 1px solid $pms-border-gray;
}

.pms-process-due-heading {
  width: 85px;
  float: left;
}

//
// Task List
//

.pms-tasks {
  padding: 0;
}

.pms-new-task-form {
  margin-top: -1px;
  margin-bottom: 0;
  padding: 12px;
  border-top: 1px solid $pms-border-gray;

  .pms-new-task-name-input {
    margin-right: 6px;
  }
}

.pms-task-completion-cancel-btn {
  border: 1px solid green;
  background-color: $pms-completed-bg;
  color: green;
}

@media (max-width: $screen-xs-max) {
  .pms-process-header .pms-process-name {
    font-size: 80%;
  }
}

//
// Task Item
//

$pms-disabled-check-button-bg: lighten($btn-default-border, 10%);

.pms-task {
  padding: 0 12px;
  background-color: lighten($pms-background-gray, 10%);
  border-bottom: 1px solid lighten($pms-border-gray, 10%);

  &.pms-completed {
    background-color: $pms-completed-bg;
  }

  &.pms-completable {
    background-color: $pms-completable-bg;
  }

  &.pms-completed-bad {
    background-color: $pms-completed-bad-bg;
  }

  .pms-task-anchor {
    position: relative;
    top: -100px;
  }

  .pms-task-header {
    margin-top: 0;
    padding-top: 10px;

    .pms-task.pms-completable & {
      font-weight: bold;
    }
  }

  .pms-task-num-attachments.nonzero {
    font-weight: bold;
  }

  .pms-task-num-pictures {
    font-weight: bold;
  }

  .pms-task-check-btn, .pms-task-name {
    vertical-align: middle;
  }

  .pms-task-name {
    text-overflow: ellipsis;
    overflow: hidden;
  }

  @media (max-width: $screen-xs-max) {
    .pms-task-name {
      font-size: 90%;
    }
  }

  .pms-task-name-input {
    display: inline-block;
    font-size: inherit;
    width: 60%;
    height: 2em;
  }

  .pms-task-instruction,
  .pms-task-note {
    white-space: pre-wrap;
  }

  .pms-task-move-btn {
    text-decoration: none;
    cursor: -moz-grab;
    cursor: -webkit-grab;
  }
}

.pms-task-check-btn {
  margin: 0;

  .pms-task.pms-future &,
  .pms-task.pms-completable &:disabled {
    background-color: $pms-disabled-check-button-bg;
  }
}

.pms-task-check-btn-icon {

  .pms-task.pms-completable & {
    // Make the icon only visible on hover (where
    // the background color becomes non-white)
    color: white;
  }

  .pms-task.pms-completable button:disabled & {
    // Make the icon invisible if the check button
    // is disabled.
    visibility: hidden;
  }

  .pms-task.pms-future & {
    // Make the icon completely invisible for
    // not-yet completable tasks
    background-color: $pms-disabled-check-button-bg;
    color: $pms-disabled-check-button-bg;
  }
}

.pms-machines, .pms-articles {
  tr {
    height: 32px;
  }
}

.pms-task-machine-state {
  width: 32px;
}

.pms-task-machine-name {

}

.pms-task-machine-room {
  width: 25%;
}

.pms-task-machine-building {
  width: 25%;
}

.pms-task-machine-details {
  width: 60px;
}

.pms-task-machine-delete {
  width: 40px;
  text-align: right;
}

.pms-task-article-state {
  width: 32px;
}

.pms-task-article-amount {
  width: 100px;
}

.pms-task-article-unit {
  width: 50px;
}

.pms-task-article-name {
   
}

.pms-task-article-details {
  width: 60px;
}

.pms-task-article-delete {
  width: 40px;
  text-align: right;
}

.pms-task-inline-heading1 {
  font-size: 1.7em;
  font-weight: bold;
}

.pms-task-inline-heading2 {
  font-size: 1.3em;
  font-weight: bold;
}