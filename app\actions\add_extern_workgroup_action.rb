# Executed when a planner makes the assignment available for extern users.t.
class AddExternWorkgroupAction < Action
  # Initialize the action.
  #
  # assignment - The assignment to make it available.
  # workgroup  - The workgroup to add as member.
  def initialize(assignment, workgroup)
    super(assignment)
    @workgroup = workgroup
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    AssignmentMembership.ensure_extern_workgroup(@workgroup, subject)
  end
end