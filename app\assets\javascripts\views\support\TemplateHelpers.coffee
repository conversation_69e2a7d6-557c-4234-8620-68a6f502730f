App.TemplateHelpers =
  ###
  A collection of useful functions for use inside of templates. Import
  them into a view through the `templateHelpers` option, like this:

  App.SomeView = Marionette.ItemView.extend
    ...
    templateHelpers:
      valueOrPlaceHolder: App.TemplateHelpers.valueOrPlaceHolder
      prettyDate: App.TemplateHelpers.prettyDate
      formatText: App.TemplateHelpers.formatText
    ...
  ###

  valueOrPlaceholder: (value) ->
    ###
    Return the HTML-escaped version of `value` or, if `value` is falsy,
    a placeholder message noting the absence of a value. Expected to
    be used in a non-escaping template placeholder ('<%= ... %>').
    ###
    if value then _.escape(value) else '<i class="pms-editable">(nicht angegeben)</i>'

  formatText: (text, pmsID) ->
    ###
    After Escaping the text, apply bold, italic, strikethrough, headline and color formatting.
    ###
    App.TemplateHelpers.valueOrPlaceholder(text)
      .replace(/%ID%/g, if pmsID? and !isNaN(Number(pmsID)) then pmsID else '%ID%')
      .replace(/\*\*(.*?)\*\*/g, '<b>$1</b>')
      .replace(/\_\_(.*?)\_\_/g, '<i>$1</i>')
      .replace(/~~(.*?)~~/g, '<del>$1</del>')
      .replace(/#([0-9]{1,7})/g, '<a href="/a/$1">#$1</a>')
      .replace(/\$([0-9a-fA-F]{3})(.*?)\$/g, '<span style="color: #$1;">$2</span>')
      .replace(/\$([a-zA-Z]{3,18})(.*?)\$/g, '<span style="color: $1;">$2</span>')
      .replace(/^# (.*?$)/gm, '<span class="pms-task-inline-heading1">$1</span>')
      .replace(/^## (.*?$)/gm, '<span class="pms-task-inline-heading2">$1</span>')
      .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>')
      .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>')

  prettyDate: (date) ->
    ###
    Return the passed ISO8601 date in a pretty human-readable format, or
    a placeholder message if `date` is falsy. Expected to be used in a
    non-escaping template placeholder ('<%= ... %>').
    ###
    value = if date then moment(date).format('dddd, DD.MM.YYYY')
    App.TemplateHelpers.valueOrPlaceholder(value)

  prettyDateTime: (date) ->
    ###
    Return the passed ISO8601 date and time in a pretty human-readable
    format. Expected to be used in a non-escaping template placeholder
    ('<%= ... %>').
    ###
    value = if date then moment(date).format('DD.MM.YYYY, HH:mm')
    App.TemplateHelpers.valueOrPlaceholder(value)

  prettyUser: (user, taskId, isTemplate = false) ->
    ###
    Return the full name of `user` turned into a link to the user's email
    address. If `user` is null/undefined or the current user, show a
    notice indicating this instead.
    ###
    if user
      if user.id == App.user.id
        _.template("""
          <i class="fas fa-user"></i>
          <%- name %> <b>(Sie)</b>
        """)(user)
      else if user.deactivated
        _.template("""
          <span class="text-muted"
                title="Dieser Benutzeraccount ist deaktiviert">
            <i class="fas fa-user"></i>
            <i><%- name %></i>
          </span>
        """)(user)
      else if not isTemplate
        task = App.Task.find(taskId)
        process = task.get("process")
        assignment = process.get("run").get("assignment")
        subject = "[" + assignment.get("project") + "] " +
                    assignment.get("name") + " (%23" +
                    assignment.id + ") - " +
                    process.get("name") + " - " + task.get("name")
        _.template("""
          <a href='mailto:<%= email %>?subject=<%= subject %>'>
            <i class="fas fa-user"></i>
            <%- name %>
          </a>
        """)(_.extend(user, subject: subject))
      else
        task_template = App.TaskTemplate.find(taskId)
        process = task_template.get("process")
        subject = "[ProcessTemplate] " + task_template.get("name")
        _.template("""
          <a href='mailto:<%= email %>?subject=<%= subject %>'>
            <i class="fas fa-user"></i>
            <%- name %>
          </a>
        """)(_.extend(user, subject: subject))
    else
      "<i>(niemand)</i>"

  prettyUserMachine: (user, taskId, isTemplate = false) ->
    ###
    TODO Integrate into prettyUser
    Return the full name of `user` turned into a link to the user's email
    address. If `user` is null/undefined or the current user, show a
    notice indicating this instead.
    ###
    if user
      if user.id == App.user.id
        _.template("""
          <i class="fas fa-user"></i>
          <%- name %> <b>(Sie)</b>
        """)(user)
      else if user.deactivated
        _.template("""
          <span class="text-muted"
                title="Dieser Benutzeraccount ist deaktiviert">
            <i class="fas fa-user"></i>
            <i><%- name %></i>
          </span>
        """)(user)
      else
        machine = App.Machine.find(taskId)
        subject = "[Maschine]"
        _.template("""
          <a href='mailto:<%= email %>?subject=<%= subject %>'>
            <i class="fas fa-user"></i>
            <%- name %>
          </a>
        """)(_.extend(user, subject: subject))
    else
      "<i>(niemand)</i>"

  prettyMachineState: (machine_state) ->
    switch machine_state
      when 'out_of_service' then '<i class="fas fa-circle pms-smaller-icon pms-state-red-icon" title="Außer Betrieb"></i>'
      when 'used' then '<i class="fas fa-circle pms-smaller-icon pms-state-yellow-icon" title="In Benutzung"></i>'
      when 'ready' then '<i class="fas fa-circle pms-smaller-icon pms-state-green-icon" title="Bereit"></i>'
      else '<i class="fas fa-circle pms-smaller-icon pms-state-red-icon" title="Status unbekannt"></i>'

  prettyMaterialState: (material_state) ->
    switch material_state
      when 'empty' then '<i class="fas fa-circle pms-smaller-icon pms-state-red-icon" title="Vestand Leer"></i>'
      when 'low' then '<i class="fas fa-circle pms-smaller-icon pms-state-yellow-icon" title="Bestand Niedrig"></i>'
      when 'filled' then '<i class="fas fa-circle pms-smaller-icon pms-state-green-icon" title="Bestand Voll"></i>'
      else '<i class="fas fa-circle pms-smaller-icon pms-state-gray-icon" title="Bestand unbekannt"></i>'

  prettyOperatorGroup: (group, currentUserIsMember, taskId, isTemplate = false) ->
    if group
      filteredMails = group.emails.filter (e) -> e != App.user.get('email')
      icon = if !group.has_machine then "fas fa-users" else "fas fa-cog"
      if not isTemplate
        task = App.Task.find(taskId)
        process = task.get("process")
        assignment = process.get("run").get("assignment")
        subject = "[" + assignment.get("project") + "] " +
                      assignment.get("name") + " (%23" +
                      assignment.id + ") - " +
                      process.get("name") + " - " + task.get("name")
        _.template("""
          <span title="<%= user_names.join(", ") %>" >
            <a href="mailto:<%= filteredMails.join("; ") %>?subject=<%= subject %>">
            <i class="<%= icon %>"></i>
            <%- name %>
            </a>
            <% if (currentUserIsMember) { %>
              <b>(Sie)</b>
            <% } %>
          </span>
        """)(_.extend(group,
                      filteredMails: filteredMails,
                      currentUserIsMember: currentUserIsMember,
                      subject: subject,
                      icon: icon))
      else
        task_template = App.TaskTemplate.find(taskId)
        process = task_template.get("process")
        subject = "[ProcessTemplate] " + task_template.get("name")
        _.template("""
          <span title="<%= user_names.join(", ") %>" >
            <a href="mailto:<%= filteredMails.join("; ") %>?subject=<%= subject %>">
            <i class="fas fa-users"></i>
            <%- name %>
            </a>
            <% if (currentUserIsMember) { %>
              <b>(Sie)</b>
            <% } %>
          </span>
        """)(_.extend(group,
                      filteredMails: filteredMails,
                      currentUserIsMember: currentUserIsMember,
                      subject: subject))
    else
      "<i>(niemand)</i>"

  deleteConfirmationButton: (className) ->
    ###
    Return the HTML for a button for confirming the deletion of an
    object. The passed CSS class is attached to the button. Useful as
    the content of a popover that is shown when a delete button is
    clicked.
    ###
    """
    <button class='btn btn-default #{className}'
            data-toggle='tooltip'
            title='Dies kann nicht rückgängig gemacht werden.'>
      <i class='far fa-trash-alt'></i>
      Löschen bestätigen
    </button>
    """

  stateLabelClasses: ->
    ###
    Return the state label's CSS classes.
    ###
    switch @state
      when 'template' then 'label label-default'
      when 'draft' then 'label label-default'
      when 'active' then 'label label-primary'
      when 'failed' then 'label label-danger'
      when 'completed' then 'label label-success'
      when 'paused' then 'label label-warning'

  prettyState: ->
    ###
    Return a human-readable, German version of the run's state.
    ###
    switch @state
      when 'template' then 'Vorlage'
      when 'draft' then 'Inaktiv'
      when 'active' then 'Aktiv'
      when 'failed' then 'Abgebrochen'
      when 'completed' then 'Abgeschlossen'
      when 'paused' then 'Pausiert'
