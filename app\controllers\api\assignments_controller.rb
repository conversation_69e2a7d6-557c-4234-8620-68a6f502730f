# Provides a JSON-based API for creating and manipulating assignments.
class Api::AssignmentsController < ApplicationController
  # Create a new assignment or copy an existing one. Copying an
  # assignment which is a template yields another template.
  #
  # copy_of - The assignment to copy from (optional). If omitted, a
  #           new assignment with an empty run is created.
  #
  # Returns the new assignment as JSON, including all of its contents.
  def create
    if params[:copy_of]
      copy_of = Assignment.find(params[:copy_of])
      name = "#{copy_of.name} (<PERSON><PERSON>)"
      if params[:template]
        project = "Vorlage"
      else
        project = copy_of.project
      end
      datapath = copy_of.datapath
      description = copy_of.description
      due = params[:due] || ""
      template = params[:template] || copy_of.state == "template"
      priority = [copy_of.priority, 3].min
      board_count = 0
    else
      name = params[:assignment][:name]
      project = params[:assignment][:project]
      copy_of = nil
      template = params[:template]
      priority = 3
      datapath = params[:assignment][:datapath]
      board_count = 0
    end

    action = CreateAssignmentAction.new(name,
                                        project,
                                        current_user,
                                        datapath,
                                        description,
                                        due,
                                        priority,
                                        copy_of: copy_of,
                                        template: template)

    execute_create_action_and_respond(action)
  end

  # Manipulate an assignment.
  #
  # name        - The new name for the assignment (optional).
  # project     - The new project name for the assignment (optional).
  # datapath    - The new datapath for the assignment files (optional).
  # description - The new description for the assignment (optional).
  # due         - The new due date for the assignment, in ISO-8601
  #               format (optional).
  # planners    - The user IDs of the planners that the assigment
  #               should have (optional). Non-planners in the array
  #               are added, and current planners not in the array
  #               are removed.
  #
  # Returns the updated assignment as JSON.
  # Raises "403 Forbidden" if the operation is not allowed
  #   for the requesting user.
  def update
    assignment = Assignment.find(params[:id])
    action = new_update_action(assignment, params)
    execute_action_and_respond(action)
  end

  # Delete an assignment.
  #
  # Returns nothing.
  # Raises "403 Forbidden" if the operation is not allowed
  #   for the requesting user.
  def destroy
    assignment = Assignment.find(params[:id])
    if assignment.deletable_by_user?(current_user)
      assignment.destroy!
      render json: {}
    else
      render status: :forbidden, nothing: true
    end
  end

  private

  def permitted_create_params
    params.delete(:planners) # ignore, client always sends an empty array
    params.require(:assignment).permit(:name, :datapath, :copy_of)
  end

  def new_update_action(assignment, params)
    if assignment.state != "completed" && assignment.state != "failed"
      if params[:add_planner]
        new_planner = User.find(params[:add_planner])
        AddPlannerAction.new(assignment, new_planner)
      elsif params[:remove_planner]
        planner = User.find(params[:remove_planner])
        RemovePlannerAction.new(assignment, planner)
      elsif params[:add_observer]
        new_observer = User.find(params[:add_observer])
        AddObserverAction.new(assignment, new_observer)
      elsif params[:remove_observer]
        observer = User.find(params[:remove_observer])
        RemoveObserverAction.new(assignment, observer)
      elsif params[:add_extern_workgroup]
        extern_workgroup = Workgroup.find(params[:add_extern_workgroup])
        AddExternWorkgroupAction.new(assignment, extern_workgroup)
      elsif params[:remove_extern_workgroup]
        extern_workgroup = Workgroup.find(params[:remove_extern_workgroup])
        RemoveExternWorkgroupAction.new(assignment, extern_workgroup)
      elsif params[:state] == "failed"
        CancelAssignmentAction.new(assignment)
      elsif (params[:priority] == 5) || (assignment.priority == 5)
        if assignment.highest_priority_editable_by_user?(current_user)
          attr = updateable_attributes.find { |a| params.key?(a) }
          UpdateAttributeAction.new(assignment, attr, params[attr])
        end
      else
        attr = updateable_attributes.find { |a| params.key?(a) }
        UpdateAttributeAction.new(assignment, attr, params[attr])
      end
    end
  end

  def updateable_attributes
    [:name, :project, :datapath, :description, :board_count, :due, :priority]
  end
end
