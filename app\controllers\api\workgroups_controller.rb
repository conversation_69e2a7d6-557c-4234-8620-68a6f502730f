# Provides a JSON API for searching for workgroups.
class Api::WorkgroupsController < ApplicationController
  # Search for workgroups by  name.
  #
  # search - The search term.
  #
  # Returns all matching workgroups as JSON.
  def index
    if params[:search].present?
      result = search_by("name").uniq
    else
      # Don't return all users at once to avoid too much traffic
      result = []
    end
    render json: result
  end

  private

  def search_by(attribute)
    Workgroup.where(extern: true)
             .where("lower(#{attribute}) like lower(?)", "#{params[:search]}%")
  end
end
