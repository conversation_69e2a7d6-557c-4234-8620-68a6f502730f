#=require "collections/Collection"
#=require "models/Assignment"

App.AssignmentCollection = App.Collection.extend
  ###
  The collection class for Assignment.
  ###

  model: App.Assignment
  url: '/api/assignments'

  comparator: (assignment) ->
    ###
    Override of Backbone.Collection#comparator which makes sure that
    assignments are sorted by project first, name second.
    ###
    "#{assignment.get('project')} #{assignment.get('name')}"

  filterByState: (state) ->
    ###
    Return a collection containing only the assignments in the passed
    state ('draft', 'active', 'completed', 'failed', 'paused').
    ###
    filtered = @filter (a) -> a.get('state') == state
    new App.AssignmentCollection(filtered)
