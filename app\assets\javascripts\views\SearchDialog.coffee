App.SearchDialog = Marionette.CompositeView.extend
  ###
  Superclass for a search dialog with a text field and a list of
  result items (of a type derived from SearchDialogItem). An example
  is the dialog for selecting the user to assign to a task
  (UserSelectorDialog).

  SearchDialog is expected to be passed a collection. It searches for
  items by calling `@collection.fetch(data: {search: "..."})`.
  ###

  #
  # Constants
  #

  SEARCH_DELAY: 300

  #
  # Backbone.View options
  #

  className: 'modal pms-search-dialog'

  events:
    'input .pms-search-dialog-input': '_onSearchInput'
    'keydown .pms-search-dialog-input': '_onSearchFieldKeyDown'
    'submit .pms-search-dialog-form': '_onSearchFormSubmit'

  #
  # Marionette.View options
  #

  ui:
    searchInput: '.pms-search-dialog-input'
    indicator: '.pms-search-dialog-indicator'

  #
  # Marionette.ItemView options
  #

  template: JST['SearchDialog']

  #
  # Marionette.CollectionView options
  #

  childViewContainer: '.pms-search-dialog-results'
  childViewEventPrefix: 'item'


  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @_fetchResultsDebounced = _.debounce(@_fetchResults.bind(@), @SEARCH_DELAY)
    @on('item:up', @_onItemUp.bind(this))
    @on('item:down', @_onItemDown.bind(this))

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    title: @title
    searchLabel: @searchLabel

  show: (callback) ->
    ###
    Display the search dialog. When the user has selected an item,
    `callback` is called with the corresponding model as argument. If
    the dialog is dismissed, `callback` is not called.
    ###
    @render()
    @$el.modal('show')
    @ui.searchInput.focus()
    @once 'item:select', (item) =>
      @$el.modal('hide')
      callback(item.model)

  defaultResultItems: (searchTerm) ->
    ###
    Return the default set of result items that should be shown
    immediately for the passed search term. For instance, a default
    item in the NewProcessDialog is to create a new, empty process
    with the typed-in name. Returns an empty array by default.
    ###
    []

  #
  # Event Handlers
  #

  _onSearchInput: (event) ->
    searchTerm = @ui.searchInput.val()
    @collection.reset(@defaultResultItems?(searchTerm) or [])
    @children.first()?.setHighlighted(true)
    @_fetchResultsDebounced searchTerm, =>
      @children.each (item) -> item.setHighlighted(false)
      @children.first()?.setHighlighted(true)

  _fetchResults: (searchTerm, callback) ->
    @_indicateLoading()
    deferred = @collection.fetch(data: {search: searchTerm}, remove: false)
    deferred.always(@_indicateIdle.bind(this))
    deferred.done(callback)

  _indicateLoading: ->
    @ui.indicator.addClass('loading')

  _indicateIdle: ->
    @ui.indicator.removeClass('loading')

  _onSearchFormSubmit: (event) ->
    # Pressing Enter in the search field is interpreted as selecting
    # the first search result.
    event.preventDefault()
    firstItem = @children.first()
    @trigger('item:select', firstItem) if firstItem

  _onSearchFieldKeyDown: (event) ->
    # If the down key is pressed within the search field, select the
    # first item in the search results (if any).
    if event.keyCode == 0x28 # down key
      event.preventDefault()
      @children.first()?.focus()

  _onItemUp: (item) ->
    # Select the search result item above or, if there is no such
    # item, return focus to the search field.
    index = @children.toArray().indexOf(item)
    if index == 0
      @ui.searchInput.focus()
      @ui.searchInput.select()
    else
      @children.findByIndex(index - 1).focus()

  _onItemDown: (item) ->
    # Select the item below (if existent).
    index = @children.toArray().indexOf(item)
    @children.findByIndex(index + 1)?.focus()
