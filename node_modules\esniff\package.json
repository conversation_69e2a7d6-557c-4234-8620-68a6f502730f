{"_args": [["esniff@^2.0.1", "/pms/node_modules/es5-ext"]], "_from": "esniff@>=2.0.1 <3.0.0", "_hasShrinkwrap": false, "_id": "esniff@2.0.1", "_inCache": true, "_installable": true, "_location": "/esniff", "_nodeVersion": "16.20.2", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esniff_2.0.1_1708529348948_0.18900879735642562"}, "_npmUser": {"email": "<EMAIL>", "name": "medikoo"}, "_npmVersion": "8.19.4", "_phantomChildren": {}, "_requested": {"name": "esniff", "raw": "esniff@^2.0.1", "rawSpec": "^2.0.1", "scope": null, "spec": ">=2.0.1 <3.0.0", "type": "range"}, "_requiredBy": ["/es5-ext"], "_resolved": "https://registry.npmjs.org/esniff/-/esniff-2.0.1.tgz", "_shasum": "a4d4b43a5c71c7ec51c51098c1d8a29081f9b308", "_shrinkwrap": null, "_spec": "esniff@^2.0.1", "_where": "/pms/node_modules/es5-ext", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "http://www.medikoo.com/"}, "bugs": {"url": "https://github.com/medikoo/esniff/issues"}, "dependencies": {"d": "^1.0.1", "es5-ext": "^0.10.62", "event-emitter": "^0.3.5", "type": "^2.7.2"}, "description": "Low footprint ECMAScript source code parser", "devDependencies": {"eslint": "^8.56.0", "eslint-config-medikoo": "^4.2.0", "esprima": "^4.0.1", "github-release-from-cc-changelog": "^2.3.0", "nyc": "^15.1.0", "prettier-elastic": "^2.7.1", "tad": "^3.1.1"}, "directories": {}, "dist": {"fileCount": 22, "integrity": "sha512-kTUIGKQ/mDPFoJ0oVfcmyJn4iBDRptjNVIzwIFR7tqWXdVI9xfA2RMwY/gbSpJG3lkdWNEjLap/NqVHZiJsdfg==", "shasum": "a4d4b43a5c71c7ec51c51098c1d8a29081f9b308", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAlOZGZto7GuH6JuDiIfZ/kSflD36GSVVVsfMtEqT41fAiASN3gW8J3xxK8Kdc14i1sf/36E3THrGHvWG1lypmtf2A=="}], "tarball": "https://registry.npmjs.org/esniff/-/esniff-2.0.1.tgz", "unpackedSize": 60911}, "engines": {"node": ">=0.10"}, "eslintConfig": {"extends": "medikoo/es5", "overrides": [{"files": ["index.js"], "rules": {"max-depth": "off"}}, {"files": ["index.js", "utils/is-variable-name.js"], "rules": {"max-lines": "off"}}, {"files": ["utils/is-variable-name.js"], "rules": {"no-misleading-character-class": "off"}}, {"files": ["test/**"], "env": {"node": true}}], "root": true}, "eslintIgnore": ["/coverage", "/test/__playground"], "gitHead": "8498674e0894acc27e606d6f55e732e628107596", "homepage": "https://github.com/medikoo/esniff#readme", "keywords": ["analyze", "ast", "code", "detect", "detective", "find", "parse", "search", "sniff", "sniffer", "source", "syntax"], "license": "ISC", "maintainers": [{"name": "medikoo", "email": "<EMAIL>"}], "name": "esniff", "nyc": {"all": true, "exclude": ["*.config.js", ".github", "coverage/**", "test/**"], "reporter": ["html", "lcov", "text-summary"]}, "optionalDependencies": {}, "prettier": {"overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}], "printWidth": 100, "tabWidth": 4}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/medikoo/esniff.git"}, "scripts": {"coverage": "nyc npm test", "lint": "eslint .", "lint:updated": "pipe-git-updated --base=main --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --base=main --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated ---base=main -ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "node ./node_modules/tad/bin/tad"}, "version": "2.0.1"}