# A task attachment ("Aufgabenanhang") is a file that has been
# uploaded for a specific task. Task attachments are useful for
# operators to document the work they have done - for instance by
# making photos - and for planners to add more detailed information
# about how the task should be executed (e.g. through design data
# files.)
class TaskAttachment < ActiveRecord::Base
  belongs_to :task, touch: true
  belongs_to :created_by, class_name: "User"
  validates :filename, presence: true
  before_save :ensure_filename_is_conflict_free

  # Find the assignment which contains the attachment's task.
  #
  # Returns the assignment.
  def assignment
    task.try(:assignment)
  end

  # Get the (MIME) content type of the attachment file.
  #
  # Returns the content type, or the generic
  # "application/octet-stream" if the type is not known.
  def content_type
    self[:content_type] || "application/octet-stream"
  end

  # Look at the attachment's content type and determine whether it is
  # an image file format.
  #
  # Returns true if the attachment is an image, false if not.
  def image?
    content_type =~ /^image\//
  end

  # Get a file system path specifying the attachment file's relative
  # to the attachments root directory (defined in the configuration as
  # config.pms_attachments_root).
  #
  # Returns the attachment file path as a Pathname.
  def file_path
    path = Pathname.new(Rails.configuration.pms_attachments_root)
    run = task.process.run
    path += "Auftrag #{run.assignment.id}"
    path += "Durchlauf #{run.version}"
    path += filename
    path
  end

  # Write the contents of an IO stream into the file represented by
  # the task attachment.
  #
  # source - The stream to read from.
  #
  # Returns nothing.
  # Raises IOError if reading or writing fails.
  def store(source)
    # Avoid corrupted attachment files by first copying `source` into
    # a temporary file, which is afterwards moved to #file_path. Using
    # this technique, users will always see either the old or the new
    # version of the file, but never a half-overwritten one.
    write_to_temporary_file(source)
    rename_temporary_file
  end

  # Check if the user has created the task attachment
  def attachment_deletable_by_user?(user)
    created_by == user
  end

  # Get the upload user of the attachment file
  def created_by_id
    self[:created_by_id] || ""
  end

  private

  def write_to_temporary_file(source)
    path = temporary_file_path
    FileUtils.mkdir_p(path.dirname)
    File.open(path, "wb+") do |dest|
      FileUtils.copy_stream(source, dest)
    end
  end

  def temporary_file_path
    Pathname.new(file_path.to_s + "~")
  end

  def rename_temporary_file
    File.rename(temporary_file_path, file_path)
  end

  def ensure_filename_is_conflict_free
    return unless new_record? && File.exist?(file_path)
    index = 1
    index += 1 while File.exist?(file_path_with_index(index))
    self.filename = filename_with_index(index)
  end

  def file_path_with_index(index)
    file_path.dirname + filename_with_index(index)
  end

  def filename_with_index(index)
    filename.sub(".", "(#{index}).")
  end
end
