# Helper methods used by NotificationMailer.
module <PERSON><PERSON><PERSON><PERSON><PERSON>
  # Assemble the "full" name of an assignment, which consists of the
  # assignment's name,the assignment id and its associated project, if given.
  #
  # assignment - The assignment to return the full name for.
  #
  # Returns the assignment's project name and name in a standard form.
  def assignment_full_name(assignment)
    if assignment.project.blank?
      "[#{assignment.id}] #{assignment.name}"
    else
      "[#{assignment.id}][#{assignment.project}] #{assignment.name}"
    end
  end
end
