# Executed when a user unassigns a article to a task
class UnassignArticleAction < Action
  # Initialize an UnassignArticleAction.
  #
  # task     - The task to assign.
  # article  - The article to unassign to.
  def initialize(task, article_task)
    super(task)
    @article_task = article_task
  end

  # See Action.
  def allowed?(user)
    # TODO prevent double article
    subject.reassignable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.article_tasks.delete(@article_task)
  end
  
end
