#=require "collections/TaskAttachmentCollection"
#=require "models/Model"
#=require "models/Operator"
#=require "models/TaskAttachment"
#=require "models/User"
#=require "models/Machine"
#=require "models/MachineTask"
#=require "models/ArticleTask"

App.Task = App.Model.extend
  ###
  Client-side representation of a Task.
  (See app/models/task.rb)
  ###

  #
  # Backbone.Model options
  #

  defaults:
    name: 'Neue Aufgabe'

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasOne,
    key: 'completed_by',
    keySource: 'completed_by_id',
    includeInJSON: 'id',
    relatedModel: App.User
  ,
    type: Backbone.HasOne,
    key: 'operator',
    keySource: 'operator',
    includeInJSON: 'id',
    relatedModel: App.Operator
  ,
    type: Backbone.HasMany
    key: 'article_tasks'
    keySource: 'article_task_ids'
    relatedModel: App.ArticleTask
    collectionType: App.ArticleTaskCollection
    reverseRelation:
      key: 'task',
      keySource: 'task_id',
      includeInJSON: 'id'
  ,
    type: Backbone.HasMany
    key: 'machines'
    keySource: 'machine_ids'
    relatedModel: App.Machine
    collectionType: App.MachineCollection
  ,
    type: Backbone.HasMany
    key: 'attachments'
    keySource: 'attachment_ids'
    relatedModel: App.TaskAttachment
    collectionType: App.TaskAttachmentCollection
    reverseRelation:
      key: 'task',
      keySource: 'task_id',
      includeInJSON: 'id'
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'task'
  jsonPluralRoot: 'tasks'

  predecessors: ->
    if @get('position') > 1
      [@collection.findWhere(position: @get('position') - 1)]
    else
      @get('process').get('parents')
        .filter((process) -> !process.get('tasks').isEmpty())
        .map((process) -> process.get('tasks').last())

  successors: ->
    next = @collection.findWhere(position: @get('position') + 1)
    if next
      [next]
    else
      @get('process').get('children')
        .filter((process) -> !process.get('tasks').isEmpty())
        .map((process) -> process.get('tasks').first())

  isCompletionUndoable: ->
    if !@get('completed') or @successors().some((s) -> s.get('completed'))
      return false
    @_differenceToUndoCompletionDeadline() > 0

  updateCompletionUndoable: ->
    if @isCompletionUndoable()
      @set('completion_undoable', true)
      diff = @_differenceToUndoCompletionDeadline()
      setTimeout(=>
        @set('completion_undoable', false)
      , diff)
    else
      @set('completion_undoable', false)

  _differenceToUndoCompletionDeadline: ->
    completionTime = moment(@get('completed_at'))
    undoDeadline = completionTime.add(App.Task.undoCompleteTimeout, 'seconds')
    now = moment()
    undoDeadline.diff(now)
