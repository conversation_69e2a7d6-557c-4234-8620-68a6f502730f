#=require "views/SearchDialogItem"

App.NewProcessItem = App.SearchDialogItem.extend
  ###
  An search result item in NewProcessDialog.
  ###

  #
  # Marionette.ItemView options
  #

  template: JST['NewProcessItem']

  #
  # Methods
  #

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    if App.useProcessTemplates
      tasks = @model.get('task_templates')
    else
      tasks = @model.get('tasks')
    _.extend(@model.toJSON(), taskNames: tasks.pluck('name'))
