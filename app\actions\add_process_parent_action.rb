# Executed when a user connects a process with another to create a
# dependency between them.
class AddProcessParentAction < Action
  # Initialize the action.
  #
  # process     - The process to add the parent to.
  # new_parent  - The parent to create a dependency to.
  def initialize(process, new_parent)
    super(process)
    @new_parent = new_parent
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    return if dependency_exist? || dependency_circular?
    remove_dependencies_which_become_redundant
    ProcessDependency.create!(parent: @new_parent, child: subject)
    mark_changed(@new_parent)
  end

  private

  def dependency_exist?
    subject.depends?(@new_parent)
  end

  def dependency_circular?
    @new_parent.depends?(subject)
  end

  def remove_dependencies_which_become_redundant
    # Consider the following process graph:
    #
    #   A
    #   |
    #   +--+
    #   |  |
    #   |  C
    #   |  |
    #   |  D
    #   B
    #
    # If we make B a child of D, the direct dependency from B to A
    # becomes redundant as it is already expressed through the path
    # to A over D and C:
    #
    #   A
    #   |
    #   +--+
    #   |  |
    #   C  |
    #   |  | <-- unnecessary!
    #   D  |
    #   |  |
    #   +--+
    #   |
    #   B
    #
    # More generally, such redundant depdendencies appear whenever a
    # parent of the action's subject (B in the example) is also a
    # (direct or indirect) ancestor of the new parent. We make sure to
    # delete them to keep the database clean.
    ancestor_ids_of_new_parent = @new_parent.ancestor_ids
    subject.parent_dependencies.each do |dep|
      next unless ancestor_ids_of_new_parent.include?(dep.parent_id)
      dep.destroy!
      mark_changed(dep.parent)
    end

    # A similar issue to the one described above appears when a
    # *child* of the *new parent* is a *descendant* of the *subject*
    # (notice the symmetry):
    #
    #                     B
    #                     |
    #   A   B           +---+
    #   |   |           |   |
    #   C   |  A -> B   A   |
    #   |   |  ----->   |   | <-- redundant!
    #   +---+           C   |
    #     |             |   |
    #     D             +---+
    #                     |
    #                     D
    #
    # We also remove these redundancies, of course.
    descendant_ids_of_subject = subject.descendant_ids
    @new_parent.child_dependencies.each do |dep|
      next unless descendant_ids_of_subject.include?(dep.child_id)
      dep.destroy!
      mark_changed(dep.child)
    end
  end
end
