# encoding: utf-8

# Mailer for all notification e-mails sent by the PMS system.
class NotificationMailer < ActionMailer::Base
  include MailerHelper
  helper MailerHelper
  default from: "<EMAIL>"

  def assignment_completed(completed_assignment, last_task, completion_time)
    return if last_task.reload.completed_at == completion_time
    @greeting = "Hallo PMS-Planer:in"
    @assignment = completed_assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_finished: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag abgeschlossen"
  end

  def assignment_failed(failed_assignment)
    @greeting = "Hallo PMS-Planer:in"
    @assignment = failed_assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_failed: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag abgebrochen!"
  end

  def run_activated(run)
    @greeting = "Hallo PMS-Planer:in"
    @run = run
    @assignment = run.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_new_run: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Durchlauf #{@run.version} aktiviert"
  end

  def run_reactivated(run)
    @greeting = "Hallo PMS-Planer:in"
    @run = run
    @assignment = run.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_reactivated: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag reaktiviert"
  end

  #TODO
  def assignment_behind_schedule(assignment)
    @greeting = "Hallo PMS-Planer:in"
    @assignment = assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_delayed: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag hinter Zeitplan!"
  end

  def assignment_no_work(assignment)
    @greeting = "Hallo PMS-Planer:in"
    @assignment = assignment
    @completable_processes = @assignment.runs.last.completable_processes
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_no_work: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag unbearbeitet!"
  end

  def info_added_planner(assignment, user)
    @greeting = "Hallo PMS-Planer:in"
    @assignment = assignment
    @user = user
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_new_planner: true).where.not(id: @user.id).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Ihrem Auftrag wurde " \
                  "#{@user.name} als planende Person hinzugefügt"
  end

  def info_added_observer(assignment, user)
    @greeting = "Hallo PMS-Planer:in"
    @assignment = assignment
    @user = user
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_new_observer: true).where.not(id: @user.id).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Ihrem Auftrag wurde " \
                  "#{@user.name} als beobachtende Person hinzugefügt"
  end

  def added_planner(assignment, user)
    @assignment = assignment
    @user = user
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless user.email_added_as_planner
    mail to: user.email,
         subject: "#{assignment_full_name(@assignment)}: Sie wurden " \
                "als planende Person hinzugefügt"
  end

  def process_completed(process, completion_time)
    return if process.reload.tasks.last.completed_at == completion_time
    @greeting = "Hallo PMS-Planer:in"
    @process = process
    @assignment = process.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @completable_children = @process.children.select(&:completable?)
    @unassigned_tasks = @completable_children.flat_map do |p|
      p.tasks.select { |t| !t.operator }
    end
    email_addresses = @assignment.planners.active.where(email_assignment_process_finished: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Prozess '#{@process.name}' abgeschlossen"
  end

  def process_ready(process)
    @greeting = "Hallo PMS-Planer:in"
    @process = process
    @assignment = process.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @completable_children = @process.children.select(&:completable?)
    @unassigned_tasks = @completable_children.flat_map do |p|
      p.tasks.select { |t| !t.operator }
    end
    email_addresses = @assignment.planners.active.where(email_assignment_process_ready: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Prozess '#{@process.name}' bearbeitbar"
  end

  def task_completed(completed_task,completion_time)
    completed_task.reload
    # return if completed_task.completed_at == completion_time
    @greeting = "Hallo PMS-Planer:in"
    @task = completed_task
    @assignment = completed_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_task_finished: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' abgeschlossen"
  end

  def first_task_ready_planner(task)
    @greeting = "Hallo PMS-Planer:in"
    @task = task
    @assignment = task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_task_ready: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Erste Aufgabe '#{@task.name}' bearbeitbar"
  end

  def next_task_ready_planner(completed_task, next_task, completion_time)
    completed_task.reload
    next_task.reload

    return if completed_task.completed_at == completion_time
    @greeting = "Hallo PMS-Planer:in"
    @completed = completed_task
    @next = next_task
    @assignment = completed_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_task_ready: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@next.name}' bearbeitbar"
  end

  def task_ready_planner(assigned_task)
    @greeting = "Hallo PMS-Planer:in"
    @task = assigned_task
    @assignment = @task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_task_ready: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' bearbeitbar"
  end

  def no_operator_assigned(next_task)
    @greeting = "Hallo PMS-Planer:in"
    @next = next_task
    @assignment = next_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_no_operator: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Keine bearbeitende Person für '#{@next.name}'"
  end

  def task_attachment_added(task)
    @greeting = "Hallo PMS-Planer:in"
    @task = task
    @assignment = task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.planners.active.where(email_assignment_task_ready: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Der Aufgabe '#{@task.name}' wurde eine Datei hinzugefügt"
  end

  def assignment_day_summary(user)
    @greeting = "Hallo #{user.name}"
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/"
    @assignments_work = user.assignments.active.where("assignments.updated_at > ?", Time.now - 1.day)
    @assignments_no_work = user.assignments.active.where("assignments.updated_at <= ?", Time.now - 1.day)
    return unless !user.deactivated? && user.email_assignment_day_summery && @assignments.nil?
    mail to: user.email,
         subject: "PMS: Zusammenfassung Planer:in"
  end

  #############################################################################################################
  # Observer - Options
  #############################################################################################################

  def assignment_completed_observer(completed_assignment, last_task, completion_time)
    return if last_task.reload.completed_at == completion_time
    @greeting = "Hallo PMS-Beobachter:in"
    @assignment = completed_assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_finished_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag abgeschlossen",
        template_name: 'assignment_completed'
  end

  def assignment_failed_observer(failed_assignment)
    @greeting = "Hallo PMS-Beobachter:in"
    @assignment = failed_assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_failed_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag abgebrochen!",
         template_name: 'assignment_failed'
  end

  def run_activated_observer(run)
    @greeting = "Hallo PMS-Beobachter:in"
    @run = run
    @assignment = run.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_new_run_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Durchlauf #{@run.version} aktiviert",
         template_name: 'run_activated'
  end

  def run_reactivated_observer(run)
    @greeting = "Hallo PMS-Beobachter:in"
    @run = run
    @assignment = run.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_reactivated_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag reaktiviert",
         template_name: 'run_reactivated'
  end

  #TODO
  def assignment_behind_schedule_observer(assignment)
    @greeting = "Hallo PMS-Beobachter:in"
    @assignment = assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_delayed_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag hinter Zeitplan!",
         template_name: 'assignment_behind_schedule'
  end

  #TODO
  def assignment_no_work_observer(assignment)
    @greeting = "Hallo PMS-Beobachter:in"
    @assignment = assignment
    @completable_processes = @assignment.runs.last.completable_processes
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_no_work_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Auftrag unbearbeitet!",
         template_name: 'assignment_no_work'
  end

  def info_added_observer_observer(assignment, user)
    @greeting = "Hallo PMS-Beobachter:in"
    @assignment = assignment
    @user = user
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_new_planner_observer: true).where.not(id: @user.id).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: Ihrem Auftrag wurde " \
                  "#{@user.name} als beobachtende Person hinzugefügt",
        template_name: 'info_added_observer'
  end

  def added_observer(assignment, user)
    @assignment = assignment
    @user = user
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless user.email_added_as_observer
    mail to: user.email,
         subject: "#{assignment_full_name(@assignment)}: Sie wurden " \
                "als beobachtende Person hinzugefügt"
  end

  def process_completed_observer(process, completion_time)
    return if process.reload.tasks.last.completed_at == completion_time
    @greeting = "Hallo PMS-Beobachter:in"
    @process = process
    @assignment = process.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @completable_children = @process.children.select(&:completable?)
    @unassigned_tasks = @completable_children.flat_map do |p|
      p.tasks.select { |t| !t.operator }
    end
    email_addresses = @assignment.observers.active.where(email_assignment_process_finished_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Prozess '#{@process.name}' abgeschlossen",
          template_name: 'process_completed'
  end

  def process_ready_observer(process)
    @greeting = "Hallo PMS-Beobachter:in"
    @process = process
    @assignment = process.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @completable_children = @process.children.select(&:completable?)
    @unassigned_tasks = @completable_children.flat_map do |p|
      p.tasks.select { |t| !t.operator }
    end
    email_addresses = @assignment.observers.active.where(email_assignment_process_ready_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Prozess '#{@process.name}' bearbeitbar",
          template_name: 'process_ready'
  end

  def task_completed_observer(completed_task,completion_time)
    completed_task.reload
    @greeting = "Hallo PMS-Beobachter:in"
    @task = completed_task
    @assignment = completed_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_task_finished_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' abgeschlossen",
          template_name: 'task_completed'
  end

  def first_task_ready_observer(task)
    @greeting = "Hallo PMS-Beobachter:in"
    @task = task
    @assignment = task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_task_ready_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Erste Aufgabe '#{@task.name}' bearbeitbar",
          template_name: 'first_task_ready_planner'
  end

  def next_task_ready_observer(completed_task, next_task, completion_time)
    completed_task.reload
    next_task.reload

    return if completed_task.completed_at == completion_time
    @greeting = "Hallo PMS-Beobachter:in"
    @completed = completed_task
    @next = next_task
    @assignment = completed_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_task_ready_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@next.name}' bearbeitbar",
          template_name: 'next_task_ready_planner'
  end

  def task_ready_observer(assigned_task)
    @greeting = "Hallo PMS-Beobachter:in"
    @task = assigned_task
    @assignment = @task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_task_ready_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' bearbeitbar",
          template_name: 'task_ready_planner'
  end

  def no_operator_assigned_observer(next_task)
    @greeting = "Hallo PMS-Beobachter:in"
    @next = next_task
    @assignment = next_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_no_operator_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Keine bearbeitende Person für '#{@next.name}'",
          template_name: 'no_operator_assigned'
  end

  #TODO
  def task_attachment_added_observer(task)
    @greeting = "Hallo PMS-Beobachter:in"
    @task = task
    @assignment = task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    email_addresses = @assignment.observers.active.where(email_assignment_task_ready_observer: true).pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Der Aufgabe '#{@task.name}' wurde eine Datei hinzugefügt",
          template_name: 'task_attachment_added'
  end

  def assignment_day_summary_observer(user)
    @greeting = "Hallo #{user.name}"
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/"
    @assignments_work = user.assignments.active.where("assignments.updated_at > ?", Time.now - 1.day)
    @assignments_no_work = user.assignments.active.where("assignments.updated_at <= ?", Time.now - 1.day)
    return unless !user.deactivated? && user.email_assignment_day_summery_observer && @assignments.nil?
    mail to: user.email,
         subject: "PMS: Zusammenfassung Beobachter:in",
         template_name: 'assignment_day_summary'

  end

  #############################################################################################################
  # Worker - Options
  #############################################################################################################

  # return unless task.operator.class == User
  # return unless !@operator.deactivated? && @operator.email_worker_new_task
  def operator_assigned(assigned_task, assigning)
    if !assigned_task.is_a?(Array)
      @task = assigned_task
      return unless (@task.operator.deactivated? == false && @task.operator.email_worker_new_task)
      @subject = "Sie wurden einer Aufgabe '#{@task.name}' als bearbeitende Person zugewiesen"
    else
      @tasks = assigned_task
      @task = @tasks.first
      return unless (@task.operator.deactivated? == false && @task.operator.email_worker_new_task)
      if @tasks.length > 1
        @subject = "Sie wurden mehreren Aufgaben als bearbeitende Person zugewiesen"
      else
        @subject = "Sie wurden einer Aufgabe '#{@task.name}' als bearbeitende Person zugewiesen"
      end
    end
    @assignment = @task.assignment
    @assigning = assigning
    @greeting = "Hallo #{@task.operator.name}"
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    mail to: @task.operator.email,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: #{@subject}"
  end

  def operator_assigned_task_ready(assigned_task, assigning)
    @task = assigned_task
    @assigning = assigning
    @assignment = @task.assignment
    @greeting = "Hallo #{@task.operator.name}"
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless (@task.completable? == true && @task.operator.deactivated? == false && @task.operator.email_worker_new_task)
    mail to: @task.operator.email,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: Die Aufgabe " \
         "'#{@task.name}' wurde ihnen zugewiesen und ist bearbeitbar"
  end

  def first_task_ready(task)
    return unless task.operator
    @task = task
    @assignment = task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless (@task.operator.email.present? && @task.operator.deactivated? == false && @task.operator.email_worker_task_ready)
    mail to: @task.operator.email,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Erste Aufgabe '#{@task.name}' bearbeitbar"
  end

  def next_task_ready(completed_task, next_task, completion_time)
    completed_task.reload
    next_task.reload

    # return if completed_task.completed_at == completion_time # TODO
    # return unless next_task.operator

    @completed = completed_task
    @next = next_task
    @assignment = completed_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless (@next.operator.email.present? && @next.operator.deactivated? == false && 
                   @next.operator.email_worker_task_ready)
    if @next.operator.id != @completed.completed_by_id
      mail to: @next.operator.email,
           reply_to: planner_emails(@assignment),
           subject: "#{assignment_full_name(@assignment)}: " \
                    "Aufgabe '#{@next.name}' bearbeitbar"
    end
  end

  def task_ready(assigned_task)
    @task = assigned_task
    @assignment = @task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless (@task.operator.email.present? && @task.operator.deactivated? == false && @task.operator.email_worker_task_ready)
    mail to: @task.operator.email,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' bearbeitbar"
  end

  def assignment_with_task_failed(failed_assignment, assigned_task)
    @task = assigned_task
    return unless @task.operator.present?
    @greeting = "Hallo #{@task.operator.name}"
    @assignment = failed_assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    return unless (@task.operator.email.present? && @task.operator.deactivated? == false && @task.operator.email_worker_assignment_failed)
    mail to: @task.operator.email,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: Auftrag abgebrochen!"
  end

  #TODO reconstruct all
  def tasks_todo_day_summary(user)
    @greeting = "Hallo #{user.name}"
    # @tasks = Assignment.involving(user)
    #                    .order(:project, :name)
    #                    .select { |a| a.has_immediate_work_for_user?(user) }
    @tasks = user.tasks.select { |a| a.completable? } # TODO maybe combine todo operator and group

    return unless !user.deactivated? && user.email_worker_day_summery && !@tasks.nil?
    mail to: user.email,
         subject: "PMS: Zusammenfassung Bearbeiter:in"
  end

  #############################################################################################################
  # Workergroup - Options
  #############################################################################################################

  def group_assigned(task, assigning)
    if !task.is_a?(Array)
      @task = task
      @group = @task.operator
      @subject = "Aufgabe '#{@task.name}' wurde ihrer Gruppe '#{@group.name}' zugewiesen"
    else
      @tasks = task
      @task = @tasks.first
      @group = @task.operator
      if @tasks.length > 1
        @subject = "Aufgaben wurden ihrer Gruppe '#{@group.name}' zugewiesen"
      else
        @subject = "Aufgabe '#{@task.name}' wurde ihrer Gruppe '#{@group.name}' zugewiesen"
      end
    end
    @assignment = @task.assignment
    @assigning = assigning
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@task.assignment.id}"
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_new_task: true})
                   .where(email_group_new_task: true).distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: #{@subject}"
  end

  def group_assigned_task_ready(task, assigning)
    return unless task.operator && task.completable? == true
    @task = task
    @assignment = task.assignment
    @assigning = assigning
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @group = task.operator
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_task_ready: true})
                   .where(email_group_task_ready: true).distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Die Aufgabe '#{@task.name}' wurde ihrer Gruppe '#{@group.name}' zugewiesen und ist bearbeitbar"
  end

  def first_group_task_ready(task)
    return unless task.operator
    @task = task
    @assignment = task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @group = task.operator
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_task_ready: true})
                   .where(email_group_task_ready: true).distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' ihrer Gruppe '#{@group.name}' bearbeitbar"
  end

  def next_group_task_ready(completed_task, next_task, completion_time)
    completed_task.reload
    next_task.reload

    return if completed_task.completed_at == completion_time
    return unless next_task.operator

    @completed = completed_task
    @next = next_task
    @assignment = completed_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @group = next_task.operator
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_task_ready: true})
                   .where(email_group_task_ready: true)
                   .where.not(id: @completed.completed_by_id).distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@next.name}' ihrer Gruppe '#{@group.name}' bearbeitbar"
  end

  def group_task_ready(assigned_task)
    @task = assigned_task
    @assignment = @task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @group = @task.operator
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_task_ready: true})
                   .where(email_group_task_ready: true).distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@task.name}' ihrer Gruppe '#{@group.name}' bearbeitbar"
  end

  def group_task_completed(completed_task,completion_time)
    completed_task.reload
    # return if completed_task.completed_at == completion_time

    @completed = completed_task
    @assignment = @completed.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @group = @completed.operator
    @completed_by = @completed.completed_by
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_task_finished: true})
                   .where(email_group_task_finished: true)
                   .where.not(id: @completed.completed_by_id).distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@completed.name}' ihrer Gruppe '#{@group.name}' wurde abgeschlossen"
  end

  def group_task_redirected(redirected_task, old_operator, new_operator)
    redirected_task.reload
    return unless new_operator != old_operator

    @red_task = redirected_task
    @assignment = @red_task.assignment
    @assignment_url = "https://pms.izm.fraunhofer.de/auftrag/#{@assignment.id}"
    @group = old_operator
    @new_op = new_operator
    email_addresses = @group.users.active.joins(:operator_group_users)
                   .where(operator_groups_users: {email_task_gone: true})
                   .where(email_group_task_gone: true)
                   .distinct.pluck(:email)
    return unless email_addresses.present?
    mail to: email_addresses,
         reply_to: planner_emails(@assignment),
         subject: "#{assignment_full_name(@assignment)}: " \
                  "Aufgabe '#{@red_task.name}' ihrer Gruppe '#{@group.name}' wurde abgegeben"
  end

  #TODO reconstruct all
  def tasks_day_summary(user)
    @greeting = "Hallo PMS-Planer:in"
    mail to: user.email,
         subject: "PMS: Zusammenfassung Aufgaben Bearbeitergruppe"
  end

  private

  def planner_emails(assignment)
    assignment.planners.active.map(&:email)
  end
end
