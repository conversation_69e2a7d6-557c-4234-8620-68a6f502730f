#=require "./_backbone"
#=require "views/NotificationBar"
#=require "views/MachineShow"

App.addInitializer ->
  App.notificationsRegion.show(new App.NotificationBar())

App.addInitializer ->
  App.machine = new App.Machine(App.machine, parse: true)
  App.user = new App.User(App.user, parse: true)
  pageEl = $('.pms-machine-show-container').get()
  page = new App.MachineShow(el: pageEl, model: App.machine)
  page.render()
  