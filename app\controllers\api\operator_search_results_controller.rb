# Provides a JSON API for searching for task operators (users or
# operator groups).
class Api::OperatorSearchResultsController < ApplicationController
  # Search for users and operator groups by name.
  #
  # query - The search term.
  #
  # Returns matching operators as JSON (using OperatorSearchResultSerializer).
  def search
    if params[:search].present?
      # remove all '%' from search params to prevent output of all users
      params[:search].gsub! '%', ''
      if params[:search].length >= 2
        by_username = search_by("username", User.active)
        by_first_name = search_by("first_name", User.active)
        by_last_name = search_by("last_name", User.active)
        by_group_name = search_group_by("name", OperatorGroup)
        result = (by_username + by_first_name + by_last_name + by_group_name).uniq
      else
        result = []
      end
    else
      # Don't return all users at once to avoid too much traffic
      result = []
    end
    render json: result, root: :search_results
  end

  private

  def search_by(attribute, scope)
    scope
      .includes(:workgroup)
      .where("lower(#{attribute}) like lower(?)", "%#{params[:search]}%")
  end

  def search_group_by(attribute, scope)
    scope
      .includes(:users, :machine, :workgroup)
      .where("lower(#{attribute}) like lower(?)", "%#{params[:search]}%")
      .select { |group| group.machine.nil? || (group.workgroup.present? && group.users.present?) }
  end
end
