# Executed when a user disconnects a process from a parent.
class RemoveProcessParentAction < Action
  # Initialize the action.
  #
  # process     - The process to remove a parent from.
  # new_parent  - The parent to disconnect from the subject.
  def initialize(process, parent)
    super(process)
    @parent = parent
  end

  def allowed?(user)
    subject.editable_by_user?(user)
  end

  def do_execute!
    ProcessDependency
      .where(parent: @parent, child: subject)
      .delete_all
    mark_changed(@parent)
  end
end
