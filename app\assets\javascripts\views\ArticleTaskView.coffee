#=require "models/ArticleTask"
#=require "views/support/TemplateHelpers"
#=require "views/behaviors/ConditionalClassNames"
#=require "views/behaviors/Editable"
#=require "views/behaviors/NameDataAttribute"

App.ArticleTaskView = Marionette.ItemView.extend
  ###
  Displays article_tasks data inside the task view.
  ###

  #
  # Backbone.Model options
  #

  tagName: 'tr'
    
  #
  # Marionette.View options
  #

  behaviors:
    Editable:
      amount:
        type: 'material_amount'
        # guard: 'editable'uses the @editable value
        target: '.pms-task-article-amount'
        inputClass: 'form-control pms-task-article-amount-input'
    NameDataAttribute: {}

  templateHelpers:
    valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
    prettyMaterialState: App.TemplateHelpers.prettyMaterialState
  
  #
  # Marionette.ItemView options
  #

  template: JST['ArticleTaskView']

  #
  # Methods
  #

  initialize: (options) ->
    @editable = options.editable

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
     
    _.extend @model.toJSON(),
      id: @model.get('id')
      state: @model.get('state')
      amount:  @model.get('amount')
      unit_name:  @model.get('unit_name')
      article_id:  @model.get('article_id')
      article_name:  @model.get('article_name')
      editable: @editable


