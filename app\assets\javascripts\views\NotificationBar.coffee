App.NotificationBar = Marionette.ItemView.extend
  ###
  A bar at the bottom of the screen which pops up whenever "error" is
  triggered on App.vent. It allows other views to show error messages
  to the user like this:

  App.vent.trigger("error",
    title: "Oops",
    description: "Something went wrong.")
  ###

  #
  # Constants
  #

  classNames:
    info: 'alert alert-info pms-notification-bar'
    warning: 'alert alert-warning pms-notification-bar'
    error: 'alert alert-danger pms-notification-bar'

  #
  # Backbone.View options
  #

  className: 'pms-notification-bar'

  events:
    'click button': '_hide'

  #
  # Marionette.ItemView options
  #

  template: JST['NotificationBar']

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @listenTo(App.vent, 'info', @_show.bind(this, 'info'))
    @listenTo(App.vent, 'warning', @_show.bind(this, 'warning'))
    @listenTo(App.vent, 'error', @_show.bind(this, 'error'))
    @listenTo(App.vent, 'hide', @_hide)

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.defaults(@options, title: '', description: '', spinner: false)

  _show: (type, options) ->
    @options = options
    @render()
    @$el.attr('class', @classNames[type])
    @$el.fadeIn(300)

  _hide: ->
    @$el.fadeOut(300)
