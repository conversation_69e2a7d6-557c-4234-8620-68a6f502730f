#=require "models/Machine"
#=require "views/behaviors/Editable"
#=require "views/support/TemplateHelpers"
#=require "views/OperatorSearchDialog"

App.MachineView = Marionette.ItemView.extend
  ###
  Displays machine data inside the task view.
  ###

  #
  # Backbone.Model options
  #

  tagName: 'tr'
    
  #
  # Marionette.View options
  #

  templateHelpers:
  #   prettyUser: App.TemplateHelpers.prettyUser
  #   prettyUserMachine: App.TemplateHelpers.prettyUserMachine
    prettyMachineState: App.TemplateHelpers.prettyMachineState
  
  #
  # Marionette.ItemView options
  #

  template: JST['MachineView']

  #
  # Methods
  #

  initialize: (options) ->
    @editable = options.editable

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###

    _.extend @model.toJSON(),
      id: @model.get('id')
      name:  @model.get('name')
      state:  @model.get('state')
      building_name:  @model.get('building_name')
      room_name:  @model.get('room_name')
      editable: @editable