<% if (uploading || deleting) { %>
<div class="pms-attachment-link">
<% } else { %>
<a class="pms-attachment-link" target="_blank" href="<%= url %>">
<% } %>

  <aside>
    <% if (uploading) { %>
      <i class="fas fa-spinner fa-pulse"></i>
    <% } else if (deleting) { %>
      <i class="far fa-trash-alt"></i>
    <% } else if (content_type.match(/^image/)) { %>
      <img src="<%= url %>/thumbnail">
    <% } else { %>
      <i class="fas fa-file"></i>
    <% } %>
  </aside>

  <% if (deleting) { %>
    <span class="text-muted pms-attachment-filename">
      Löschen ...
    </span>
  <% } else if (uploading) { %>
    <span class="text-muted pms-attachment-filename">
      <%- filename %>
    </span>
  <% } else { %>
    <span class="pms-attachment-filename" href="<%= url %>" target="_blank">
      <%- filename %>
    </span>
  <% } %>

<% if (uploading || deleting) { %>
</div>
<% } else { %>
</a>
<% } %>

<button class="btn btn-sm btn-default pms-attachment-download-btn" title="Anhang herunterladen" onclick="location.href='<%= url %>/download'">
  <i class="fas fa-download"></i>
</button>

<div class="pms-attachment-actions-container">
  <div class="btn btn-sm pms-attachment-cb-container">
    <input type="checkbox" class="pms-attachment-sticky-cb" title="Dieser Anhang wird bei Kopien <%= sticky ? 'übernommen' : 'nicht übernommen' %>" <% if (sticky) { %>checked<% } %>>
    <i class="fas fa-thumbtack checkmark"></i>
  </div>

  <% if ((App.user.id == created_by_id || taskAnnotatable) && !uploading && !deleting) { %>
    <button class="btn btn-sm btn-default pms-attachment-delete-btn"
            title="Anhang löschen">
      <i class="far fa-trash-alt"></i>
    </button>
  <% } %>
</div>
