// Styles for the SearchDialog Backbone view, used on the
// assignment page.

.pms-search-dialog-indicator {
  // Hide the loading icon while idle.
  color: transparent;
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.pms-search-dialog-indicator.loading {
  color: inherit;
  -webkit-animation: rotate 1s linear 0s infinite;
  animation: rotate 1s linear 0s infinite;
}

.pms-search-dialog-results {
  margin-left: 0;
  padding-left: 0;
  padding-top: $padding-base-vertical;

  li {
    list-style-type: none;

    &.pms-highlighted {
      border: 1px solid $btn-default-border;
      border-radius: $border-radius-large;
    }

    a {
      display: block;
      padding: 10px;
      margin: -1px;
      border-radius: $border-radius-large;

      &:hover, &:focus {
        border: none;
        background-color: $btn-default-border;
        text-decoration: none;
      }

      .pms-title {
        font-size: 125%;
      }

      .pms-subtitle {
        color: $text-color;
      }

      .pms-badge-extern {
        background-color: #d9534f;
      }
    }
  }
}