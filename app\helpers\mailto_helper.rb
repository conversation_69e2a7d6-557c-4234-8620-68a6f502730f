# Helps with the generation of 'mailto:' URLs.
module <PERSON><PERSON><PERSON><PERSON><PERSON>
  # Create a 'mailto:' URL to multiple recipients. The resulting
  # URL is technically non-standard, but works fine on Outlook,
  # which is all we need within Fraunhofer IZM.
  #
  # recipients - A list of users which should be recipients.
  #
  # Returns the generated URL as string.
  def mailto(recipients)
    "mailto:" + recipients.map(&:email).join(";%20")
  end
end
