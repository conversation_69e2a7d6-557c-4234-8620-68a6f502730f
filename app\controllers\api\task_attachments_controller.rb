require 'zip'

# Provides a REST API for accessing, uploading and deleting task
# attachments.
class Api::TaskAttachmentsController < ApplicationController
  # Upload an attachment.
  #
  # file    - The file (ActionDispatch::Http::UploadedFile) to
  #           upload. Includes the filename to use.
  # task_id - ID of the task to attach the file to.
  #
  # Returns the new task attachment's metadata as JSON.
  #   The filename might have been altered to avoid name conflicts.
  # Raises IOError if writing the attachment file fails.

  helper_method :clear_filename

  def upload
    task = Task.find(params[:task_id])
    upload = params[:file]

    #if task.annotatable_by_user?(current_user)
      attachment = create_attachment(task, upload, current_user)
      begin
        attachment.store(upload)
        ThumbnailService.thumbnail(attachment.file_path) if attachment.image?
        render status: :created, json: attachment
      rescue IOError => e
        attachment.destroy!
        raise e
      rescue SystemCallError => e
        attachment.destroy!
        raise e
      rescue Errno::ENOENT => e
        attachment.destroy!
        raise e
      rescue Errno::EACCES => e
        attachment.destroy!
        raise e
      rescue Errno::EPROTO => e
        attachment.destroy!
        raise e
      end

    #else
    #  render status: :forbidden, nothing: true
    #end
  end

  # Get all attachments of a task as a ZIP archive.
  #
  # task_id  - ID of the task to get the attachments of.
  #
  # Returns the attachments as a ZIP archive.
  def retrieve_all
    task_id = params[:task_id]
    attachments = TaskAttachment.where(task_id: task_id)

    archive = Tempfile.new(["pms-attachments-zip", ".zip"])
    Zip::OutputStream.open(archive) do |z|
      attachments.each do |attachment|
        z.put_next_entry(attachment.filename)
        z << IO.read(attachment.file_path, mode: "rb")
      end
    end

    send_file archive.path
  end

  # Get the contents of an attachment file.
  #
  # task_id  - ID of the task the attachment is attached to.
  # filename - The attachment's filename.
  #
  # Returns the attachment file's contents with an appropriate
  #   Content-Type header.
  def retrieve
    task_id = params[:task_id]
    filename = params[:filename]
    attachment = TaskAttachment.find_by(task_id: task_id, filename: filename)
    send_file(attachment.file_path, disposition: :inline)
  end

  def download
    task_id = params[:task_id]
    filename = params[:filename]
    attachment = TaskAttachment.find_by(task_id: task_id, filename: filename)
    send_file(attachment.file_path, disposition: :download)
  end

  # Get a thumbnail image representation of an attachment
  # file. Currently only supported for image files.
  #
  # task_id  - ID of the task the attachment is attached to.
  # filename - The attachment's filename.
  #
  # Returns a thumbnail image with the same file type as the original.
  def retrieve_thumbnail
    task_id = params[:task_id]
    filename = params[:filename]
    attachment = TaskAttachment.find_by(task_id: task_id, filename: filename)

    thumbnail_path = ThumbnailService.thumbnail_path(attachment.file_path)
    ThumbnailService.thumbnail(attachment.file_path) unless File.exist?(thumbnail_path)

    send_file(thumbnail_path, disposition: :inline)
  end

  # Delete a task attachment.
  #
  # task_id  - ID of the task the attachment is attached to.
  # filename - The attachment's filename.
  #
  # Returns nothing.
  def destroy
    task = Task.find(params[:task_id])
    filename = params[:filename]
    attachment = TaskAttachment.find_by(task: task, filename: filename)
    if (attachment.attachment_deletable_by_user?(current_user) || task.annotatable_by_user?(current_user))
      if File.exist?(attachment.file_path)
        File.delete(attachment.file_path)
      end
      thumbnail_path = ThumbnailService.thumbnail_path(attachment.file_path)
      if File.exist?(thumbnail_path)
        File.delete(thumbnail_path)
      end
      attachment.destroy!
      status = :ok
    else
      status = :forbidden
    end

    render status: status, json: {}
  end

  def update
    task = Task.find(params[:id])
    attachment = TaskAttachment.find_by(task: task, filename: params[:filename])
    if attachment.update(sticky: params[:sticky])
      render json: attachment
    else
      render json: { errors: attachment.errors }, status: :unprocessable_entity
    end
  end

  private

  def permitted_update_params
    params
      .require(:task_attachment)
      .permit(:sticky)
  end

  def create_attachment(task, upload, user)
    filename = I18n.transliterate(upload.original_filename)
    type = upload.content_type
    TaskAttachment.create(task: task, filename: filename, content_type: type, created_by: user)
  end

  def create_archive_for_attachments(attachments, file)

  end
end
