# Executed if a user adds a new task to a process.
class CreateTaskAction < CreateAction
  def initialize(process, name, creator)
    super(process)
    @name = name
    @creator = creator
  end

  def allowed?(current_user)
    subject.editable_by_user?(current_user)
  end

  def do_create!
    operator = subject.tasks.last.try(:operator)
    task = Task.create!(process: subject, name: @name)
    call!(AssignTaskAction, task, operator, @creator) if operator
    task
  end
end
