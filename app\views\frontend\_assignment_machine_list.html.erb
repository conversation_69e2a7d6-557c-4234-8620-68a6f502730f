<% assignments.each do |assignment| %>
  <%= link_to assignment_path(assignment.id), class: ['list-group-item prio-', assignment.priority].join do %>

    <h4 class="list-group-item-heading">
      <% if show_state %>
        <small><%= assignment.project %></small>
      <% end %>

    <% cache("#{assignment.cache_key}/#{current_user.id}/assignment_flags") do %>
      <span><%= assignment.name %></span>
      <% if assignment.editable_by_user?(current_user) %>
        <i class="fas fa-pencil-alt pms-planner-icon"
           title="Sie sind hier planende Person">
        </i>
      <% end %>

      <% if assignment.due && Date.today > assignment.due && assignment.state == "active" %>
        <i class="fas fa-exclamation-triangle pms-overdue-icon"
           title="Dieser Auftrag ist überfällig">
        </i>
      <% end %>
      </h4>
    <% end %>

    <% cache("#{assignment.cache_key}/#{current_user.id}/processes_tasks") do %>
      <% if assignment.state != "completed" && assignment.current_processname(current_user) != "kein aktiver Prozess" && assignment.current_taskname(current_user) != "keine aktive Aufgaben"%>
        <p class="list-group-item-text">
          <span class="label current-process" title="Aktueller Prozess">
            <%= assignment.current_processname(current_user) %>
          </span>
          <span class="label current-arrow">
            <i class="fas fa-long-arrow-alt-right" aria-hidden="true"></i>
          </span>
          <span class="label current-task" title="Aktuelle Aufgabe">
            <%= assignment.current_taskname(current_user) %>
          </span>
        </p>
      <% end %>
    <% end %>

    <% cache("#{assignment.cache_key}/details") do %>
      <p class="list-group-item-text spacer">
        <span class="label <%= state_label_class(assignment.state) %>">
          <%= state_label_content(assignment.state) %>
        </span>
        <span class="label label-default" title="Auftrags-ID">
          <%= "##{assignment.id}#{".#{assignment.runs.last.version}" if assignment.runs.last.version > 1}" %>
        </span>
        <% if (assignment.state == "completed" || assignment.state == "failed") %>
          <span class="label label-default" title="Abschlusszeitpunkt">
            <i class="fas fa-flag-checkered pms-small-icon"></i>
            <%= l assignment.runs.last.completed_at, format: :short %>
          </span>
        <% else %>
          <span class="label label-default" title="Letzte Änderung">
            <i class="far fa-clock"></i>
            <%= l assignment.updated_at, format: :short %>
          </span>
        <% end %>
    <% end %>
    
    <% cache("#{assignment.cache_key}/#{current_user.id}/details") do %>
        <% if @immediate_work_ids.include?(assignment.id) %>
          <i class="fas fa-flag pms-immediate-work-icon"
             title="Sie sind hier einer aktuellen Aufgabe zugewiesen">
          </i>
        <% elsif assignment.has_near_work_for_user?(current_user) %>
          <i class="fas fa-flag pms-near-work-icon"
              title="Sie sind hier einer folgenden Aufgabe zugewiesen">
          </i>
        <% elsif @any_work_ids.include?(assignment.id) %>
          <i class="fas fa-flag pms-work-icon"
             title="Sie sind hier einer zukünftigen Aufgabe zugewiesen">
          </i>
        <% end %>
      </p>
    <% end %>

    <% if assignment.state == "active" || assignment.state == "paused" %>
      <% cache assignment.progress do %>
        <p>
          <div class="progress"
               title="Auftrag ist zu <%= assignment.progress %>% abgeschlossen">
            <div class="progress-bar"
                 role="progressbar"
                 aria-valuemin="0"
                 aria-valuemax="100"
                 style="width: <%= assignment.progress%>%;">
              <span class="sr-only"><%= assignment.progress%></span>
            </div>
          </div>
        </p>
      <% end %>
    <% end %>

    <% if (defined? advanced_timeline) && advanced_timeline %>
      <script type="text/javascript">
        google.charts.load("current", {packages:["timeline"]});
        google.charts.setOnLoadCallback(drawChartAssignment<%=assignment.id %>);
        function drawChartAssignment<%=assignment.id %>() {

          var container = document.getElementById('visual_frame_assignment<%=assignment.id %>');
          var chart = new google.visualization.Timeline(container);
          var dataTable = new google.visualization.DataTable();
          dataTable.addColumn({ type: 'string', id: 'Position' });
          dataTable.addColumn({ type: 'string', id: 'Name' });
          dataTable.addColumn({ type: 'date', id: 'Start' });
          dataTable.addColumn({ type: 'date', id: 'End' });
          dataTable.addRows([
            <% assignment.runs.last.processes.each do |process| %>
              [ 'Geplanter Ablauf', '<%= process.name %>', new Date(<%= process.planed_starttime.to_f * 1000 %>), new Date(<%= process.planed_endtime.to_f * 1000 %>) ],
              [ 'Realer Ablauf', '<%= process.name %>', new Date(<%= process.real_starttime.to_f * 1000 %>), new Date(<%= process.real_endtime.to_f * 1000 %>)],
              [ 'Kalkulierter Ablauf', '<%= process.name %>', new Date(<%= process.cached_calculated_starttime.to_f * 1000 %>), new Date(<%= process.calculated_endtime.to_f * 1000 %>)],
            <% end %>
          ]);
          chart.draw(dataTable);
          // workaround to get the right height
          var realheight = parseInt($("#visual_frame_assignment<%=assignment.id %> div:first-child div:first-child div:first-child div svg").attr("height"))+70;
          var options = {height: realheight};
          chart.draw(dataTable,options);
        }
      </script>

      <div id="visual_frame_assignment<%=assignment.id %>" style="margin-top: 10px;"></div>
    <% end %>

  <% end %>
<% end %>
 