# A process ("Prozess") is an ordered list of tasks which are thought
# form a single unit. They are the basic unit of reuse in the PMS
# system; when turned into a template (see ProcessTemplate), a process
# can be copied into any run by its planners, who thus don't need to
# create all tasks of a run by hand.
#
# Within a run, a process may depend on one or more other processes,
# which need to be completed before work on the process can
# start. These are called the "parents" of the process. Conversely,
# the set of other processes which directly depend on a process are
# the "children" of that process.
#
# NOTE: Unfortunately, this class cannot be named Process as this is
# the name of a class in the Ruby standard library.
class PmsProcess < ActiveRecord::Base
  # Associations
  belongs_to :run,
             touch: true
  has_many :parent_dependencies,
           class_name: "ProcessDependency",
           foreign_key: :child_id,
           inverse_of: :child,
           autosave: false
  has_many :child_dependencies,
           -> { order :child_index },
           class_name: "ProcessDependency",
           foreign_key: :parent_id,
           inverse_of: :parent,
           autosave: false
  has_many :parents,
           through: :parent_dependencies
  has_many :children,
           through: :child_dependencies
  has_many :tasks,
           -> { order :position },
           class_name: "Task",
           foreign_key: :process_id,
           dependent: :destroy

  # Validations
  validates :name, presence: true
  before_validation :fill_in_defaults
  after_touch :reset_completed

  amoeba do
    clone [:tasks]
    nullify :run_id
    set :process_completed => 0
  end

  # Scope: get every process for a given Group that is not completed
  def self.from_group(group_id)
    where(Task.where("process_id = pms_processes.id 
                      and operator_type = 'OperatorGroup' 
                      and operator_id = ?
                      and completed = 'false'", group_id)
    .exists)
    .includes(:run, :tasks, :parents)
  end

  # Duplicate the process and its contained tasks. The duplicate will
  # not belong to any run.
  #
  # Returns the duplicate process.
  def copy
    clone = amoeba_dup
    clone.parent_dependencies.clear
    clone.child_dependencies.clear
    clone.tasks.each { |t| t.process = clone }
    clone
  end

  # Find the assignment that the process belongs to.
  #
  # Returns the containing, or nil if the process is currently not
  # part of an assignment.
  def assignment
    run.try(:assignment)
  end

  # Determine if all tasks in the process are completed. If there are
  # no tasks in the process, consider it uncompleted.
  #
  # Returns true if the process is completed, or false otherwise.
  def completed?
    if process_completed == true
      return true
    elsif tasks.present? && tasks.all?(&:completed?)
      # skip validation and touch events
      update_column(:process_completed, true)
      return true
    else
      return false
    end
  end

  # reset the process_completed attribute after a touch event
  # for example if a finished task becomes unfinished
  def reset_completed
    if process_completed == true
      update_attribute(:process_completed, false)
    end
  end

  # Check if it is currently possible to complete the currently open
  # task of the process (if any). This takes into account the current
  # state of the containing run (e.g. if it is paused) and the
  # completion state of the process's parents.
  #
  # A completed process is never considered completable.
  #
  # Returns true if the process is completable, or false otherwise.
  def completable? 
    if run.inactive? || process_completed == true
      false
    elsif tasks.present?
      tasks.any?(&:completable?)
    else
      parents.all?(&:completed?)
    end
  end

  # Check if one of the parent processes is completable
  # see doc completable?
  def completable_next?
    parents.any?(&:completable?)
  end

  # Check if the process contains at least one completed task.
  #
  # Returns true if there is a completable task, or false otherwise.
  def has_completed_tasks?
    tasks.any?(&:completed?)
  end

  # Determines the IDs of all processes which this process directly
  # or indirectly depends on - that is, of the process's parents,
  # grandparents, grandgrandparents, etc.
  #
  # Returns the ancestors' IDs as an array. No particular order is
  # guaranteed.
  def ancestor_ids
    PmsProcess.find_by_sql(ancestor_ids_sql).map(&:id)
  end

  # Determines the IDs of all processes which directly or indirectly
  # depend on this process - that is, of the process's children,
  # grandchildren, grandgrandchildren etc.
  #
  # Returns the descendants' IDs as an array. No particular order is
  # guaranteed.
  def descendant_ids
    PmsProcess.find_by_sql(descendant_ids_sql).map(&:id)
  end

  # Check if a process is a parent of this process, its parents, the
  # parents of these parents etc.
  #
  # other - The other process to check with.
  #
  # Examples
  #
  #   ProcessDependency.create(child: process, parent: parent)
  #   ProcessDependency.create(child: parent, parent: grandparent)
  #   process.depends?(parent)
  #   #=> true
  #   parent.depends?(grandparent)
  #   #=> true
  #   process.depends?(grandparent)
  #   #=> true
  #
  # Returns true if the process depends on the other process, or false
  # otherwise.
  def depends?(other)
    ancestor_ids.include?(other.id)
  end

  # Check if it is currently possible to edit the process's name and
  # instruction as well as its contents (e.g. by adding tasks). To
  # preserve history, this is not possible if e.g. the process is
  # already completed.
  #
  # Returns true if the process can currently be edited, or false
  # otherwise.
  def editable?
    !completed? && run.editable?
  end

  # Returns true if children can be added to the process, or false
  # otherwise.
  def child_creatable?(user)
    run.editable? && run.editable_by_user?(user)
  end

  # Check if a user may currently edit the process in the sense of
  # #editable?. In addition to #editable?, this takes the user's
  # access rights to account.
  #
  # user - The user to check with.
  #
  # Returns true if the process is editable for the user, or false
  # otherwise.
  def editable_by_user?(user)
    editable? && run.editable_by_user?(user)
  end

  # Check if it is currently possible to change the process's position
  # within the process dependency graph of its containing run.  This
  # is only possible as long as history is preserved (e.g., not for
  # processes have already been worked on).
  #
  # Returns true if the process is currently movable within its run,
  # or false otherwise.
  def movable?
    has_completed_tasks?
  end

  # Check if a user may move a process in the sense of #movable?. In
  # addition to #movable?, this taks the user's access rights into
  # account.
  #
  # user - The user to check with.
  #
  # Returns true if the process is movable for the user, or false
  # otherwise.
  def movable_by_user?(user)
    movable? && editable_by_user?(user)
  end

  # Check if the process can currently be deleted. This is only
  # possible while it wouldn't cause history (e.g., completed tasks)
  # to be lost.
  #
  # Return true if the process is currently deletable, or false
  # otherwise.
  def deletable?
    !has_completed_tasks?
  end

  # Check if a user may delete the process. In addition to
  # #deletable?, this checks the user's access rights.
  #
  # user - The user to work with.
  #
  # Returns true if the process is deletable for the user, or false
  # otherwise.
  def deletable_by_user?(user)
    deletable? && editable_by_user?(user)
  end

  def invalidate_caches
    tasks.each do |task|
      task.touch
    end
  end

  ### Statistic Data ###

  def planed_starttime
    if !parents.any? # <--- hit db because its encapsulated in it self [this calls planed_endtime calls this 
                       # calls planed_endtime ...] until it hits the first process or 'due' is defined
      created_at
    else
      parents.map{|p| p.planed_endtime}.max
    end
  end

  def planed_endtime
    if due.nil?
      planed_starttime + 2.day
    else
      due.to_datetime
    end
  end

  def cached_calculated_starttime
    Rails.cache.fetch("#{cache_key}/calculated_starttime", expires_in: 12.hours) do
      calculated_starttime
    end
  end

  def calculated_starttime
    if !parents.any?
      created_at
    else
      parents.map{|p| p.calculated_endtime}.max
    end
  end

  def calculated_endtime
    cached_calculated_starttime + cached_calculated_duration
  end

  def cached_calculated_duration
    Rails.cache.fetch("#{cache_key}/calculated_duration", expires_in: 12.hours) do
      calculated_duration
    end
  end

  # the avg duration of the process
  # calculats the avg time of the tasks inside the process
  def calculated_duration
    duration = 0
    num_tasks = tasks.count
    query_result = PmsProcess.connection.select_all(avg_duration_sql)
    if query_result.rows[0][1] > 0
      duration = duration + query_result.rows[0][0]
    end
    duration + (num_tasks - query_result.rows[0][1]) * 1.day
  end

  def real_starttime
    if !parents.any?
      created_at
    else
      parents.map{|p| p.real_endtime}.max
    end
  end

  def real_endtime
    if completed?
      tasks.last.completed_at
    else
      Time.now
    end
  end

  def real_duration
    real_endtime - real_starttime
  end

  private

  def fill_in_defaults
    self.instruction ||= ""
    self.due ||= ""
  end

  def avg_duration_sql
    <<-SQL
    select sum(combined_tasks.avg_duration) as total_duration, count(*) as numKnownDurations from (
      select avg(t.duration) as avg_duration
      from tasks as t
      where t.completed = #{sql_boolean}
      and t.duration > 0
      group by t.name
      having t.name in (
        select tasks.name 
        from tasks inner join pms_processes 
        on pms_processes.id = tasks.process_id 
        where pms_processes.id = #{id})
    ) as combined_tasks
    SQL
  end

  def ancestor_ids_sql
    # We can get all ancestor IDs with one query by using SQL's WITH
    # RECURSIVE. This is supported in both SQLite and SQL Server, as
    # well as several other database systems such as PostgreSQL.
    # (In SQL Server, it's WITH instead of WITH RECURSIVE).
    <<-SQL
      #{sql_with_recursive} ancestors(id) AS (
        SELECT id FROM pms_processes WHERE id = #{id}
        UNION ALL
        SELECT process_dependencies.parent_id
        FROM process_dependencies, ancestors
        WHERE ancestors.id = process_dependencies.child_id
      )
      SELECT DISTINCT pms_processes.id
      FROM pms_processes INNER JOIN ancestors
      ON pms_processes.id = ancestors.id
    SQL
  end

  def descendant_ids_sql
    <<-SQL
      #{sql_with_recursive} descendants(id) AS (
        SELECT id FROM pms_processes WHERE id = #{id}
        UNION ALL
        SELECT process_dependencies.child_id
        FROM process_dependencies, descendants
        WHERE descendants.id = process_dependencies.parent_id
      )
      SELECT DISTINCT pms_processes.id
      FROM pms_processes INNER JOIN descendants
      ON pms_processes.id = descendants.id
    SQL
  end

  def sql_with_recursive
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "WITH"
    else
      "WITH RECURSIVE"
    end
  end

  def sql_boolean
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "1"
    else
      "\"t\""
    end
  end
end
