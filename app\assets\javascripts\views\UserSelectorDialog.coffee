#=require "collections/UserCollection"
#=require "views/UserSelectorItem"
#=require "views/SearchDialog"

App.UserSelectorDialog = App.SearchDialog.extend
  ###
  A dialog that allows a user to search for and select a user. Used to
  let the user assign someone to a process.
  ###

  #
  # Marionette.CollectionView options
  #

  childView: App.UserSelectorItem

  #
  # App.SearchDialog options
  #

  title: 'Benutzer auswählen'
  searchLabel: 'Vorname, Nachname oder Benutzername'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    App.SearchDialog::initialize.call(this)
    @collection = new App.UserCollection()
