# A task ("Aufgabe") represents a single thing that an operator should
# do as part of a process (see PmsProcess).
#
# Each task is assigned to a single user (who is then called the
# task's "operator") or to a group of group of users (see OperatorGroup).
# These user(s) are notified once the task can be started.
#
#
# For documentation purposes, the user who completes a task can
# annotate with a text note and/or by attaching files (such as photos
# showing the task's result). The assignment's planners can attach
# files as well, for instance to give design sketches and other kind
# of instruction files to the operator(s).
class Task < ActiveRecord::Base
  # Associations
  belongs_to :process, class_name: "PmsProcess", touch: true
  belongs_to :operator, polymorphic: true
  belongs_to :completed_by, class_name: "User"
  has_many :machine_tasks
  has_many :machines, through: :machine_tasks
  has_many :article_tasks
  has_many :articles, through: :article_tasks
  has_many :attachments, class_name: "TaskAttachment"
  has_many :sticky_attachments, -> { where(sticky: true) }, class_name: "TaskAttachment"

  # Validations
  validates :name, presence: true
  validate :must_be_completed_after_its_predecessors

  # Callbacks
  before_validation :fill_in_defaults
  before_destroy :dereference_membership_of_operator_on_destroy
  after_save :update_assignment_memberships
  after_update :touch_tasks

  acts_as_list scope: :process
  amoeba do
    include_association :machine_tasks
    include_association :article_tasks
    include_association :sticky_attachments
    nullify :completed
    nullify :completed_at
    nullify :completed_by_id
    nullify :note
    nullify :process_id
    set :completion_quality => 0
  end

  # Duplicate the task. The created duplicate is reset to be
  # uncompleted and unannotated. It is not automatically saved.
  #
  # Returns the created duplicate.
  def copy
    amoeba_dup
  end

  # Find the assignment which contains the task's process.
  #
  # Returns the assignment.
  def assignment
    process.try(:assignment)
  end

  # Return an array of the task(s) directly succeeding this task. This
  # is just (The task may have multiple successors if it
  # comes last in a process with multiple children.)

  # Find the set of tasks which become completable if this task is
  # completed. For a task in the middle of a process, this set only
  # consists of the directly following task; however, if the task is
  # the last in its process, each first task of the process's children
  # are part of the set.
  #
  # Returns the task's successors in an Enumerable.
  def successors
    if lower_item
      [lower_item]
    else
      process.children
        .reject { |p| p.tasks.empty? }
        .map    { |p| p.tasks.first  }
    end
  end

  # Find the tasks that this task is a successor of. See #successors
  # for an explanation what "successor" means here.
  #
  # Returns the task's predecessors in an Enumerable.
  def predecessors
    if higher_item
      [higher_item]
    else
      process.parents
        .reject { |p| p.tasks.empty? }
        .map    { |p| p.tasks.last   }
    end
  end

  # Determine whether it is currently possible to mark the task as
  # completed. This is only the case if all preceding tasks have
  # been completed and the containing run is currently active.
  #
  # Returns true if the task can be completed, or false otherwise.
  def completable?
    Rails.cache.fetch("#{cache_key}/is_completable", expires_in: 24.hours) do 
      if completed?
        return false
      else
        if process.parents.present? && !process.parents.all?(&:completed?)
          return false
        end
        return Task.connection.select_all(completable_sql).any?
      end
    end
  end

  #def completable_by_user?(user)
  #  if user != nil
  #    completable? && !user.workgroup.extern
  #  else
  #    completable?
  #  end
  #end

  # Determine wether it is still possible to undo marking a task as complete.
  # This is for instance not the case if successive tasks have been completed
  # or too much time has passed.
  #
  # Returns true if the task can be uncompleted, or false otherwise.
  def completion_undoable?
    return false if !completed? || successors.any?(&:completed?)
    timeout = Rails.configuration.pms_undo_complete_timeout
    times_out_at = completed_at + timeout
    Time.now < times_out_at
  end

  # Determine if it is currently possible to change the completion quality of
  # the task. This is only possible if the quality is not marked as bad.
  #
  # Returns true if the completion quality can be changed, or false otherwise.
  def completion_quality_changeable?
    completion_quality != 2
  end

  # Determine if a user may currently undo marking a task as completed. Implies
  # checking #completion_undoable?.
  #
  # user - The user to check for.
  #
  # Returns true if undoing completion is possible for the user, or false
  #   otherwise.
  def completion_undoable_by_user?(user)
    completion_undoable? && completed_by == user
  end

  # Check if a user may add annotations to the task, that is:
  #
  # - task note
  # - attachments
  #
  # user - The user to check for.
  #
  # Returns true if annotation is allowed for the user, or false
  #   otherwise.
  def annotatable_by_user?(user)
    if operator.is_a?(OperatorGroup)
      assigned_to_user = operator.users.include?(user)
    else
      assigned_to_user = operator == user
    end
    assigned_to_user || completed_by == user ||
        assignment.planners.include?(user) || Assignment.involving(user).include?(assignment)
  end

  # Determine if it is currently possible to assign the task to a
  # different user. To preserve history, there are cases when this is
  # not allowed, for instance if the task is already completed.
  #
  # Returns true if the task can be currently reassigned, or false
  #   otherwise.
  def reassignable?
    !completed?
  end

  # Check if a user may currently reassign the task to another
  # user. Implies checking #reassignable?.
  #
  # user - The user to check for.
  #
  # Returns true if reassigning is allowed for the user, or false
  #   otherwise.
  def reassignable_by_user?(user)
    reassignable? && (operator == user || editable_by_user?(user) || annotatable_by_user?(user))
  end

  # Determine if it's currently possible to modify the task's details,
  # that is:
  #
  # - task name
  # - task instruction
  #
  # Changes to these are not allowed when they would rewrite history,
  # as would for instance be the case if the task is already completed.
  def editable?
    !completed?
  end

  # Check if a user may currently edit the task's details. Implies
  # checking #editable? (see that method for a definition of "the
  # task's details").
  #
  # user - The user to check for.
  #
  # Returns true if editing is allowed for the user, or false
  #   otherwise.
  def editable_by_user?(user)
    editable? && process.editable_by_user?(user)
  end

  # Determine if it is currently possible to change the task's
  # position within its containing process. This is not allowed for
  # e.g. completed tasks as moving them would rewrite history.
  #
  # Returns true if the task can currently be moved, or false
  #   otherwise.
  def movable?
    editable?
  end

  # Check if a user may currently move a task within its
  # process. Implies checking #movable?.
  #
  # user - The user to check for.
  #
  # Returns true if moving is allowed for the user, or false otherwise.
  def movable_by_user?(user)
    movable? && editable_by_user?(user)
  end

  # Determines the date the task is due
  # if there is no time given through the process
  # use today or 2 days from now
  def due
    due_time = self.process.due
    if !due_time.nil?
      return due_time
    else
      if self.completable?
        return Time.now.to_date
      else
        return (Time.now + 2.days).to_date
      end
    end
  end

  def calculate_duration
    if duration < 0 
      if completed
        if predecessors.present?
          update!(duration: (completed_at - predecessors.first.completed_at))
          completed_at - predecessors.first.completed_at
        else
          # FIX ME created_at does not take the time a run was paused into consideration
          update!(duration: (completed_at - created_at))
          completed_at - created_at
        end
      else
        0
      end
    else
      duration
    end
  end

  private

  def fill_in_defaults
    self.completed ||= false
    self.instruction ||= ""
    self.note ||= ""
  end

  def must_be_completed_after_its_predecessors
    return unless completed? && has_uncompleted_predecessors?
    errors.add(:completed, "cannot complete task before predecessor")
  end

  def has_uncompleted_predecessors?
    predecessors.any? { |p| !p.completed? }
  end


  def update_assignment_memberships
    return unless operator_id_changed?
    decrease_membership_for_old_operator(old_operator)
    increase_membership_for_new_operator(operator)
  end

  def old_operator
    return nil unless operator_id_was
    old_operator_class = (operator_type_was || operator_type).constantize
    old_operator_class.find(operator_id_was)
  end

  def decrease_membership_for_old_operator(old_operator)
    return unless old_operator
    AssignmentMembership.get(old_operator, assignment).try(:remove_ref!)
  end

  def increase_membership_for_new_operator(new_operator)
    return unless new_operator
    AssignmentMembership.ensure(new_operator, assignment).add_ref!
  end

  def dereference_membership_of_operator_on_destroy
    decrease_membership_for_old_operator(operator)
  end

  def touch_tasks
    process.tasks.where(completed: false).each do |task|
      task.touch
    end
    successors.each do |task|
      task.touch
    end

    if completed == false 
      case operator
        when OperatorGroup
          ActionController::Base.new.expire_fragment("open_tasks_table/#{operator.id}/operator_group_cache")
      end
    end
  end

  def completable_sql
    <<-SQL
      SELECT 1 as one
      FROM tasks t1
      join pms_processes p1 on p1.id = t1.process_id
      join runs on p1.run_id = runs.id
      where t1.id = #{id}
      and completed = #{sql_boolean_false}
      and runs.state = 'active'
      and ((
      t1.position = 1
      and Not Exists (Select 1 from pms_processes p2 
                      join process_dependencies pd1 on pd1.child_id = p1.id 
                      and pd1.parent_id = p2.id
                      where p2.process_completed = #{sql_boolean_false})
      ) or (
      t1.position != 1
      and Exists (Select 1 from tasks t2 
                  where t2.process_id = t1.process_id 
                  and t2.position = (t1.position - 1) 
                  and t2.completed = #{sql_boolean_true})
      ))
    SQL
  end

  def sql_boolean_true
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "1"
    else
      "\"t\""
    end
  end

  def sql_boolean_false
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "0"
    else
      "\"f\""
    end
  end
end
