#=require "views/AssignmentHeader"
#=require "views/AssignmentRunsView"

App.AssignmentPage = Marionette.LayoutView.extend
  ###
  The page shown when an assignment is opened.
  ###

  #
  # Backbone.View options
  #

  className: 'pms-assignment span12'

  #
  # Marionette.ItemView options
  #

  template: JST['AssignmentPage']

  #
  # Marionette.Layout options
  #

  regions:
    header: '>header'
    main: '.pms-assignment-main'

  #
  # Methods
  #

  onShow: ->
    ###
    Override of Marionette.View#onShow.
    ###
    @header.show(new App.AssignmentHeader(model: @model))
    @main.show(new App.AssignmentRunsView(model: @model))

  #
  # Event Handlers
  #
