# An assignment ("Auftrag") is the top-level unit of organization in
# PMS. It represents a team effort to produce a specific product,
# e.g. a circuit board.
#
# An assignment has two types of members. Planners ("Planer") are
# responsible to define the required production steps and the
# dependencies between them in form of a run (see Run), as
# well as assign tasks to other members. These members are called
# operators ("<PERSON>beiter"); they can mark the tasks they are assigned
# to as completed.
#
# The Assignment model is designed to record the complete history of
# an assignment's lifetime, including failed production attempts. For
# this reason, an assignment contains not only the currently worked-on
# run, but also the previously canceled ones.
#
# Assignment Templates
#
# Assignments can be reused in two ways. First, an existing assignment
# can be copied to create a new one. Second, it is possible to make
# "assignment templates" from which new assignments can be created.
#
# An assignment template is simply an Assignment with a single run in
# the "template" state (see Run). It cannot be activated; the only
# thing you can do with an assignment template, other than editing its
# attributes and contents, is to copy it to create a concrete assignment
# from it.
class Assignment < ActiveRecord::Base
  # Associations
  has_many :memberships,
           class_name: "AssignmentMembership",
           dependent: :destroy
  has_many :members,
           through: :memberships,
           source: :member,
           source_type: "User"
  has_many :operator_groups,
           through: :memberships,
           source: :member,
           source_type: "OperatorGroup"
  has_many :planner_memberships,
           -> { where(role: "planner") },
           class_name: "AssignmentMembership"
  has_many :extern_memberships,
           -> { where(member_type: "Workgroup") },
           class_name: "AssignmentMembership"
  has_many :observer_memberships,
           -> { where(role: "observer") },
           class_name: "AssignmentMembership"
  has_many :planners,
           through: :planner_memberships,
           source: :member,
           source_type: "User"
  has_many :extern_workgroups,
           through: :extern_memberships,
           source: :member,
           source_type: "Workgroup"
  has_many :observers,
           through: :observer_memberships,
           source: :member,
           source_type: "User"
  has_many :runs,
           -> { order(:version) },
           dependent: :destroy

  # Validations
  validates :name, presence: true
  validates :priority, presence: true
  validates :datapath, length: { maximum: 4000 }
  validates :board_count, presence: true

  # Callbacks
  before_validation :fill_in_defaults

  amoeba do
    nullify :due
  end

  # Query for all currently active assignments in the system.
  #
  # Returns the assignments as an ActiveRecord::Relation.
  scope :active, (lambda do
    joins(:runs)
      .where("runs.state" => "active")
      .uniq
  end)

  # Find all assignments in which a specific user is involved either
  # as a planner or operator. This includes assignments where a task is
  # assigned to an operator group which the user belongs to.
  #
  # user - The user to find involved assignments for.
  #
  # Returns the found assignments as an ActiveRecord::Relation.
  def self.involving(user)
    joins(:memberships)
      .where("(assignment_memberships.member_id = ?
               AND assignment_memberships.member_type = 'User')
              OR
              (assignment_memberships.member_id IN (?)
               AND assignment_memberships.member_type = 'OperatorGroup')",
             user.id,
             user.operator_groups.pluck(:id))
      .distinct
  end

  def self.of_workgroup(workgroup)
    joins(:planners)
      .where(users: { workgroup_id: workgroup.id })
      .distinct
  end

  def self.of_extern_workgroup(workgroup)
    joins(:extern_workgroups)
      .where(workgroups: { id: workgroup.id })
      .distinct
  end

  # Determine the assignment's state, which is calculated from the
  # state of its latest run.
  #
  # Returns one of "template", "draft", "active", "paused", "completed"
  #   and "failed". Consult the documentation of Run to see what these
  #   states mean.
  def state
    runs.last.state
  end

  # Determine the assignment's current progress, which corresponds to
  # the percentage of completed tasks within the latest run.
  #
  # Returns an integer which corresponds to the rounded percentage of
  #   completed tasks.
  def progress
    Rails.cache.fetch("#{cache_key}/progress") do
      calculate_progress
    end
  end

  # Determine whether the assignment's latest run is done. (See
  # Run#done? for an explanation what "done" means in this context.)
  #
  # Returns true if the assignment is done or false otherwise.
  def done?
    runs.last.done?
  end

  def reopen_after_repeat!
    if state == "completed"
      ReopenAssignmentAction.new(assignment)
      execute_action_and_respond(action)
    end
  end

  # Check if a user may edit the assignment's details, which consist
  # the following elements:
  #
  # - name
  # - project name
  # - description
  # - due date
  # - the list of planners
  #
  # user - The user to check for.
  #
  # Returns true if the user may edit the assignment, or false otherwise.
  def editable_by_user?(user)
    if state == "template"
      user.can_edit_templates?
    else
      planners.include?(user) || user.present? && user.can_edit_planners?
    end
  end

  # Returns true if the user is allowed to set assignments to highest priority, or false otherwise.
  def highest_priority_editable_by_user?(user)
    user.present? && user.can_set_highest_priority?
  end

  # Check if the assignment may still be deleted. This is not the case
  # anymore if the assignment's deletion would destroy history (e.g.,
  # work done in a sequence of the assignment).
  #
  # Returns true if the assignment is still deletable, or false otherwise.
  def deletable?
    runs.first.state == "template" || runs.first.state == "draft"
  end

  # Check if a user may delete the assignment. In addition to
  # #deletable?, this checks the user's permissions.
  #
  # user - The user to check for.
  #
  # Returns true if the user may delete the assignment or false otherwise.
  def deletable_by_user?(user)
    deletable? && editable_by_user?(user)
  end

  # Check if the assignment's current sequence contains completable or
  # future tasks for a specific user.
  #
  # user - The user to check for.
  #
  # Returns true if tasks for the user were found, or false otherwise.
  def has_work_for_user?(user)
    Rails.cache.fetch("#{cache_key}/has_work_for_user/#{user.cache_key}") do
      uncompleted_tasks_for_user(user).exists?
    end
  end

  # Check if the assignment's current sequence contains a currently
  # completable task for a specific user. Uncompleted, but still
  # uncompletable tasks are not considered, unlike #has_work_for_user?.
  #
  # user - The user to check for.
  #
  # Returns true if current tasks for the user were found, or false
  # otherwise.
  def has_immediate_work_for_user?(user)
    Rails.cache.fetch("#{cache_key}/has_immediate_work_for_user/#{user.cache_key}") do
      uncompleted_tasks_for_user(user)
        .any?(&:completable?)
    end
  end

  # Check if the assignment's current sequence contains a process that
  # is after a completable one for the specific user.
  def has_near_work_for_user?(user)
    Rails.cache.fetch("#{cache_key}/has_near_work_for_user/#{user.cache_key}") do
      uncompleted_tasks_for_user(user).each do |task|
        first_predecessor = task.predecessors.first
        return true if first_predecessor&.completable?
      end
      false
    end
  end

  # Check if the assignment's current sequence contains a currently
  # completable task for a specific group. Uncompleted, but still
  # uncompletable tasks are not considered, unlike #has_work_for_group?.
  #
  # group - The group to check for.
  #
  # Returns true if current tasks for the group were found, or false
  # otherwise.
  def has_immediate_work_for_group?(group)
    Rails.cache.fetch("#{cache_key}/has_immediate_work_for_group/#{group.cache_key}") do
      uncompleted_tasks_for_group(group)
        .any?(&:completable?)
    end
  end

  # Duplicate the assignment, including its most recent sequence. All
  # copied tasks are reset to be uncompleted and unannotated. To make
  # the duplicate assignment easily distinguishable from the original,
  # an explanitory suffix is appended to the assignment name.
  #
  # Returns the duplicate assignment.
  def copy
    duplicate = amoeba_dup
    duplicate.name += " (Kopie)"
    copy_last_run_to(duplicate)
    duplicate
  end

  def cached_current_task(user)
    Rails.cache.fetch("#{cache_key}/current_task/#{user.cache_key}", expires_in: 6.days) do
      if has_immediate_work_for_user?(user)
        Task.find_by_sql(current_task_for_user_sql(user))
      else
        Task.find_by_sql(current_task_sql)
      end
    end
  end

  # Returns the current taskname of the assignment if one exists
  # if nil returns "keine aktive Aufgaben"
  def current_taskname(user)
    tasks = cached_current_task(user)
    return "keine aktive Aufgaben" if tasks.empty?
    output = tasks.first.name
    tasks.drop(1).each do |task|
      output = output + " | " + task.name
    end
    output
  end

  # Returns the current processname of the assignment if one exists
  # if nil returns "kein aktiver Prozess"
  def current_processname(user)
    tasks = cached_current_task(user)
    return "kein aktiver Prozess" if tasks.empty?
    output = tasks.first.p_name
    tasks.drop(1).each do |task|
      output = output + " | " + task.p_name
    end
    output
  end

  def self.any_work_for_user_ids(assignment_ids, user)
    return [] if assignment_ids.blank?
    group_ids = user.operator_groups.pluck(:id)

    rel = Assignment
      .joins(runs: { processes: :tasks })
      .where(assignments: { id: assignment_ids })
      .where(runs: { state: 'active' })
      .where("NOT EXISTS (SELECT 1 FROM runs r2 WHERE r2.assignment_id = runs.assignment_id AND runs.version < r2.version)")
      .where(tasks: { completed: false })
      .where(
        "(tasks.operator_type = 'User' AND tasks.operator_id = :uid) OR (tasks.operator_type = 'OperatorGroup' AND tasks.operator_id IN (:gids))",
        uid: user.id, gids: group_ids
      )
      .distinct

    rel.pluck('assignments.id')
  end

  def self.immediate_work_for_user_ids(assignment_ids, user)
    return [] if assignment_ids.blank?
    group_ids = user.operator_groups.pluck(:id)

    rel = Assignment
      .joins(runs: { processes: :tasks })
      .where(assignments: { id: assignment_ids })
      .where(runs: { state: 'active' })
      .where("NOT EXISTS (SELECT 1 FROM runs r2 WHERE r2.assignment_id = runs.assignment_id AND runs.version < r2.version)")
      .where(tasks: { completed: false })
      .where(
        "(tasks.operator_type = 'User' AND tasks.operator_id = :uid) OR (tasks.operator_type = 'OperatorGroup' AND tasks.operator_id IN (:gids))",
        uid: user.id, gids: group_ids
      )

    completable_sql = <<-SQL
      (
        (tasks.position = 1 AND NOT EXISTS (
          SELECT 1 FROM process_dependencies pd1
          JOIN pms_processes p2 ON p2.id = pd1.parent_id
          WHERE pd1.child_id = tasks.process_id AND p2.process_completed = ?
        ))
        OR
        (tasks.position <> 1 AND EXISTS (
          SELECT 1 FROM tasks t2
          WHERE t2.process_id = tasks.process_id AND t2.position = (tasks.position - 1) AND t2.completed = ?
        ))
      )
    SQL

    rel = rel.where(completable_sql, false, true).distinct
    rel.pluck('assignments.id')
  end

  def self.near_work_for_user_ids(assignment_ids, user)
    return [] if assignment_ids.blank?
    group_ids = user.operator_groups.pluck(:id)

  end

  private

  def fill_in_defaults
    self.project ||= ""
    self.datapath ||= ""
    self.description ||= ""
  end

  def copy_last_run_to(assignment)
    current_run = runs.last
    run_copy = current_run.copy
    run_copy.version = 1
    run_copy.assignment = assignment
    assignment.runs << run_copy
  end

  def calculate_progress
    num_tasks_by_completed =
      Task.joins(process: { run: :assignment })
      .where("runs.id" => runs.last.id)
      .group(:completed)
      .count
    num_completed = num_tasks_by_completed[true] || 0
    num_uncompleted = num_tasks_by_completed[false] || 0
    num_total = num_completed + num_uncompleted
    (num_total == 0) ? 0 : (100.0 * num_completed / num_total).to_i
  end

  def uncompleted_tasks_for_user(user)
    current_run = runs.last
    return Task.none unless current_run.state == "active"
    current_run.tasks
      .where(completed: false)
      .where("(tasks.operator_id = ? AND operator_type = 'User')
              OR
              (operator_id IN (?) AND operator_type = 'OperatorGroup')",
             user.id,
             user.operator_groups.pluck(:id))
  end

  def uncompleted_tasks_for_group(group)
    current_run = runs.last
    return Task.none unless current_run.state == "active"
    current_run.tasks
      .where(completed: false)
      .where("(tasks.operator_id = ? AND operator_type = 'OperatorGroup')",
             group.id)
  end

  def current_task_sql
    <<-SQL
      SELECT t0.name, p0.name as p_name
      FROM tasks t0
      join pms_processes p0 on p0.id = t0.process_id
      join runs r1 ON (r1.assignment_id = #{id} and p0.run_id = r1.id)
          LEFT OUTER JOIN runs r2 ON (r2.assignment_id = #{id} AND (r1.version < r2.version))
      where r2.id IS NULL
      and t0.completed = #{sql_boolean_false}
      and r1.state = 'active'
      and ((
          t0.position = 1
          and Not Exists (Select 1 from pms_processes p2
                          join process_dependencies pd1 on pd1.child_id = p0.id
                          and pd1.parent_id = p2.id
                          where p2.process_completed = #{sql_boolean_false})
          ) or (
          t0.position != 1
          and Exists (Select 1 from tasks t2
                      where t2.process_id = t0.process_id
                      and t2.position = (t0.position - 1)
                      and t2.completed = #{sql_boolean_true})
          ))
    SQL
  end

  def current_task_for_user_sql(user)
    <<-SQL
      SELECT t0.name, p0.name as p_name
      FROM tasks t0
      join pms_processes p0 on p0.id = t0.process_id
      join runs r1 ON (r1.assignment_id = #{id} and p0.run_id = r1.id)
          LEFT OUTER JOIN runs r2 ON (r2.assignment_id = #{id} AND (r1.version < r2.version))
      where r2.id IS NULL
      and t0.completed = #{sql_boolean_false}
      and r1.state = 'active'
      and ((
          t0.position = 1
          and Not Exists (Select 1 from pms_processes p2
                          join process_dependencies pd1 on pd1.child_id = p0.id
                          and pd1.parent_id = p2.id
                          where p2.process_completed = #{sql_boolean_false})
          ) or (
          t0.position != 1
          and Exists (Select 1 from tasks t2
                      where t2.process_id = t0.process_id
                      and t2.position = (t0.position - 1)
                      and t2.completed = #{sql_boolean_true})
          ))
    SQL
  end

  def sql_boolean_true
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "1"
    else
      "\"t\""
    end
  end

  def sql_boolean_false
    if ActiveRecord::Base.connection.adapter_name == "MSSQL"
      "0"
    else
      "\"f\""
    end
  end
end
