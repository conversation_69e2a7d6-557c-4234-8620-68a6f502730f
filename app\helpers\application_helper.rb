require "pms/serialize"

# Helper whose methods are available to all view template.
module ApplicationHelper
  include Pms::Serialize

  # Like Pms::Serialize#to_json, but automatically passes
  # #current_user as serialization scope.
  #
  # target  - The record to serialize.
  # options - See Pms::Serialize#to_json.
  #
  # Returns the target's JSON representation.
  def to_json(subject, options = {})
    super(subject, options.merge(scope: current_user))
  end
end
