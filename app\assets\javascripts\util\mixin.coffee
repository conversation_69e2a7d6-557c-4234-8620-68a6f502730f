App.mixin = (mixins, target) ->
  ###
  Augments the prototype of class `target` with the methods and attributes
  of one or more mixins. These are simple objects with reusable method
  definitions. As a special case, a mixin can contain an `$initialize`
  method that, when mixed into a class, is prepended to the `initialize`
  method of that class, which allows a mixin to have automatically-invoked
  initialization logic.

  Here is a simple example:

      CanShowAlert = ->
        $initialize: (options) ->
          @alertsShown = 0

        alert: (messageId) ->
          alert(@messages[messageId])

      App.SomeView = mixin [
        CanShowAlert
      ], Backbone.View.extend
        messages:
          success: 'Successful!'
          error: 'Error!'

      view = new App.SomeView
      view.alertsShown      # 0
      view.alert('success') # "Sucessful!" shown
      view.alert('error')   # "Error!" shown
      view.alertsShown      # 2

  `mixins` must be an array. The modified target class is returned.
  ###
  for m in mixins
    for key, value of m
      if key == '$initialize'
        _prependInitializer(target, value)
      else
        target::[key] = value
  target

_prependInitializer = (target, initializer) ->
  ###
  Makes sure the mixin's `$initialize` method is automatically called
  before the `initialize` method of the target class.
  ###
  original = target::initialize
  target::initialize = (options) ->
    initializer.call(this, options)
    original?.call(this, options)
