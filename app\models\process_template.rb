# Process templates ("Prozessvorlagen") are pre-built processes that
# planners can use to quickly compose runs. In addition to saving
# planners time, process templates ensure consistency across
# assignments.
#
# Process templates can only be created by administrators.
class ProcessTemplate < ActiveRecord::Base
  # Associations
  has_many :task_templates, 
            -> { order(:position) },
            class_name: "TaskTemplate",
            foreign_key: :process_template_id,
            dependent: :destroy

  # Validations
  validates :name, presence: true

  # Callbacks
  after_initialize :fill_in_defaults
  before_save :fill_in_defaults

  # Create a new Process from the template. The process is returned
  # unpersisted (#new_record? returns true).
  #
  # Returns the constructed Process.
  def new_instance
    process = PmsProcess.new(name: name, instruction: instruction)
    instantiate_tasks(process)
    process
  end

  private

  def instantiate_tasks(process)
    task_templates.each do |template|
      task = Task.new(name: template.name, instruction: template.instruction, operator: template.operator)
      template.template_machines.each do |machine|
        task.machines << machine
      end
      template.article_task_templates.each do |article_task_template|
        article_task = ArticleTask.new(amount: article_task_template.amount, article: article_task_template.article)
        task.article_tasks << article_task
      end
      process.tasks << task
    end
  end

  def fill_in_defaults
    self.instruction ||= ""
  end
end
