# Executed when a user assigns a task to another user
# (or the user itself.)
class AssignTaskAction < Action
  # Initialize an AssignTaskAction.
  #
  # task     - The task to assign.
  # operator - The user to assign to.
  # assigning- The user who assigns the operator
  def initialize(task, operator, assigning)
    super(task)
    @operator = operator
    @assigning = assigning
  end

  # See Action.
  def allowed?(operator)
    subject.reassignable_by_user?(operator)
  end

  # See Action.
  def do_execute!
    notify_old_group(subject.operator, @operator)
    case subject.operator
      when OperatorGroup
        ActionController::Base.new.expire_fragment("open_tasks_table/#{subject.operator.id}/operator_group_cache")
    end
    subject.update!(operator: @operator)
    notify_new_operator(@operator, @assigning)
  end

  private

  def notify_new_operator(operator, assigning)
    return unless subject.process.run.state == "active"
    case operator
      when User
        if subject.completable? == true
          notify(:operator_assigned_task_ready, subject, assigning)
        else
          notify(:operator_assigned, subject, assigning)
        end
      when OperatorGroup
        if subject.completable? == true
          notify(:group_assigned_task_ready, subject, assigning)
        else
          notify(:group_assigned, subject, assigning)
        end
    end
  end

  def notify_old_group(group, new_operator)
    return unless subject.process.run.state == "active"
    case group
      when OperatorGroup
        notify(:group_task_redirected, subject, group, new_operator)
    end
  end
  
end
