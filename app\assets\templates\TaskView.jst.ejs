<h4 class="pms-task-header">

  <%
  // This anchor element is used for jumping to a specific task when the
  // the assignment page is loaded. It is placed above the actual TaskView
  // to prevent the fixed header bar from occluding the top part of the
  // task.
  %>
  <div id="aufgabe<%= id %>" class="pms-task-anchor"></div>

  <button class="btn btn-default btn-sm pms-task-check-btn"
    <% if (completable && !assignedToUser) { %>
      data-toggle="popover"
      data-placement="right"
      data-html="true"
      data-content="<%= promptTaskCompletion() %>"
    <% } %>
    <%= (completable || completion_undoable) ? '' : 'disabled' %> >
    <i class="fas fa-check pms-task-check-btn-icon"></i>
  </button>

  <i class='fa fa-pause pms-pause-icon pms-task-tag-pause'></i> 
  <span class="pms-task-name">
    <%- name %>
  </span>

  <span class="pull-right pms-task-buttons" href="#">
    <% if (completion_quality && annotatable) { %>
    <button class="btn btn-sm btn-default pms-task-quality-btn"
      title="Qualität ändern"
      data-toggle="popover"
      data-placement="left"
      data-html="true"
      data-content="<%= promptChangeQuality() %>" >
      <i class="<%= {
        1: 'fas fa-thumbs-up',
        2: 'fas fa-thumbs-down'
        }[completion_quality] %>">
      </i>
    </button>
    <% } %>
    <button class="btn btn-sm btn-default pms-task-attachments-btn"
            title="Anhänge">
      <span class="pms-task-num-attachments"></span>
      <i class=" fas fa-paperclip"></i>
    </button>
    <button class="btn btn-sm btn-default pms-task-attachments-pictures-btn"
            title="Bildansicht">
      <span class="pms-task-num-pictures"></span>
      <i class=" fas fa-images"></i>
    </button>
    <% if ((editable || (reassignable && assignedToUser))) { %>
      <button class="hidden-xs btn btn-sm btn-default pms-task-add-utility-btn"
        title="Maschinen hinzufügen"
        data-toggle="popover"
        data-placement="left"
        data-html="true"
        data-content="<%= promptAddMachineOrMaterial() %>" >
        <i class="fas fa-cubes pms-task-add-material-or-machine-btn-icon"></i>
      </button>
    <% } %>
    <% if (editable) { %>
      <a class="hidden-xs btn btn-sm btn-default pms-task-move-btn"
         title="Aufgabe verschieben">
        <i class="fas fa-bars"></i>
      </a>
      <button class="hidden-xs btn btn-sm btn-default pms-task-delete-btn"
              <% if (completed) { %>
              disabled title="Abgeschlossene Aufgaben können nicht gelöscht werden"
              <% } else { %>
              title="Aufgabe löschen"
              <% } %>>
        <i class="far fa-trash-alt"></i>
      </button>
    <% } %>
  </span>

</h4>

<dl class="dl-horizontal pms-details-list">
  <dt>Zugewiesen</dt>
  <dd>
    <span class="pms-task-operator">
      <% if (operator && operator.type == "user") { %>
        <%= prettyUser(operator, id) %>
      <% } else { %>
        <%= prettyOperatorGroup(operator, assignedToUser, id) %>
      <% } %>
    </span>
    <% if (reassignable) { %>
      <button class="btn btn-xs btn-default pms-task-assign-btn">
        <% if (assignedToUser) { %>
          Abgeben
        <% } else { %>
          Zuweisen
        <% } %>
      </button>
    <% } %>
  </dd>
  <dt>Instruktion</dt>
  <dd>
    <div class="pms-task-instruction pms-multi-line"><%=
      App.TemplateHelpers.formatText(instruction, pmsID)
    %></div>
  </dd>

  <div class="pms-task-articles-holder" style="display:none">  
    <dt>Material</dt>
    <dd>
      <div class="pms-task-articles">
        <table class="pms-articles" style="width:100%;">
          <% /* MaterialView(s) */ %>
        </table>
      </div>
    </dd>
  </div>

  <div class="pms-task-machines-holder" style="display:none">
    <dt>Maschinen</dt>
    <dd>
      <div class="pms-task-machines">
        <table class="pms-machines" style="width:100%;">
          <% /* MachineView(s) */ %>
        </table>
      </div>
    </dd>
  </div>

  <% if (assignmentState != "draft") { %>
    <dt>Bemerkung</dt>
    <dd>
      <div class="pms-task-note pms-multi-line"><%=
        App.TemplateHelpers.formatText(note, pmsID)
      %></div>
    </dd>
  <% } %>
  <% if(completed_at) { %>
    <dt>Abgeschlossen</dt>
    <dd>
      <%= prettyUser(completed_by, id) %>
      am
      <%= prettyDateTime(completed_at) %>
    </dd>
  <% } %>
</dl>
