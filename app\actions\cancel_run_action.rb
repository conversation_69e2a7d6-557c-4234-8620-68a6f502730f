# Executed when a planner cancels the latest run of an assignment.
# This differs from canceling a complete assignment in that it leads
# to the creation of a new draft run.
class CancelRunAction < Action
  # Initialize the action.
  #
  # run - The run to cancel.
  def initialize(run)
    super(run)
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.update!(state: "failed")
    subject.update!(completed_at: Time.now)
    new_run = create_new_draft_run

    copy_attachments_previous_run if Dir.exist?(Pathname.new(Rails.configuration.pms_attachments_root) + "Auftrag #{subject.assignment.id}" + "Durchlauf #{subject.version}")

    mark_changed(new_run)
    mark_changed(subject.assignment.reload) # now a draft again
    
    # notify_after_undo_completion_timeout
    notify(:assignment_failed, subject.assignment)
    notify(:assignment_failed_observer, subject.assignment)

    handle_now_failed_tasks
  end

  private

  def copy_attachments_previous_run
    dest = Pathname.new(Rails.configuration.pms_attachments_root)
    dest += "Auftrag #{subject.assignment.id}"
    dest += "Durchlauf #{subject.version + 1}"
    FileUtils.mkdir_p(dest.dirname)
    src = Pathname.new(Rails.configuration.pms_attachments_root)
    src += "Auftrag #{subject.assignment.id}"
    src += "Durchlauf #{subject.version}"
    FileUtils.cp_r src, dest
  end

  def create_new_draft_run
    new_run = subject.copy
    new_run.update!(completed_at: nil)
    new_run.save!
    new_run.processes.each do |process|
      process.tasks.each do |task|
        task.update!(completion_quality: 0)
        task.attachments.where(sticky: false).destroy_all
      end
    end
    new_run
  end

  def handle_now_failed_tasks
    subject.tasks.where(completed: false).each do |failed_task|
      # notify_after_undo_completion_timeout
      case failed_task.operator
      when User
        notify(:assignment_with_task_failed, subject.assignment, failed_task)
      when OperatorGroup
        # TODO needs implementation
      end
    end
  end

  # def notify_after_undo_completion_timeout
  #   timeout = Rails.configuration.pms_undo_complete_timeout
  #   NotificationMailer.delay(run_at: timeout.from_now)
  # end
end
