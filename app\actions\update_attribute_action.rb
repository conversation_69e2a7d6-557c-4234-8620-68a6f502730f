# An Action which sets a model attribute to a specific value.  It
# avoids the need to create many different action classes for simple,
# side-effect-free types of attribute updates (no other changed
# records, no notification mail, etc.).
#
# This action assumes that its subject has an #editable_by_user?
# method, and that #editable_by_user? determines whether the
# action is allowed. If you need other validation logic, consider
# creating a subclass.
class UpdateAttributeAction < Action
  # Initializes the action.
  #
  # subject - The record to modify.
  # attr    - The name of the attribute to change (as a symbol).
  # value   - The value to set the attribute to.
  def initialize(subject, attr, value)
    super(subject)
    @attr = attr
    @value = value
  end

  # See Action.
  def allowed?(user)
    subject.editable_by_user?(user)
  end

  # See Action.
  def do_execute!
    subject.update!(@attr => @value)
  end
end
