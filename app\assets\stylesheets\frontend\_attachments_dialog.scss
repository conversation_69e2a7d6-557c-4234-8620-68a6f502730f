// Styles for the AttachmentsDialog Backbone view, used on the
// assignment page.

$pms-attachment-list-item-size: 112px;

.pms-attachments-list {
}

.pms-attachments-list .list-group-item {
  display: flex;
  height: $pms-attachment-list-item-size;
  padding: 0;

  .pms-attachment-link {
    display: flex;
    flex: 1 0;

    &:hover {
      background-color: #e6e6e6;
      text-decoration: none;
    }

    aside {
      width: $pms-attachment-list-item-size;
      line-height: $pms-attachment-list-item-size;
      text-align: center;
      vertical-align: middle;

      border-right: 1px solid $list-group-border;

      i.fas {
        font-size: 48px;
        line-height: $pms-attachment-list-item-size;
      }
    }

    span.pms-attachment-filename {
      flex: 1;
      margin-left: 0.5em;
      font-weight: bold;
      line-height: $pms-attachment-list-item-size;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
  }
  
  .pms-attachment-download-btn {
    border: none;
    border-left: 1px solid $list-group-border;
  }

  .pms-attachment-actions-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .pms-attachment-cb-container {
      border-left: 1px solid $list-group-border;
      border-bottom: 1px solid $list-group-border;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
  
      .pms-attachment-sticky-cb {
        opacity: 0;
        height:100%;
        width: 100%;
        position: absolute;
      }

      .pms-attachment-sticky-cb:checked ~ .checkmark{
        color: $brand-primary;
      }
    }

    .pms-attachment-cb-container:hover {
      background-color: #e6e6e6;
    }

    .pms-attachment-delete-btn {
      border: none;
      border-left: 1px solid $list-group-border;
      height: 50%;
    }
  }

}


.pms-attachments-upload-form input[type=file] {
  margin-bottom: 8px;
}
