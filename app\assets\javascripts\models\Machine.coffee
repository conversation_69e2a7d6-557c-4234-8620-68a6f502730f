#=require "collections/MachineAttachmentCollection"
#=require "models/Model"
#=require "models/User"
#=require "models/MachineAttachment"

App.Machine = App.Model.extend
  ###
  Client-side representation of Machine.
  ###

  #
  # Backbone.RelationalModel options
  #

  relations: [
    type: Backbone.HasOne
    key: 'responsible'
    keySource: 'responsible_id'
    includeInJSON: 'id',
    relatedModel: App.User
  ,
    type: Backbone.HasOne
    key: 'deputy'
    keySource: 'deputy_id'
    includeInJSON: 'id',
    relatedModel: App.User
  ,
    type: Backbone.HasMany
    key: 'attachments'
    keySource: 'attachment_ids'
    relatedModel: App.MachineAttachment
    collectionType: App.MachineAttachmentCollection
    reverseRelation:
      key: 'machine',
      keySource: 'machine_id',
      includeInJSON: 'id'
  ]

  #
  # App.Model options
  #

  jsonSingularRoot: 'machine'
  jsonPluralRoot: 'machines'
