require "pms/serialize"

# Provides common behavior and helper methods to all controllers.
class ApplicationController < ActionController::Base
  include Pms::Serialize
  protect_from_forgery

  before_action do
    if Rails.env.development? || Rails.env.staging?
      if current_user && current_user.can_edit_users?
        Rack::MiniProfiler.authorize_request
      end
    end
  end

  # Add the requester's session ID to the logging payload.
  # See https://github.com/roidrage/lograge
  #
  # Returns nothing.
  def append_info_to_payload(payload)
    super
    payload[:session_id] = session.id
  end

  # Overrides Pms::Serialize#as_json such that #current_user is always
  # passed as the serialization scope. This makes computed JSON
  # attributes based on user access rights (e.g. "editable") work
  # correctly.
  #
  # target  - The record to serialize.
  # options - See Pms::Serialize#as_json.
  #
  # Returns the target's JSON representation as hash.
  def as_json(target, options = {})
    super(target, options.merge(scope: current_user))
  end

  # Execute an Action and send its result (the action's subject and
  # everything returned by #changed_records) to the client. If the
  # action is not allowed for the current user, render an empty "403
  # Forbidden" response instead.
  #
  # Returns nothing.
  def execute_action_and_respond(action)
    assert_action_allowed(action) || return
    action.execute!
    render json: to_json(action.subject, also: action.changed_records)
  end

  # Execute an Action and send its result (the record created by the
  # action and everything returned by #changed_records) to the
  # client. If the action is not allowed for the current user, render
  # an empty "403 Forbidden" response instead.
  #
  # Returns nothing.
  def execute_create_action_and_respond(action)
    assert_action_allowed(action) || return
    action.execute!
    render json: to_json(action.created_record, also: action.changed_records)
  end

  # Make sure that an action is allowed for the current user. If not,
  # return a "403 Forbidden" response to the client.
  #
  # Examples
  #
  #   action = CreateSomethingAction.new(...)
  #   assert_action_allowed(action) || return
  #   action.execute!
  #   redirect_to action: edit, id: action.created_record.id
  #
  # Returns true if the action is allowed, or false otherwise. You can
  #   use this return value to return early from a controller method if
  #   the action is forbidden (see the example above).
  def assert_action_allowed(action)
    if action && action.allowed?(current_user)
      true
    else
      render status: :forbidden, json: {}
      false
    end
  end

  def expire_open_tasks_cache
    ActionController::Base.new.expire_fragment("open_tasks_table/#{current_user.workgroup_id}")

    OperatorGroupUser.where("user_id = ?",current_user.id).each do |group|
      ActionController::Base.new.expire_fragment("open_tasks_table/#{group.operator_group_id}/operator_group_cache")
    end
    redirect_to(:open_tasks)
  end
end
