#=require "models/Task"
#=require "models/Article"
#=require "models/ArticleTask"
#=require "views/AttachmentsDialog"
#=require "views/AttachmentsPictureView"
#=require "views/OperatorSearchDialog"
#=require "views/MachineSearchDialog"
#=require "views/MachineTaskView"
#=require "views/MachineView"
#=require "views/MachineHolder"
#=require "views/ArticleTaskHolder"
#=require "views/ArticleTaskView"
#=require "views/ArticleSearchDialog"
#=require "views/behaviors/ConditionalClassNames"
#=require "views/behaviors/ConditionalElements"
#=require "views/behaviors/Editable"
#=require "views/behaviors/NameDataAttribute"
#=require "views/support/TemplateHelpers"

App.TaskView = Marionette.LayoutView.extend
  ###
  Displays a task on an assignment page.
  ###

  #
  # Backbone.View options
  #

  className: 'pms-task'

  events:
    'click .pms-task-attachments-btn': '_showAttachmentsDialog'
    'click .pms-task-attachments-pictures-btn': '_showAttachmentsPictures'
    'click .pms-task-check-btn': '_toggleTaskCompletionState'
    'click .pms-task-delete-btn': '_deleteTask'
    'click .pms-task-assign-btn': '_showAssignDialog'
    'click .pms-task-completion-confirm-btn': '_completeTask'
    'click .pms-task-completion-cancel-btn': '_cancel'
    'click .pms-task-add-article-btn': '_showAddArticleDialog'
    'click .pms-task-add-machine-btn': '_showAddMachineDialog'
    'click .pms-task-quality-btn': '_updateCompletionQuality'


  #
  # Marionette.View options
  #

  ui:
    attachmentsCounter: '.pms-task-num-attachments'
    picturesCounter: '.pms-task-num-pictures'
    picturesButton: '.pms-task-attachments-pictures-btn'
    checkButton: '.pms-task-check-btn'
    moveButton: '.pms-task-move-btn'
    addUtilityButton: '.pms-task-add-utility-btn'
    qualityButton: '.pms-task-quality-btn'

  behaviors:
    ConditionalClassNames:
      'pms-completed': 'completed'
      'pms-completed-bad': -> @model.get('completion_quality') == 2
      'pms-completable': 'completable'
      'pms-future': -> !@model.get('completed') && !@model.get('completable')
      'pms-movable': 'movable'
    ConditionalElements:
      '.pms-task-tag-pause': -> @model.get('name').match?(/(^|\s)pause($|\s)/i)
    Editable:
      name:
        type: 'string'
        guard: 'editable'
        target: '.pms-task-name'
        inputClass: 'form-control pms-task-name-input'
      instruction:
        type: 'text'
        guard: 'editable'
        target: '.pms-task-instruction'
        inputClass: 'form-control pms-task-instruction-input'
      note:
        type: 'text'
        guard: 'annotatable'
        target: '.pms-task-note'
        inputClass: 'form-control pms-task-note-input'
    NameDataAttribute: {}

  templateHelpers:
    valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
    prettyUser: App.TemplateHelpers.prettyUser
    prettyOperatorGroup: App.TemplateHelpers.prettyOperatorGroup
    prettyDateTime: App.TemplateHelpers.prettyDateTime
    promptAddMachineOrMaterial: ->
        _.template("""
          <div class='pms-task-add-machine-or-material'>
            <div>
              <!-- Uncommend if material gets officially supported 
              <button class='btn btn-sm btn-default pms-task-add-article-btn'>
                <i class='fas fa-cube' title='Material'></i> Material
              </button>
              -->
              <button class='btn btn-sm btn-default pms-task-add-machine-btn'>
                <i class='fas fa-cog' title='Maschine'></i> Maschine
              </button>
            </div>
          </div>
        """)(this)
    promptTaskCompletion: ->
        _.template("""
          <div class='pms-task-completion-prompt'>
            Soll diese Aufgabe, zu der Sie nicht zugewiesen sind, wirklich abgeschlossen werden?
            <div style='margin-top: 5px;'>
              <button class='btn btn-sm btn-default pms-task-completion-cancel-btn'>
                Nein
              </button>
              <button class='btn btn-sm btn-default pms-task-completion-confirm-btn'>
                Ja
              </button>
            </div>
          </div>
        """)(this)
    promptBadQualityConfirmation: ->
        _.template("""
          <div class='pms-task-bad-quality-confirmation'>
            Soll diese Aufgabe wirklich als schlecht bewertet werden? Dieser Schritt kann nicht rückgängig gemacht werden.
            <div style='margin-top: 5px;'>
              <button class='btn btn-sm btn-default pms-task-quality-cancel-btn'>
                Nein
              </button>
              <button class='btn btn-sm btn-default pms-task-quality-confirm-btn'>
                Ja
              </button>
            </div>
          </div>
        """)(this)
    promptChangeQuality: ->
      _.template("""
        <div class='pms-task-change-quality'>
          <div style='margin-top: 5px;'>
            <button class='btn btn-sm btn-default pms-task-quality-btn bad-quality'>
              <i class='fas fa-thumbs-down'></i> Schlecht
            </button>
          </div>
        </div>
      """)(this)

  #
  # Marionette.ItemView options
  #

  template: JST['TaskView']

  modelEvents:
    'change:completed' : 'render'
    'change:operator' : 'render'
    'change:completable': 'render'
    'change:completion_undoable': 'render'
    'change:completion_quality': 'render'
    'change:completion_quality_changeable': 'render'

  #
  # Marionette.CollectionView options
  #

  # childView: App.MachineView
  # childViewOptions: (model, index) ->
  #   return {editable: (@model.get('editable') || (@model.get('reassignable') && App.user?.isAssignedTo(@model)))}
  # childViewContainer: '.pms-machines'


  #
  # Marionette.Layout options
  #

  regions:
    article_tasks: '.pms-articles'
    machines: '.pms-machines'

  #
  # Methods
  #

  onDomRefresh: ->
    ###
    Override of Marionette.View#onDomRefresh.
    ###
    @article_tasks.show(new App.ArticleTaskHolder(model: @model))
    @machines.show(new App.MachineHolder(model: @model))

    # hide machine field if empty
    if (@model.get('machines').length > 0)
      @$el.find('.pms-task-machines-holder').show()
    else
      @$el.find('.pms-task-machines-holder').hide()
    # hide article field if empty
    if (@model.get('article_tasks').length > 0)
      @$el.find('.pms-task-articles-holder').show()
    else
      @$el.find('.pms-task-articles-holder').hide()


  initialize: ->
    @listenTo @model.get('attachments'), 'add', @_adjustAttachmentsCounter
    @listenTo @model.get('attachments'), 'remove', @_adjustAttachmentsCounter
    @listenTo @model.get('article_tasks'), 'add', @_toggleArticle
    @listenTo @model.get('article_tasks'), 'remove', @_toggleArticle
    @listenTo @model.get('machines'), 'add', @_toggleMachine
    @listenTo @model.get('machines'), 'remove', @_toggleMachine

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###

    _.extend @model.toJSON(),
      assignedToUser: App.user?.isAssignedTo(@model)
      operator: @model.get('operator')?.toJSON() || null
      completed_by: @model.get('completed_by')?.toJSON() || null
      assignmentState: @model.get('process').get('run').get('state')
      pmsID: @model.get('process').get('run').get('assignment').id

  onRender: ->
    ###
    Override of Marionette.ItemView#onRender.
    ###
    @ui.checkButton.popover()
    @ui.addUtilityButton.popover()
    @_adjustAttachmentsCounter()
    @_setCheckButtonState()
    # Make the associated task's ID available through the DOM. This is
    # used by the task drag-and-drop code in ProcessView.
    @$el.attr('data-pms-id', @model.id)
    @ui.qualityButton.popover()
    @_setCompletionQualityButtonState()

  #
  # Event Handlers
  #

  _showAttachmentsDialog: ->
    dialog = new App.AttachmentsDialog
      model: @model
      title: @model.get('name')
    dialog.show()

  _showAttachmentsPictures: ->
    dialog = new App.AttachmentsPictureView
      model: @model
      title: @model.get('name')
    dialog.show()

  _adjustAttachmentsCounter: ->
    numAttachments = @model.get('attachments').length
    pictures = @model.get('attachments').filter (attachment) -> attachment.get('content_type').match(/^image\/(?!tiff)/)
    numPictures = if pictures? then pictures.length else 0
    counter = @ui.attachmentsCounter
    counter.text(numAttachments)
    picturesCounter = @ui.picturesCounter
    picturesCounter.text(numPictures)
    picturesButton = @ui.picturesButton
    # Setting the "nonzero" CSS class on the attachments counter lets
    # us highlight tasks with attachments.
    if numAttachments > 0
      counter.addClass('nonzero')
    else
      counter.removeClass('nonzero')
    if numPictures > 0
      picturesButton.show()
    else
      picturesButton.hide()

  _setCheckButtonState: ->
    @ui.checkButton.attr('disabled', !@model.get('completable') && !@model.get('completion_undoable'))

  _setCompletionQualityButtonState: ->
    @ui.qualityButton.attr('disabled', !@model.get('completion_quality_changeable'))

  _toggleTaskCompletionState: ->
    if @model.get('completion_undoable')
      @_uncompleteTask()
    else
      if App.user?.isAssignedTo(@model)
        @_completeTask()

  _completeTask: ->
    @model.save('completed', true, patch: true, wait: true).then =>
      @model.updateCompletionUndoable()
      for predecessor in @model.predecessors()
        predecessor.updateCompletionUndoable()

  _uncompleteTask: ->
    @model.save('completed', false, patch: true, wait: true).then =>
      for predecessor in @model.predecessors()
        predecessor.updateCompletionUndoable()

  _deleteTask: ->
    @model.destroy() # also deletes the view

  _showAssignDialog: ->
    assignment = @model.get('process').get('run').get('assignment')
    extern_workgroup_ids = assignment.get('extern_workgroups').pluck('id')
    new App.OperatorSearchDialog().show (operator) =>
      if operator.get('type') == "user" && operator.get('extern') &&
         operator.get('workgroup_id') not in extern_workgroup_ids
          confirm "Benutzer kann nicht zugewiesen werden, da der Auftrag nicht für ihn verfügbar ist. (siehe Auftragsdetails - Extern verfügbar)"
      else
        operatorSpec = {id: operator.id, type: operator.get('type')}
        @model.save(
          task: {operator: operatorSpec},
          {patch: true, wait: true})

  _cancel: ->
    @ui.checkButton.popover('hide')

  _addMaterial: ->
    @ui.addUtilityButton.popover('hide')

  _addMachine: ->
    @ui.addUtilityButton.popover('hide')

  _showAddMachineDialog: ->
    @ui.addUtilityButton.popover('hide')
    new App.MachineSearchDialog().show (machine) =>
        @model.save(
          task: {machine: machine.id},
          {patch: true, wait: true})

  _showAddArticleDialog: ->
    @ui.addUtilityButton.popover('hide')
    new App.ArticleSearchDialog().show (article) =>
        @model.save(
          task: {article: article.id},
          {patch: true, wait: true})

  _toggleArticle: ->
    # hide article field if empty
    if (@model.get('article_tasks').length > 0)
      @$el.find('.pms-task-articles-holder').show()
    else
      @$el.find('.pms-task-articles-holder').hide()

  _toggleMachine: ->
    # hide machine field if empty
    if (@model.get('machines').length > 0)
      @$el.find('.pms-task-machines-holder').show()
    else
      @$el.find('.pms-task-machines-holder').hide()

  _updateCompletionQuality: (e) ->
    e.preventDefault()
    if $(e.currentTarget).hasClass('bad-quality')
      confirmationHtml = @templateHelpers.promptBadQualityConfirmation()
      # Initialize and show the popover
      $(e.currentTarget).popover({
        content: confirmationHtml,
        html: true,
        placement: 'bottom'
      }).popover('show')
      $('.pms-task-quality-confirm-btn').one 'click', =>
        quality = 2
        @_saveCompletionQuality(quality)
      $('.pms-task-quality-cancel-btn').one 'click', =>
        $(e.currentTarget).popover('hide')
    else
      return

  _saveCompletionQuality: (quality) ->
    @model.save( task: {completion_quality: quality}, {patch: true, wait: true})
