App.SearchDialogItem = Marionette.ItemView.extend
  ###
  Superclass for a result item displayed in a SearchDialog subclass.
  ###

  #
  # Backbone.View options
  #

  tagName: 'li'
  events:
    'keydown a': '_triggerUpOrDown'

  #
  # Marionette.View options
  #

  triggers:
    'click a': 'select'

  #
  # Methods
  #

  setHighlighted: (highlighted) ->
    ###
    Sets whether the item should be displayed in a highlighted state.
    ###
    if highlighted
      @$el.addClass('pms-highlighted')
    else
      @$el.removeClass('pms-highlighted')

  focus: ->
    ###
    Move keyboard focus to the item.
    ###
    @$('a').focus()

  #
  # Event Handlers
  #

  _triggerUpOrDown: (event) ->
    # These events are used by SearchDialog for keyboard navigation.
    if event.keyCode in [0x26, 0x28]
      event.preventDefault()
      @trigger('up') if event.keyCode == 0x26
      @trigger('down') if event.keyCode == 0x28
