#=require "models/Model"
#=require "models/Machine"
#=require "models/TaskTemplate"

App.MachineTaskTemplate = App.Model.extend
  ###
  Client-side representation of MachineTask.
  ###

  #
  # Backbone.Model options
  #

  #
  # Backbone.RelationalModel options
  #

  ###
  relations: [
    type: Backbone.HasOne
    key: 'machine'
    keySource: 'machine_id'
    includeInJSON: 'id',
    relatedModel: App.Machine
  ,
    type: Backbone.HasOne
    key: 'task'
    keySource: 'task_id'
    includeInJSON: 'id',
    relatedModel: App.Task
  ]
  ###

  #
  # App.Model options
  #

  jsonSingularRoot: 'machine_task_template'
  jsonPluralRoot: 'machine_task_templates'
