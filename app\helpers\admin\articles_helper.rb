module Admin::<PERSON><PERSON><PERSON><PERSON>
  def article_pretty_state(state)
    case state
    when "filled"
      "Vorr<PERSON>ig" 
    when "low"
      "Niedrig"
    when "empty"
      "Leer"
    else
      "Unbekannt"
    end
  end

  def article_state_icon(state)
    case state
    when "filled"
      "<i class='fas fa-circle pms-smaller-icon pms-state-green-icon' title='#{pretty_state(state)}'></i>".html_safe
    when "low"
      "<i class='fas fa-circle pms-smaller-icon pms-state-yellow-icon' title='#{pretty_state(state)}'></i>".html_safe
    when "empty"
      "<i class='fas fa-circle pms-smaller-icon pms-state-red-icon' title='#{pretty_state(state)}'></i>".html_safe
    else
      "<i class='fas fa-circle pms-smaller-icon pms-state-gray-icon' title='#{pretty_state("")}'></i>".html_safe
    end
  end

  def pretty_stock(amount, unit)
    if unit.present?
      if amount > 0
        "#{amount} #{unit.name}"
      else
        "0 #{unit.name}"
      end
    else
      if amount > 0
        "#{amount}"
      else
        "0"
      end
    end
  end
end
