#=require "collections/Collection"
#=require "models/Article"

App.ArticleSearchResultCollection = App.Collection.extend
  ###
  Collection class for ArticleSearchDialog search results.
  ###

  url: "/api/search/articles"
  jsonRoot: "search_results"

  # model: (attrs) ->
  #   switch attrs.type
  #     when "user"
  #       App.User.findOrCreate(attrs, parse: true)
  #     when "operator_group"
  #       App.OperatorGroup.findOrCreate(attrs, parse: true)

  # modelId: (attrs) ->
  #   "#{attrs.type}:#{attrs.id}"
