#=require "views/UserSelectorDialog"
#=require "views/WorkgroupSelectorDialog"
#=require "views/behaviors/Editable"
#=require "views/support/BoxLabelService"
#=require "views/support/TemplateHelpers"
#=require "views/support/PdfKit"
#=require "views/support/CsvExport"

# JS to enable delayed plugin startup, with IE browser switch
`if (window.navigator.userAgent.indexOf("Trident") !== -1) { var dymoprint = 1; } else { var dymoprint = 0;
var url = new URL(window.location.href);
if (url.searchParams.get("dymoprint") == 1) { dymoprint = 1; } }`

App.AssignmentHeader = Marionette.ItemView.extend
  ###
  The top area of an an assignment page which displays general
  attributes of the assignment, along with the possibility to edit
  these attributes (if the user has permission to do so).
  ###

  #
  # Backbone.View options
  #

  className: 'pms-assignment-header'

  events:
    'click .pms-add-planner-btn': '_addPlanner'
    'click .pms-add-observer-btn': '_addObserver'
    'click .pms-add-extern-workgroup-btn': '_addExternWorkgroup'
    'click .pms-remove-planner-btn': '_removePlanner'
    'click .pms-remove-observer-btn': '_removeObserver'
    'click .pms-remove-extern-workgroup-btn': '_removeExternWorkgroup'
    'click .pms-raise-priority-btn': '_raisePriority'
    'click .pms-reduce-priority-btn': '_reducePriority'
    'click .pms-raise-count-btn': '_raiseCount'
    'click .pms-reduce-count-btn': '_reduceCount'
    'click .pms-assignment-activate-btn': '_activateAssignment'
    'click .pms-cancel-assignment-btn': '_cancelAssignment'
    'click .pms-cancel-run-btn': '_cancelRun'
    'click .pms-repeat-run-btn': '_repeatRun' 
    'click .pms-assignment-copy-btn': '_copyAssignment'
    'click .pms-assignment-create-template-btn': '_createAssignmentTemplate'
    'click .pms-assignment-copy-datapath-btn': '_copyAssignmentDatapath'
    'click .pms-assignment-confirm-print-label-btn': '_printLabel'
    'click .pms-assignment-confirm-delete-btn': '_deleteAssignment'
    'click .pms-assignment-pause-btn': '_pauseAssignment'
    'click .pms-assignment-to-pdf-intern-btn': '_convertAssigmentToPDFIntern'
    'click .pms-assignment-to-pdf-extern-btn': '_convertAssigmentToPDFExtern'
    'click .pms-assignment-to-csv': '_convertAssigmentToCSV'
    'show.bs.collapse .pms-assignment-details': '_onDetailsExpand'
    'hide.bs.collapse .pms-process-body': '_onDetailsCollapse'

  #
  # Marionette.View options
  #

  behaviors:
    Editable:
      name:
        type: 'string'
        guard: 'editable'
        target: '.pms-assignment-name'
        inputClass: 'form-control pms-assignment-name-input'
      project:
        type: 'string'
        guard: 'editable'
        target: '.pms-assignment-project'
        inputClass: 'form-control pms-assignment-project-input'
      datapath:
        type: 'string'
        guard: 'editable'
        target: '.pms-assignment-datapath'
        inputClass: 'form-control pms-assignment-datapath-input'
      description:
        type: 'text'
        guard: 'editable'
        target: '.pms-assignment-description'
        inputClass: 'form-control pms-assignment-description-input'
      due:
        type: 'date'
        guard: 'editable'
        dateFormat: 'dddd, DD.MM.YYYY'
        target: '.pms-assignment-due'
        inputClass: 'pms-date-input pms-assignment-due-input'
      board_count:
        type: 'number'
        guard: 'editable'
        target: '.pms-assignment-board-count-field'
        inputClass: 'pms-assignment-board-count-input'

  templateHelpers:
    valueOrPlaceholder: App.TemplateHelpers.valueOrPlaceholder
    formatText: App.TemplateHelpers.formatText
    prettyDate: App.TemplateHelpers.prettyDate
    prettyState: App.TemplateHelpers.prettyState
    stateLabelClasses: App.TemplateHelpers.stateLabelClasses
    deleteConfirmationButton: App.TemplateHelpers.deleteConfirmationButton

    statePopoverContent: ->
      ###
      Return the content shown when clicking on the state indicator
      as planner.
      ###
      _.template("""
        <div class='btn-group'>
        <% if (state == 'active') { %>
          <button
            class='btn btn-danger has-tooltip pms-cancel-btn'
            data-toggle='modal'
            data-target='#pms-assignment-cancel-dialog'
            title='Durchlauf abbrechen oder Auftrag beenden'>
            <i class='fas fa-times'></i>
            <b>Abbrechen</b>
          </button>
          <% if (editable) { %>
            <button
              class='btn btn-warning has-tooltip pms-assignment-pause-btn'
              data-toggle='tooltip'
              data-placement='bottom'
              title='Aktuellen Durchlauf für Bearbeitende vorübergehend deaktivieren'>
              <i class='fas fa-pause'></i>
              <b>Pausieren</b>
            </button>
          <% } %>
        <% } else if (state == 'draft' || state == 'paused') { %>
          <button
            class='btn btn-primary has-tooltip pms-assignment-activate-btn'
            data-toggle='tooltip'
            data-placement='bottom'
            title='Aktuellen Durchlauf für Bearbeitende aktivieren'>
            <i class='fas fa-play'></i>
            <b>Aktivieren</b>
          </button>
          <% if (state == 'paused') { %>
            <button
              class='btn btn-danger has-tooltip pms-assignment-cancel-btn'
              data-toggle='modal'
              data-target='#pms-assignment-cancel-dialog'
              title='Durchlauf abbrechen oder Auftrag beenden'>
              <i class='fas fa-times'></i>
              <b>Abbrechen</b>
            </button>
          <% } %>
        <% } else if (state == 'completed') { %>
          <button
            class='btn btn-primary has-tooltip pms-repeat-run-btn'
            data-toggle='tooltip'
            data-placement='bottom'
            title='Durchlauf als Entwurf wiederholen'>
            <i class='fas fa-redo'></i>
            <b>Wiederholen</b>
          </button>
        <% } %>
        </div>
        <script>$('.has-tooltip').tooltip()</script>
      """)(this)

    printLabelPopoverContent: (user) ->
      _.template("""
          <div class='form-group'>
            <label for='pms-label-printer-select'>Drucker</label>
            <select id='pms-label-printer-select' class='form-control'>
              <% _.each(labelPrinters, function(printer) { %>
                <option><%= printer %></option>
              <% }) %>
            </select>
          </div>
          <button class='btn btn-primary pms-assignment-confirm-print-label-btn'>
            <i class='fas fa-print'></i>
            Drucken
          </button>
      """)(this)

    plannerListItem: (user) ->
      ###
      Generate HTML for an item for `user` in the displayed planner
      list.
      ###
      _.template("""
        <li data-pms-user-id="<%= id %>">
          <% if (id == App.user.id) { %>
            <i class="fas fa-user"></i>
            <span <% if (created_by_id == id) { %> class="pms-creator" <% } %>>
              <%- first_name %> <%- last_name %>
              <b>(Sie)</b>
            </span>
          <% } else { %>
              <a href="mailto:<%= email %>?subject=<%= subject %>"
                  <% if (created_by_id == id) { %> class="pms-creator" <% } %>>
                <i class="fas fa-user"></i>
                <%- name %>
              </a>
            <% if (editable) { %>
              <button class="btn btn-xs btn-default pms-remove-planner-btn">
                <i class="fas fa-minus" title="Planer:in entfernen"></i>
              </button>
            <% } %>
          <% } %>
         </li>
      """)(_.extend(user,
                    created_by_id: @created_by_id,
                    editable: @editable,
                    subject: @mailSubject))

    observerListItem: (user) ->
      ###
      Generate HTML for an item for `user` in the displayed observer
      list.
      ###
      _.template("""
        <li data-pms-user-id="<%= id %>">
          <% if (id == App.user.id) { %>
            <i class="fas fa-user"></i>
            <span <% if (created_by_id == id) { %> class="pms-creator" <% } %>>
              <%- first_name %> <%- last_name %>
              <b>(Sie)</b>
            </span>
          <% } else { %>
              <a href="mailto:<%= email %>?subject=<%= subject %>"
                  <% if (created_by_id == id) { %> class="pms-creator" <% } %>>
                <i class="fas fa-user"></i>
                <%- name %>
              </a>
            <% if (editable) { %>
              <button class="btn btn-xs btn-default pms-remove-observer-btn">
                <i class="fas fa-minus" title="Beobachter:in entfernen"></i>
              </button>
            <% } %>
          <% } %>
         </li>
      """)(_.extend(user,
                    created_by_id: @created_by_id,
                    editable: @editable,
                    subject: @mailSubject))        

  #
  # Marionette.ItemView options
  #

  template: JST['AssignmentHeader']

  ui:
    deleteButton: '.pms-assignment-delete-btn'
    printLabelButton: '.pms-assignment-print-label-btn'
    overdueIcon: '.pms-overdue-icon'
    overdueHeaderIcon: '.pms-header-overdue-icon'

  modelEvents:
    'change:state': 'render'
    'change:due' : '_updateOverdueIcons'
    'change:project': '_updatePageHeading'
    'change:name': '_updatePageHeading'
    'change:priority': 'render'
    'change:board_count': 'render'

  #
  # Marionette.CollectionView options
  #

  collectionEvents:
    'change:state' : 'render'

  #
  # Methods
  #

  initialize: ->
    ###
    Override of Backbone.View#initialize.
    ###
    @collection = @model.get('runs')
    @_detailsExpanded = false
    @listenTo(@model.get('planners'), 'add', @render)
    @listenTo(@model.get('planners'), 'remove', @render)
    @listenTo(@model.get('extern_workgroups'), 'add', @render)
    @listenTo(@model.get('extern_workgroups'), 'remove', @render)
    @listenTo(@model.get('observers'), 'add', @render)
    @listenTo(@model.get('observers'), 'remove', @render)
    @listenTo(@model, 'change:datapath', @render)

  serializeData: ->
    ###
    Override of Marionette.ItemView#serializeData.
    ###
    _.extend @model.toJSON(),
      editable: @model.get('editable') &&
                (@model.get("highest_prio_editable") ||
                @model.get('priority') < 5)
      detailsExpanded: @_detailsExpanded
      overdue: @model.isOverdue()
      planners: @model.get('planners').toJSON()
      externWorkgroups: @model.get('extern_workgroups').toJSON()
      observers: @model.get('observers').toJSON()
      members: @model.get('members').toJSON()
      completed_at: @model.get('runs').last().get('completed_at')
      memberEmails: @_emailsOfOtherMembers().join(';%20')
      plannerEmails: @_emailsOfPlanners().join(';%20')
      observerEmails: @_emailsOfObservers().join(';%20')
      datapath: @model.get('datapath')
      board_count: @model.get('board_count')
      mailSubject: "[" + @model.get("project") + "] " + @model.get("name") +
                    " (%23" + @model.id + ")"
      if (dymoprint == 1)
        canPrintLabels: App.BoxLabelService.isAvailable()
        labelPrinters: App.BoxLabelService.listPrinters()
      else
        canPrintLabels: 0
        labelPrinters: 0

  _emailsOfOtherMembers: ->
    memberEmails = @model.get('members').pluck('email')
    operatorGroupEmails = _.flatten(@model.get('operator_groups').pluck('emails'))
    allMemberEmails = _.union(memberEmails, operatorGroupEmails)
    allMemberEmails.filter((email) -> email != App.user.get('email'))

  _emailsOfPlanners: ->
    memberEmails = @model.get('planners').pluck('email')
    memberEmails.filter((email) -> email != App.user.get('email'))

  _emailsOfObservers: ->
    memberEmails = @model.get('observers').pluck('email')
    memberEmails.filter((email) -> email != App.user.get('email'))

  onRender: ->
    @ui.deleteButton.popover()
    @ui.printLabelButton.popover()

    state = @model.get('state')
    if state != 'failed'
      @$('.pms-assignment-state-label').popover(trigger: 'focus')
    @$('.pms-assignment-to-pdf-btn').popover(trigger: 'focus')
    @_updateOverdueIcons()

  #
  # Event Handlers
  #

  _updatePageHeading: ->
    name = _.escape(@model.get('name'))
    project = _.escape(@model.get('project'))
    $(".pms-heading-title").html("[#{project}] #{name}")

  _activateAssignment: (event) ->
    @model.get('runs').last().save('state', 'active', patch: true)

  _addPlanner: ->
    new App.UserSelectorDialog().show (user) =>
      @model.save('add_planner', user.id, patch: true)

  _addObserver: (event) ->
    new App.UserSelectorDialog().show (user) =>
      # Check if the user is already a member
      if @model.get('members').findWhere(id: user.id)
        # Show notification if the user is already a member
        App.vent.trigger("warning", 
          title: "Achtung", 
          description: "Dieser Benutzer ist bereits ein Mitglied")
        setTimeout (=> App.vent.trigger("hide")), 1500
      else
        # Proceed to add the user as a observer
        @model.save('add_observer', user.id, patch: true)

  _addExternWorkgroup: (event) ->
    new App.WorkgroupSelectorDialog().show (workgroup) =>
      @model.save('add_extern_workgroup', workgroup.id, patch: true)

  _cancelAssignment: (event) ->
    reason = $('#pms-assignment-cancel-reason').val()
    if (reason)
      new_description = @model.get('description') +
                        "\nGrund für Abbruch des Auftrags: " +
                        reason
      @model.save('description', new_description, patch: true)
    @model.save(state: 'failed', {patch: true})

  _cancelRun: (event) ->
    reason = $('#pms-assignment-cancel-reason').val()
    run = @model.get('runs').last().get('version')
    if (reason)
      new_description = @model.get('description') +
                        "\nGrund für Abbruch des " + run + ". Durchlaufs: " +
                        reason
      @model.save('description', new_description, patch: true)
    @model.get('runs').last().save('state', 'failed', patch: true)

  _repeatRun: (event) ->
    @model.get('runs').last().save('repeat_run', true, patch: true)

  _copyAssignment: ->
    newAssignment = new App.Assignment()
    newAssignment.save(copy_of: @model.id).done =>
      document.location = "/auftrag/#{newAssignment.id}"

  _createAssignmentTemplate: ->
    newAssignment = new App.Assignment()
    newAssignment.save(copy_of: @model.id, template: true).done =>
      document.location = "/auftrag/#{newAssignment.id}"

  _copyAssignmentDatapath: ->
    # Select the datapath div
    datapathDiv = @$('.pms-assignment-datapath')

    # Get the text content of the datapath div
    datapathContent = datapathDiv.text().trim()
    
    # Use the Clipboard API to copy the content
    if navigator.clipboard?
      navigator.clipboard.writeText(datapathContent).then ->
        App.vent.trigger("info", 
          title: "Hinweis", 
          description: "Dateipfad in Zwischenablage kopiert")
        setTimeout (=> App.vent.trigger("hide")), 1500
      .catch (err) ->
        App.vent.trigger("error", 
          title: "Kopieren fehlgeschlagen", 
          description: "Fehler beim Kopieren des Datepfades: " + err)
    else
      App.vent.trigger("warning", 
          title: "Achtung", 
          description: "Funktion nicht verfügbar")
      setTimeout (=> App.vent.trigger("hide")), 1500

  _deleteAssignment: ->
    @model.destroy().done ->
      document.location = "/"

  _onDetailsCollapse: ->
    @_detailsExpanded = false
    true # don't prevent event bubbling

  _onDetailsExpand: ->
    @_detailsExpanded = true

  _pauseAssignment: ->
    @model.get('runs').last().save('state', 'paused', patch: true)

  _printLabel: (event)->
    printer = @$('#pms-label-printer-select').val()
    App.BoxLabelService.print(@model, printer)
    @ui.printLabelButton.popover('hide')

  _convertAssigmentToPDFIntern: ->
    createPDF(@model, @_getUserName(), true)

  _convertAssigmentToPDFExtern: ->
    createPDF(@model, @_getUserName(), false)

  _convertAssigmentToCSV: ->
    createCSV(@model, @_getUserName())

  _updateOverdueIcons: ->
    if @model.isOverdue()
      @ui.overdueIcon.show()
      @ui.overdueHeaderIcon.show()
    else
      @ui.overdueIcon.hide()
      @ui.overdueHeaderIcon.hide()

  _removePlanner: (event) ->
    plannerItem = $(event.target).closest('li')
    userId = plannerItem.attr('data-pms-user-id')
    @model.save('remove_planner', userId, patch: true)

  _removeObserver: (event) ->
    observerItem = $(event.target).closest('li')
    userId = observerItem.attr('data-pms-user-id')
    @model.save('remove_observer', userId, patch: true)

  _removeExternWorkgroup: (event) ->
    workgroupItem = $(event.target).closest('li')
    workgroupId = workgroupItem.attr('data-pms-workgroup-id')
    @model.save('remove_extern_workgroup', workgroupId, patch: true)

  _raisePriority: ->
    priority = @model.get('priority')
    @model.save('priority', priority + 1, patch: true)

  _reducePriority: ->
    priority = @model.get('priority')
    @model.save('priority', priority - 1, patch: true)

  _raiseCount: ->
    count = @model.get('board_count')
    @model.save('board_count', count + 1, patch: true)

  _reduceCount: ->
    count = @model.get('board_count')
    count = if (count < 1) then 1 else count
    @model.save('board_count', count - 1, patch: true)

  _getUserName: ->
    App.user.get('first_name') + " " + App.user.get('last_name')
