#=require "views/behaviors/NameDataAttribute"

App.ProcessHandle = Marionette.ItemView.extend
  ###
  An overlay <div> for a process within a ProcessGraphView. It
  implements the interactivity of a "process dot" on the graph.
  ###

  #
  # Constants
  #

  DRAG_TYPE: 'applicaton/vnd.pms-process-id'
  HIGHLIGHT_COLOR: '#F0AD4E' # yellow

  #
  # Backbone.View options
  #

  className: 'pms-process-handle'

  events:
    dragstart: '_onDrag'
    dragenter: '_onDragEnter'
    dragover: '_onDragOver'
    dragleave: '_onDragLeave'
    dragend: '_unhighlightDragging'
    drop: '_onDrop'
    click: '_onClick'

  #
  # Marionette.View options
  #

  behaviors:
    NameDataAttribute: {}

  #
  # Marionette.ItemView options
  #

  # We don't need a template
  template: -> ""

  #
  # Methods
  #

  initialize: (options) ->
    @_position(options)
    @$el.attr('id', @model.id)
    @$el.attr('data-pms-name', @model.get('name'))
    @child_movable = @model.get('children').any (c) -> (c.get('editable'))
    @parent_movable = @model.get('parents').any (p) -> (p.get('editable'))
    if @model.get('child_creatable')
      @$el.attr('draggable', true)
      @$el.popover(
        placement: 'right',
        html: true,
        content: _.template(
          '<div class="pms-popover-insert-actions">
            <div>
              <button class="btn btn-sm btn-default pms-move-process-above-btn"
                id="<%= id %>" value="moveAbove" <% if (!parent_movable || no_parent) { %> disabled <% } %>>
                <i class="fas fa-caret-up" title="Prozess nach oben verschieben"></i>
              </button>
              <button class="btn btn-sm btn-default pms-remove-connection-before-btn"
                id="<%= id %>" value="removeBefore" title="Verbindung darüber trennen"
                <% if (!parent_movable) { %> disabled <% } %>>
                <i class="fas fa-cut"></i>
              </button>
              <button class="btn btn-sm btn-default pms-add-process-before-btn"
                id="<%= id %>" value="addBefore" title="Vorgängerprozess hinzufügen"
                <% if (!movable) { %> disabled <% } %>>
                <div class="pms-add-process-icon"><i class="fas fa-caret-up"></i></div>
                <i class="fas fa-ellipsis-v"></i>
              </button>
            </div>
            <div>
              <i class="fas fa-bars pms-process-icon"></i>
              <button class="btn btn-sm btn-default pms-move-process-beside-btn"
                id="<%= id %>" value="moveBeside" title="Prozess aus der Reihe lösen"
                <% if (!movable || (no_parent && no_child)) { %> disabled <% } %>>
                <i class="fas fa-caret-right"></i>
              </button>
              <button class="btn btn-sm btn-default pms-add-process-beside-btn"
                id="<%= id %>" value="addBeside" title="Parallelen Prozess hinzufügen">
                <i class="fas fa-ellipsis-h"></i>
                <i class="fas fa-caret-right"></i>
              </button>
            </div>
            <div>
              <button class="btn btn-sm btn-default pms-move-process-below-btn"
                id="<%= id %>" value="moveBelow" title="Prozess nach unten verschieben"
                <% if (!movable || no_child) { %> disabled <% } %>>
                <i class="fas fa-caret-down"></i>
              </button>
              <button class="btn btn-sm btn-default pms-remove-connection-after-btn"
                id="<%= id %>" value="removeAfter" title="Verbindung darunter trennen"
                <% if (!child_movable) { %> disabled <% } %>>
                <i class="fas fa-cut"></i>
              </button>
              <button class="btn btn-sm btn-default pms-add-process-below-btn"
                id="<%= id %>" value="addBelow" title="Nachfolgerprozess hinzufügen"
                <% if (!child_addable) { %> disabled <% } %>>
                <div class="pms-add-process-icon"><i class="fas fa-ellipsis-v"></i></div>
                <i class="fas fa-caret-down"></i>
              </button>
            </div>
          </div>'
        )(_.extend(@model, movable: @model.get('editable'),
          no_child: @model.get('children').isEmpty(),
          no_parent: @model.get('parents').isEmpty(),
          child_addable: @child_movable || @model.get('children').isEmpty(),
          child_movable: @child_movable,
          parent_movable: @parent_movable || false ))
      )

  _position: (options) ->
    @$el.css('position', 'absolute')
    @$el.css('left', options.x - options.radius)
    @$el.css('top', options.y - options.radius)
    @$el.css('width', options.radius * 2)
    @$el.css('height', options.radius * 2)
    @$el.css('border-radius', options.radius)

  _highlightDragging: ->
    @$el.css('background-color', @HIGHLIGHT_COLOR)

  _unhighlightDragging: ->
    @$el.css('background-color', 'transparent')

  _highlightDroppable: ->
    @$el.popover('show')
    @$el.css('border', "3px solid #{@HIGHLIGHT_COLOR}")

  _unhighlightDroppable: ->
    @$el.css('border', 'none')
    @$el.popover('hide')

  #
  # Event Handlers
  #

  _onClick: (event) ->
    processHandleList = document.getElementsByClassName("pms-process-handle")
    for p in processHandleList
      if p != @$el[0]
        $(p).popover('hide')

  _onDrag: (event) ->
    event.originalEvent.dataTransfer.setData(@DRAG_TYPE, @model.id)
    @_highlightDragging()

  _onDragEnter: (event) ->
    if _.include(event.originalEvent.dataTransfer.types, @DRAG_TYPE)
      @_highlightDroppable()
      event.preventDefault() # allow drop

  _onDragOver: (event) ->
    if _.include(event.originalEvent.dataTransfer.types, @DRAG_TYPE)
      event.preventDefault() # allow drop

  _onDragLeave: ->
    @_unhighlightDroppable()

  _onDrop: (event) ->
    @_unhighlightDroppable()
    otherId = event.originalEvent.dataTransfer.getData(@DRAG_TYPE)
    other = App.Process.find(otherId)
    if other.get('parents').include(@model)
      @_disconnectProcess(@model, other)
    else if @model.get('parents').include(other)
      @_disconnectProcess(other, @model)
    else
      @_connectProcess(other)

  _connectProcess: (other) ->
    if other.id == @model.id
      return
    else if @model.hasChild(other)
      @_cannotConnect(
        """
        <b><%- name %></b> und <b><%- otherName %></b> würden zyklisch
        voneinander abhängen.
        """,
        name: @model.get('name'),
        otherName: other.get('name'))
    else if other.hasChild(@model)
      @_cannotConnect(
        """
        <b><%- name %></b> hängt schon von <b><%- otherName %></b> ab.
        """,
        name: @model.get('name'),
        otherName: other.get('name'))
    else if @model.get('completed')
      @_cannotConnect(
        """
        <b><%- name %></b> ist schon abgeschlossen und kann keine neue
        Abhängigkeiten mehr bekommen.
        """,
        name: @model.get('name'))
    else
      @model.addParent(other)

  _cannotConnect: (reason, args) ->
    App.vent.trigger 'info',
      title: 'Verbinden dieser Prozesse nicht möglich'
      description: _.template(reason)(args)

  _cannotDisconnect: (reason, args) ->
    App.vent.trigger 'info',
      title: 'Trennen dieser Prozesse nicht möglich'
      description: _.template(reason)(args)

  _disconnectProcess: (parent, child) ->
    if child.get('completed')
      @_cannotDisconnect(
        """
        <b><%- name %></b> ist schon abgeschlossen. Die Abhängigkeit
        kann nicht mehr entfernt werden.
        """,
        name: @model.get('name'))
    else
      child.removeParent(parent)
