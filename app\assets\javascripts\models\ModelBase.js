//
// The base class of all other model classes. It mainly provides the
// fetchAllRelated() method, which, unlike fetchRelated() from
// backbone-relational, fetches related models for all relations at
// once (instead of a single specific one) and works recursively.
//
App.ModelBase = Backbone.RelationalModel.extend({

  // See <http://backbonerelational.org>.
  relations: [],

  // Fetches all models related to this model, and the models related to
  // the fetched models, recursively. A jQuery promise is returned that can
  // be used to do something when all data has been fetched, e.g.:
  //
  //   assignment.fetchAllRelated().then(function() {
  //     var nRuns = assignment.get('runs').length;
  //     console.log("The assignment has " + nRuns + " runs");
  //   });
  fetchAllRelated: function() {
    if (this.relations.length === 0) {
      // If this model has no relations, there is nothing to do. Return
      // an immediately resolved promise in this case so that the caller
      // can instantly continue.
      return $.Deferred().resolve().promise();
    } else {
      return this._doFetchAllRelated();
    }
  },

  _doFetchAllRelated: function() {
    var promises = [];
    var self = this;

    _.chain(this.relations)
       // Ignore automatically generated reverse relations; otherwise,
       // fetchAllRelated() could end up in an infinite loop.
      .filter(function(r) {
        return !r.isAutoRelation;
      })
      // For every model we start to fetch, we store the returned promise
      // so that we can return a combined super-promise from this method.
      // That super-promise will only be resolved when all sub-fetches are
      // done.
      .each(function(r) {
        promises.push(self._fetchRelated(r.key));
      });

    return $.when.apply($, promises);
  },

  _fetchRelated: function(key) {
    var promises = this.fetchRelated(key);
    var self = this;
    return $.when.apply($, promises).then(function() {
      return self._fetchAllRelatedOfRelated(key);
    });
  },

  _fetchAllRelatedOfRelated: function(key) {
    var related = this.get(key);
    if (related instanceof Backbone.Collection) {
      var promises = related.map(function(model) {
        return model.fetchAllRelated();
      });
      return $.when.apply($, promises);
    } else if (related) {
      return related.fetchAllRelated();
    }
  }
});
