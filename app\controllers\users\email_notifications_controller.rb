class Users::EmailNotificationsController < ApplicationController
	layout "layouts/main"
  def edit
  	@user = User.find(current_user.id)
  	# FIX ME find better solution for creating form for OperatorGroup Data
  	@user_groups = OperatorGroup.joins("join operator_groups_users on operator_groups.id = operator_groups_users.operator_group_id")
                                .where(["operator_groups_users.user_id = ?", @user.id])
  	@user_setting_groups = OperatorGroupUser.user_groups(@user.id)
  end

  def update
  	@user = User.find(current_user.id)
    if @user.update(permitted_params)
      OperatorGroupUser.user_groups(@user.id).each do |g|
      	g.email_new_task = params['email_new_task'].include?(g.operator_group_id.to_s)
      	g.email_task_ready = params['email_task_ready'].include?(g.operator_group_id.to_s)
      	g.email_task_finished = params['email_task_finished'].include?(g.operator_group_id.to_s)
      	g.email_task_gone = params['email_task_gone'].include?(g.operator_group_id.to_s)
      	# g.email_day_summery = params['email_day_summery'].include?(g.operator_group_id.to_s)
      	g.save
     end

      flash[:notice] = "Die Email-Einstellungen wurden gespeichert."
      redirect_to action: :edit
    end
  end

  private

  def permitted_params
    params.require(:user).permit( :email_assignment_finished,
                                  :email_assignment_failed,
                                  :email_assignment_new_run,
                                  :email_assignment_reactivated,
                                  :email_assignment_process_finished,
                                  :email_assignment_process_ready,
                                  :email_assignment_task_finished,
                                  :email_assignment_task_ready,
                                  # :email_assignment_delayed,
                                  # :email_assignment_no_work,
                                  :email_assignment_new_planner,
                                  :email_added_as_planner,
                                  :email_assignment_no_operator,
                                  # :email_assignment_day_summery,

                                  :email_worker_new_task,
                                  :email_worker_task_ready,
                                  :email_worker_assignment_failed,
                                  # :email_assignment_attachment_added,
                                  # :email_worker_day_summery,

                                  :email_group_new_task,
                                  :email_new_task,
                                  :email_group_task_ready,
                                  :email_task_ready,
                                  :email_group_task_finished,
                                  :email_task_finished,
                                  :email_group_task_gone,
                                  :email_task_gone,
                                  # :email_group_day_summery,
                                  # :email_day_summery,

                                  :email_assignment_new_observer,
                                  :email_assignment_finished_observer,
                                  :email_assignment_failed_observer,
                                  :email_assignment_new_run_observer,
                                  :email_assignment_reactivated_observer,
                                  # :email_assignment_delayed_observer,
                                  # :email_assignment_no_work_observer,
                                  :email_assignment_new_planner_observer,
                                  :email_assignment_process_finished_observer,
                                  :email_assignment_process_ready_observer,
                                  :email_assignment_task_finished_observer,
                                  :email_assignment_task_ready_observer,
                                  # :email_assignment_attachment_added_observer,
                                  :email_assignment_no_operator_observer,
                                  # :email_assignment_day_summery_observer,
                                  :email_added_as_observer
                                  )
  end
end
